{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyOKrX9gmuhl9+KwscpZKr3u", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/Notebooks/Chap12/12_1_Self_Attention.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# **Notebook 12.1: Self Attention**\n", "\n", "This notebook builds a self-attention mechanism from scratch, as discussed in section 12.2 of the book.\n", "\n", "Work through the cells below, running each cell in turn. In various places you will see the words \"TODO\". Follow the instructions at these places and make predictions about what is going to happen or write code to complete the functions.\n", "\n", "Contact <NAME_EMAIL> if you find any mistakes or have any suggestions.\n", "\n"], "metadata": {"id": "t9vk9Elugvmi"}}, {"cell_type": "code", "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"], "metadata": {"id": "OLComQyvCIJ7"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["The self-attention mechanism maps $N$ inputs $\\mathbf{x}_{n}\\in\\mathbb{R}^{D}$ and returns $N$ outputs $\\mathbf{x}'_{n}\\in \\mathbb{R}^{D}$.  \n", "\n"], "metadata": {"id": "9OJkkoNqCVK2"}}, {"cell_type": "code", "source": ["# Set seed so we get the same random numbers\n", "np.random.seed(3)\n", "# Number of inputs\n", "N = 3\n", "# Number of dimensions of each input\n", "D = 4\n", "# Create an empty list\n", "all_x = []\n", "# Create elements x_n and append to list\n", "for n in range(N):\n", "  all_x.append(np.random.normal(size=(D,1)))\n", "# Print out the list\n", "print(all_x)\n"], "metadata": {"id": "oAygJwLiCSri"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["We'll also need the weights and biases for the keys, queries, and values (equations 12.2 and 12.4)"], "metadata": {"id": "W2iHFbtKMaDp"}}, {"cell_type": "code", "source": ["# Set seed so we get the same random numbers\n", "np.random.seed(0)\n", "\n", "# Choose random values for the parameters\n", "omega_q = np.random.normal(size=(D,D))\n", "omega_k = np.random.normal(size=(D,D))\n", "omega_v = np.random.normal(size=(D,D))\n", "beta_q = np.random.normal(size=(D,1))\n", "beta_k = np.random.normal(size=(D,1))\n", "beta_v = np.random.normal(size=(D,1))"], "metadata": {"id": "79TSK7oLMobe"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Now let's compute the queries, keys, and values for each input"], "metadata": {"id": "VxaKQtP3Ng6R"}}, {"cell_type": "code", "source": ["# Make three lists to store queries, keys, and values\n", "all_queries = []\n", "all_keys = []\n", "all_values = []\n", "# For every input\n", "for x in all_x:\n", "  # TODO -- compute the keys, queries and values.\n", "  # Replace these three lines\n", "  query = np.ones_like(x)\n", "  key = np.ones_like(x)\n", "  value = np.ones_like(x)\n", "\n", "  all_queries.append(query)\n", "  all_keys.append(key)\n", "  all_values.append(value)"], "metadata": {"id": "TwDK2tfdNmw9"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["We'll need a softmax function (equation 12.5) -- here, it will take a list of arbitrary numbers and return a list where the elements are non-negative and sum to one\n"], "metadata": {"id": "Se7DK6PGPSUk"}}, {"cell_type": "code", "source": ["def softmax(items_in):\n", "\n", "  # TODO Compute the elements of items_out\n", "  # Replace this line\n", "  items_out = items_in.copy()\n", "\n", "  return items_out ;"], "metadata": {"id": "u93LIcE5PoiM"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Now compute the self attention values:"], "metadata": {"id": "8aJVhbKDW7lm"}}, {"cell_type": "code", "source": ["# Create emptymlist for output\n", "all_x_prime = []\n", "\n", "# For each output\n", "for n in range(N):\n", "  # Create list for dot products of query N with all keys\n", "  all_km_qn = []\n", "  # Compute the dot products\n", "  for key in all_keys:\n", "    # TODO -- compute the appropriate dot product\n", "    # Replace this line\n", "    dot_product = 1\n", "\n", "    # Store dot product\n", "    all_km_qn.append(dot_product)\n", "\n", "  # Compute dot product\n", "  attention = softmax(all_km_qn)\n", "  # Print result (should be positive sum to one)\n", "  print(\"Attentions for output \", n)\n", "  print(attention)\n", "\n", "  # TODO: Compute a weighted sum of all of the values according to the attention\n", "  # (equation 12.3)\n", "  # Replace this line\n", "  x_prime = np.zeros((D,1))\n", "\n", "  all_x_prime.append(x_prime)\n", "\n", "\n", "# Print out true values to check you have it correct\n", "print(\"x_prime_0_calculated:\", all_x_prime[0].transpose())\n", "print(\"x_prime_0_true: [[ 0.94744244 -0.24348429 -0.91310441 -0.44522983]]\")\n", "print(\"x_prime_1_calculated:\", all_x_prime[1].transpose())\n", "print(\"x_prime_1_true: [[ 1.64201168 -0.08470004  4.02764044  2.18690791]]\")\n", "print(\"x_prime_2_calculated:\", all_x_prime[2].transpose())\n", "print(\"x_prime_2_true: [[ 1.61949281 -0.06641533  3.96863308  2.15858316]]\")\n"], "metadata": {"id": "yimz-5nCW6vQ"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Now let's compute the same thing, but using matrix calculations.  We'll store the $N$ inputs $\\mathbf{x}_{n}\\in\\mathbb{R}^{D}$ in the columns of a $D\\times N$ matrix, using equations 12.6 and 12.7/8.\n", "\n", "Note:  The book uses column vectors (for compatibility with the rest of the text), but in the wider literature it is more normal to store the inputs in the rows of a matrix;  in this case, the computation is the same, but all the matrices are transposed and the operations proceed in the reverse order."], "metadata": {"id": "PJ2vCQ_7C38K"}}, {"cell_type": "code", "source": ["# Define softmax operation that works independently on each column\n", "def softmax_cols(data_in):\n", "  # Exponentiate all of the values\n", "  exp_values = np.exp(data_in) ;\n", "  # Sum over columns\n", "  denom = np.sum(exp_values, axis = 0);\n", "  # Replicate denominator to N rows\n", "  denom = np.matmul(np.ones((data_in.shape[0],1)), denom[np.newaxis,:])\n", "  # Compute softmax\n", "  softmax = exp_values / denom\n", "  # return the answer\n", "  return softmax"], "metadata": {"id": "obaQBdUAMXXv"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [" # Now let's compute self attention in matrix form\n", "def self_attention(X,omega_v, omega_q, omega_k, beta_v, beta_q, beta_k):\n", "\n", "  # TODO -- Write this function\n", "  # 1. Compute queries, keys, and values\n", "  # 2. Compute dot products\n", "  # 3. Apply softmax to calculate attentions\n", "  # 4. Weight values by attentions\n", "  # Replace this line\n", "  X_prime = np.zeros_like(X);\n", "\n", "\n", "  return X_prime"], "metadata": {"id": "gb2WvQ3SiH8r"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Copy data into matrix\n", "X = np.zeros((D, N))\n", "X[:,0] = np.squeeze(all_x[0])\n", "X[:,1] = np.squeeze(all_x[1])\n", "X[:,2] = np.squeeze(all_x[2])\n", "\n", "# Run the self attention mechanism\n", "X_prime = self_attention(X,omega_v, omega_q, omega_k, beta_v, beta_q, beta_k)\n", "\n", "# Print out the results\n", "print(X_prime)"], "metadata": {"id": "MUOJbgJskUpl"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["If you did this correctly, the values should be the same as above.\n", "\n", "TODO:  \n", "\n", "Print out the attention matrix\n", "You will see that the values are quite extreme (one is very close to one and the others are very close to zero.  Now we'll fix this problem by using scaled dot-product attention."], "metadata": {"id": "as_lRKQFpvz0"}}, {"cell_type": "code", "source": ["# Now let's compute self attention in matrix form\n", "def scaled_dot_product_self_attention(X,omega_v, omega_q, omega_k, beta_v, beta_q, beta_k):\n", "\n", "  # TODO -- Write this function\n", "  # 1. Compute queries, keys, and values\n", "  # 2. Compute dot products\n", "  # 3. Scale the dot products as in equation 12.9\n", "  # 4. Apply softmax to calculate attentions\n", "  # 5. Weight values by attentions\n", "  # Replace this line\n", "  X_prime = np.zeros_like(X);\n", "\n", "  return X_prime"], "metadata": {"id": "kLU7PUnnqvIh"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Run the self attention mechanism\n", "X_prime = scaled_dot_product_self_attention(X,omega_v, omega_q, omega_k, beta_v, beta_q, beta_k)\n", "\n", "# Print out the results\n", "print(X_prime)"], "metadata": {"id": "n18e3XNzmVgL"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["TODO -- Investigate whether the self-attention mechanism is covariant with respect to permutation.\n", "If it is, when we permute the columns of the input matrix $\\mathbf{X}$, the columns of the output matrix $\\mathbf{X}'$ will also be permuted.\n"], "metadata": {"id": "QDEkIrcgrql-"}}]}