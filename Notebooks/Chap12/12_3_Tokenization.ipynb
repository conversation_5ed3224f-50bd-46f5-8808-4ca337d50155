{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyP0/KodWM9Dtr2x+8MdXXH1", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/Notebooks/Chap12/12_3_Tokenization.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# **Notebook 12.3: Tokenization**\n", "\n", "This notebook builds set of tokens from a text string as in figure 12.8 of the book.\n", "\n", "Work through the cells below, running each cell in turn. In various places you will see the words \"TODO\". Follow the instructions at these places and make predictions about what is going to happen or write code to complete the functions.\n", "\n", "I adapted this code from *SOMEWHERE*.  If anyone recognizes it, can you let me know and I will give the proper attribution or rewrite if the license is not permissive.\n", "\n", "Contact <NAME_EMAIL> if you find any mistakes or have any suggestions.\n", "\n"], "metadata": {"id": "t9vk9Elugvmi"}}, {"cell_type": "code", "source": ["import re, collections"], "metadata": {"id": "3_WkaFO3OfLi"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["text = \"a sailor went to sea sea sea \"+\\\n", "                  \"to see what he could see see see \"+\\\n", "                  \"but all that he could see see see \"+\\\n", "                  \"was the bottom of the deep blue sea sea sea\""], "metadata": {"id": "tVZVuauIXmJk"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Tokenize the input sentence To begin with the tokens are the individual letters and the </w> whitespace token. So, we represent each word in terms of these tokens with spaces between the tokens to delineate them.\n", "\n", "The tokenized text is stored in a structure that represents each word as tokens together with the count of how often that word occurs.  We'll call this the *vocabulary*."], "metadata": {"id": "fF2RBrouWV5w"}}, {"cell_type": "code", "source": ["def initialize_vocabulary(text):\n", "  vocab = collections.defaultdict(int)\n", "  words = text.strip().split()\n", "  for word in words:\n", "      vocab[' '.join(list(word)) + ' </w>'] += 1\n", "  return vocab"], "metadata": {"id": "OfvXkLSARk4_"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["vocab = initialize_vocabulary(text)\n", "print('Vocabulary: {}'.format(vocab))\n", "print('Size of vocabulary: {}'.format(len(vocab)))"], "metadata": {"id": "aydmNqaoOpSm"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Find all the tokens in the current vocabulary and their frequencies"], "metadata": {"id": "fJAiCjphWsI9"}}, {"cell_type": "code", "source": ["def get_tokens_and_frequencies(vocab):\n", "  tokens = collections.defaultdict(int)\n", "  for word, freq in vocab.items():\n", "      word_tokens = word.split()\n", "      for token in word_tokens:\n", "          tokens[token] += freq\n", "  return tokens"], "metadata": {"id": "qYi6F_K3RYsW"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["tokens = get_tokens_and_frequencies(vocab)\n", "print('Tokens: {}'.format(tokens))\n", "print('Number of tokens: {}'.format(len(tokens)))"], "metadata": {"id": "Y4LCVGnvXIwp"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Find each pair of adjacent tokens in the vocabulary\n", "and count them.  We will subsequently merge the most frequently occurring pair."], "metadata": {"id": "_-Rh1mD_Ww3b"}}, {"cell_type": "code", "source": ["def get_pairs_and_counts(vocab):\n", "    pairs = collections.defaultdict(int)\n", "    for word, freq in vocab.items():\n", "        symbols = word.split()\n", "        for i in range(len(symbols)-1):\n", "            pairs[symbols[i],symbols[i+1]] += freq\n", "    return pairs"], "metadata": {"id": "OqJTB3UFYubH"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["pairs = get_pairs_and_counts(vocab)\n", "print('Pairs: {}'.format(pairs))\n", "print('Number of distinct pairs: {}'.format(len(pairs)))\n", "\n", "most_frequent_pair = max(pairs, key=pairs.get)\n", "print('Most frequent pair: {}'.format(most_frequent_pair))"], "metadata": {"id": "d-zm0JBcZSjS"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Merge the instances of the best pair in the vocabulary"], "metadata": {"id": "pcborzqIXQFS"}}, {"cell_type": "code", "source": ["def merge_pair_in_vocabulary(pair, vocab_in):\n", "    vocab_out = {}\n", "    bigram = re.escape(' '.join(pair))\n", "    p = re.compile(r'(?<!\\S)' + bigram + r'(?!\\S)')\n", "    for word in vocab_in:\n", "        word_out = p.sub(''.join(pair), word)\n", "        vocab_out[word_out] = vocab_in[word]\n", "    return vocab_out"], "metadata": {"id": "xQI6NALdWQZX"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["vocab = merge_pair_in_vocabulary(most_frequent_pair, vocab)\n", "print('Vocabulary: {}'.format(vocab))\n", "print('Size of vocabulary: {}'.format(len(vocab)))"], "metadata": {"id": "TRYeBZI3ZULu"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Update the tokens, which now include the best token 'se'"], "metadata": {"id": "bkhUx3GeXwba"}}, {"cell_type": "code", "source": ["tokens = get_tokens_and_frequencies(vocab)\n", "print('Tokens: {}'.format(tokens))\n", "print('Number of tokens: {}'.format(len(tokens)))"], "metadata": {"id": "Fqj-vQWeXxQi"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Now let's write the full tokenization routine"], "metadata": {"id": "K_hKp2kSXXS1"}}, {"cell_type": "code", "source": ["# TODO -- write this routine by filling in this missing parts,\n", "# calling the above routines\n", "def tokenize(text, num_merges):\n", "  # Initialize the vocabulary from the input text\n", "  # vocab = (your code here)\n", "\n", "  for i in range(num_merges):\n", "    # Find the tokens and how often they occur in the vocabulary\n", "    # tokens = (your code here)\n", "\n", "    # Find the pairs of adjacent tokens and their counts\n", "    # pairs = (your code here)\n", "\n", "    # Find the most frequent pair\n", "    # most_frequent_pair = (your code here)\n", "    print('Most frequent pair: {}'.format(most_frequent_pair))\n", "\n", "    # Merge the code in the vocabulary\n", "    # vocab = (your code here)\n", "\n", "  # Find the tokens and how often they occur in the vocabulary one last time\n", "  # tokens = (your code here)\n", "\n", "  return tokens, vocab"], "metadata": {"id": "U_1SkQRGQ8f3"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["tokens, vocab = tokenize(text, num_merges=22)"], "metadata": {"id": "w0EkHTrER_-I"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print('Tokens: {}'.format(tokens))\n", "print('Number of tokens: {}'.format(len(tokens)))\n", "print('Vocabulary: {}'.format(vocab))\n", "print('Size of vocabulary: {}'.format(len(vocab)))"], "metadata": {"id": "moqDtTzIb-NG"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["TODO - Consider the input text:\n", "\n", "\"How much wood could a woodchuck chuck if a woodchuck could chuck wood\"\n", "\n", "How many tokens will there be initially and what will they be?\n", "How many tokens will there be if we run the tokenization routine for the maximum number of iterations (merges)?\n", "\n", "When you've made your predictions, run the code and see if you are correct."], "metadata": {"id": "jOW_HJtMdAxd"}}]}