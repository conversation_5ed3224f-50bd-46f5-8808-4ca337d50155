{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyMJLViYIpiivB2A7YIuZmzU", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/Notebooks/Chap16/16_1_1D_Normalizing_Flows.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# **Notebook 16.1: 1D normalizing flows**\n", "\n", "This notebook investigates a 1D normalizing flows example similar to that illustrated in figures 16.1 to 16.3 in the book.\n", "\n", "Work through the cells below, running each cell in turn. In various places you will see the words \"TODO\". Follow the instructions at these places and make predictions about what is going to happen or write code to complete the functions.\n", "\n", "Contact <NAME_EMAIL> if you find any mistakes or have any suggestions."], "metadata": {"id": "t9vk9Elugvmi"}}, {"cell_type": "code", "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"], "metadata": {"id": "OLComQyvCIJ7"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["First we start with a base probability density function"], "metadata": {"id": "IyVn-Gi-p7wf"}}, {"cell_type": "code", "source": ["# Define the base pdf\n", "def gauss_pdf(z, mu, sigma):\n", "  pr_z = np.exp( -0.5 * (z-mu) * (z-mu) / (sigma * sigma))/(np.sqrt(2*3.1413) * sigma)\n", "  return pr_z"], "metadata": {"id": "ZIfQwhd-AV6L"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["z = np.arange(-3,3,0.01)\n", "pr_z = gauss_pdf(z, 0, 1)\n", "\n", "fig,ax = plt.subplots()\n", "ax.plot(z, pr_z)\n", "ax.set_xlim([-3,3])\n", "ax.set_xlabel('$z$')\n", "ax.set_ylabel('$Pr(z)$')\n", "plt.show();"], "metadata": {"id": "gGh8RHmFp_Ls"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Now let's define a nonlinear function that maps from the latent space $z$ to the observed data $x$."], "metadata": {"id": "wVXi5qIfrL9T"}}, {"cell_type": "code", "source": ["# Define a function that maps from the base pdf over z to the observed space x\n", "def f(z):\n", "    x1 = 6/(1+np.exp(-(z-0.25)*1.5))-3\n", "    x2 = z\n", "    p = z * z/9\n", "    x = (1-p) * x1 + p * x2\n", "    return x\n", "\n", "# Compute gradient of that function using finite differences\n", "def df_dz(z):\n", "    return (f(z+0.0001)-f(z-0.0001))/0.0002"], "metadata": {"id": "shHdgZHjp52w"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["x = f(z)\n", "fig, ax = plt.subplots()\n", "ax.plot(z,x)\n", "ax.set_xlim(-3,3)\n", "ax.set_ylim(-3,3)\n", "ax.set_xlabel('Latent variable, $z$')\n", "ax.set_ylabel('Observed variable, $x$')\n", "plt.show()"], "metadata": {"id": "sz7bnCLUq3Qs"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Now let's evaluate the density in the observed space using equation 16.1"], "metadata": {"id": "rmI0BbuQyXoc"}}, {"cell_type": "code", "source": ["# TODO -- plot the density in the observed space\n", "# Replace these line\n", "x = np.ones_like(z)\n", "pr_x = np.ones_like(pr_z)\n"], "metadata": {"id": "iPdiT_5gyNOD"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Plot the density in the observed space\n", "fig,ax = plt.subplots()\n", "ax.plot(x, pr_x)\n", "ax.set_xlim([-3,3])\n", "ax.set_ylim([0, 0.5])\n", "ax.set_xlabel('$x$')\n", "ax.set_ylabel('$Pr(x)$')\n", "plt.show();"], "metadata": {"id": "Jlks8MW3zulA"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Now let's draw some samples from the new distribution (see section 16.1)"], "metadata": {"id": "1c5rO0HHz-FV"}}, {"cell_type": "code", "source": ["np.random.seed(1)\n", "n_sample = 20\n", "\n", "# TODO -- Draw samples from the modeled density\n", "# Replace this line\n", "x_samples = np.ones((n_sample, 1))\n", "\n"], "metadata": {"id": "LIlTRfpZz2k_"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Draw the samples\n", "fig,ax = plt.subplots()\n", "ax.plot(x, pr_x)\n", "for x_sample in x_samples:\n", "  ax.plot([x_sample, x_sample], [0,0.1], 'r-')\n", "\n", "ax.set_xlim([-3,3])\n", "ax.set_ylim([0, 0.5])\n", "ax.set_xlabel('$x$')\n", "ax.set_ylabel('$Pr(x)$')\n", "plt.show();"], "metadata": {"id": "JS__QPNv0vUA"}, "execution_count": null, "outputs": []}]}