{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {"colab_type": "text", "id": "view-in-github"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/Notebooks/Chap16/16_3_Contraction_Mappings.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "t9vk9Elugvmi"}, "source": ["# **Notebook 16.3: Contraction mappings**\n", "\n", "This notebook investigates a 1D normalizing flows example similar to that illustrated in figure 16.9 in the book.\n", "\n", "Work through the cells below, running each cell in turn. In various places you will see the words \"TODO\". Follow the instructions at these places and make predictions about what is going to happen or write code to complete the functions.\n", "\n", "Contact <NAME_EMAIL> if you find any mistakes or have any suggestions."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "OLComQyvCIJ7"}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4Pfz2KSghdVI"}, "outputs": [], "source": ["# Define a function that is a contraction mapping\n", "def f(z):\n", "    return 0.3 + 0.5 *z + 0.02 * np.sin(z*15)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zEwCbIx0hpAI"}, "outputs": [], "source": ["def draw_function(f, fixed_point=None):\n", "  z = np.arange(0,1,0.01)\n", "  z_prime = f(z)\n", "\n", "  # Draw this function\n", "  fig, ax = plt.subplots()\n", "  ax.plot(z, z_prime,'c-')\n", "  ax.plot([0,1],[0,1],'k--')\n", "  if fixed_point!=None:\n", "    ax.plot(fixed_point, fixed_point, 'ro')\n", "  ax.set_xlim(0,1)\n", "  ax.set_ylim(0,1)\n", "  ax.set_xlabel('Input, $z$')\n", "  ax.set_ylabel('Output, f$[z]$')\n", "  plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "k4e5Yu0fl8bz"}, "outputs": [], "source": ["draw_function(f)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "DfgKrpCAjnol"}, "source": ["Now let's find where $\\text{f}[z]=z$ using fixed point iteration"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bAOBvZT-j3lv"}, "outputs": [], "source": ["# Takes a function f and a starting point z\n", "def fixed_point_iteration(f, z0):\n", "  # TODO -- write this function\n", "  # Print out the iterations as you go, so you can see the progress\n", "  # Set the maximum number of iterations to 20\n", "  # Replace this line\n", "  z_out = 0.5;\n", "\n", "\n", "\n", "  return z_out"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "CAS0lgIomAa0"}, "source": ["Now let's test that and plot the solution"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "EYQZJdNPk8Lg"}, "outputs": [], "source": ["# Now let's test that\n", "z = fixed_point_iteration(f, 0.2)\n", "draw_function(f, z)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4DipPiqVlnwJ"}, "outputs": [], "source": ["# Let's define another function\n", "def f2(z):\n", "    return 0.7 + -0.6 *z + 0.03 * np.sin(z*15)\n", "draw_function(f2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tYOdbWcomdEE"}, "outputs": [], "source": ["# Now let's test that\n", "# TODO Before running this code, predict what you think will happen\n", "z = fixed_point_iteration(f2, 0.9)\n", "draw_function(f2, z)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Mni37RUpmrIu"}, "outputs": [], "source": ["# Let's define another function\n", "# Define a function that is a contraction mapping\n", "def f3(z):\n", "    return -0.2 + 1.5 *z + 0.1 * np.sin(z*15)\n", "draw_function(f3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "agt5mfJrnM1O"}, "outputs": [], "source": ["# Now let's test that\n", "# TODO Before running this code, predict what you think will happen\n", "z = fixed_point_iteration(f3, 0.7)\n", "draw_function(f3, z)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "n6GI46-ZoQz6"}, "source": ["Finally, let's invert a problem of the form $y = z+ f[z]$  for a given value of $y$. What is the $z$ that maps to it?"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dy6r3jr9rjPf"}, "outputs": [], "source": ["def f4(z):\n", "   return -0.3 + 0.5 *z + 0.02 * np.sin(z*15)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "GMX64Iz0nl-B"}, "outputs": [], "source": ["def fixed_point_iteration_z_plus_f(f, y, z0):\n", "  # TODO -- write this function\n", "  # Replace this line\n", "  z_out = 1\n", "\n", "  return z_out"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "uXxKHad5qT8Y"}, "outputs": [], "source": ["def draw_function2(f, y, fixed_point=None):\n", "  z = np.arange(0,1,0.01)\n", "  z_prime = z+f(z)\n", "\n", "  # Draw this function\n", "  fig, ax = plt.subplots()\n", "  ax.plot(z, z_prime,'c-')\n", "  ax.plot(z, y-f(z),'r-')\n", "  ax.plot([0,1],[0,1],'k--')\n", "  if fixed_point!=None:\n", "    ax.plot(fixed_point, y, 'ro')\n", "  ax.set_xlim(0,1)\n", "  ax.set_ylim(0,1)\n", "  ax.set_xlabel('Input, $z$')\n", "  ax.set_ylabel('Output, z+f$[z]$')\n", "  plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mNEBXC3Aqd_1"}, "outputs": [], "source": ["# Test this out and draw\n", "y = 0.8\n", "z = fixed_point_iteration_z_plus_f(f4,y,0.2)\n", "draw_function2(f4,y,z)\n", "# If you have done this correctly, the red dot should be\n", "# where the cyan curve has a y value of 0.8"]}], "metadata": {"colab": {"authorship_tag": "ABX9TyNeCWINUqqUGKMcxsqPFTAh", "include_colab_link": true, "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}