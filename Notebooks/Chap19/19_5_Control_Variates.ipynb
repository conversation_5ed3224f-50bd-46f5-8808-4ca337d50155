{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyO6CLgMIO5bUVAMkzPT3z4y", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/Notebooks/Chap19/19_5_Control_Variates.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# **Notebook 19.5: Control variates**\n", "\n", "This notebook investigates the method of control variates as described in figure 19.16\n", "\n", "\n", "Work through the cells below, running each cell in turn. In various places you will see the words \"TODO\". Follow the instructions at these places and make predictions about what is going to happen or write code to complete the functions.\n", "\n", "Contact <NAME_EMAIL> if you find any mistakes or have any suggestions."], "metadata": {"id": "t9vk9Elugvmi"}}, {"cell_type": "code", "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"], "metadata": {"id": "OLComQyvCIJ7"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Generate from our two variables, $a$ and $b$.  We are interested in estimating the mean of $a$, but we can use $b$$ to improve our estimates if it is correlated"], "metadata": {"id": "uwmhcAZBzTRO"}}, {"cell_type": "code", "source": ["# Sample from two variables with mean zero, standard deviation one, and a given correlation coefficient\n", "def get_samples(n_samples, correlation_coeff=0.8):\n", "  a = np.random.normal(size=(1,n_samples))\n", "  temp = np.random.normal(size=(1, n_samples))\n", "  b = correlation_coeff * a + np.sqrt(1-correlation_coeff * correlation_coeff) * temp\n", "  return a, b"], "metadata": {"id": "bC8MBXPawQJ3"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["N = 10000000\n", "a,b, = get_samples(N)\n", "\n", "# Verify that these two variables have zero mean and unit standard deviation\n", "print(\"Mean of a = %3.3f,  Std of a = %3.3f\"%(np.mean(a),np.std(a)))\n", "print(\"Mean of b = %3.3f,  Std of b = %3.3f\"%(np.mean(b),np.std(b)))"], "metadata": {"id": "1cT66nbRyW34"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Now let's samples $N=10$ examples from $a$ and estimate their mean $\\mathbb{E}[a]$.  We'll do this 1000000 times and then compute the variance of those estimates."], "metadata": {"id": "PWoYRpjS0Nlf"}}, {"cell_type": "code", "source": ["n_estimate = 1000000\n", "\n", "N = 5\n", "\n", "# TODO -- sample N examples of variable $a$\n", "# Compute the mean of each\n", "# Compute the mean and variance of these estimates of the mean\n", "# Replace this line\n", "mean_of_estimator_1 = -1; std_of_estimator_1 = -1\n", "\n", "print(\"Standard estimator mean = %3.3f, Standard estimator variance = %3.3f\"%(mean_of_estimator_1, std_of_estimator_1))"], "metadata": {"id": "n6Uem2aYzBp7"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Now let's estimate the mean $\\mathbf{E}[a]$ of $a$ by computing $a-b$ where $b$ is correlated with $a$"], "metadata": {"id": "F-af86z13TFc"}}, {"cell_type": "code", "source": ["n_estimate = 1000000\n", "\n", "N = 5\n", "\n", "# TODO -- sample N examples of variables $a$ and $b$\n", "# Compute $c=a-b$ for each and then compute the mean of $c$\n", "# Compute the mean and variance of these estimates of the mean of $c$\n", "# Replace this line\n", "mean_of_estimator_2 = -1; std_of_estimator_2 = -1\n", "\n", "print(\"Control variate estimator mean = %3.3f, Control variate estimator variance = %3.3f\"%(mean_of_estimator_2, std_of_estimator_2))"], "metadata": {"id": "MrEVDggY0IGU"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Note that they both have a very similar mean, but the second estimator has a lower variance.   \n", "\n", "TODO -- Experiment with different samples sizes $N$ and correlation coefficients."], "metadata": {"id": "Jklzkca14ofS"}}]}