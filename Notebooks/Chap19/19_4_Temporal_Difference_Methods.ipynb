{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/Notebooks/Chap19/19_4_Temporal_Difference_Methods.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "t9vk9Elugvmi"}, "source": ["# **Notebook 19.4: Temporal difference methods**\n", "\n", "This notebook investigates temporal difference methods for  tabular reinforcement learning as described in section 19.3.3 of the book\n", "\n", "Work through the cells below, running each cell in turn. In various places you will see the words \"TODO\". Follow the instructions at these places and make predictions about what is going to happen or write code to complete the functions.\n", "\n", "Contact <NAME_EMAIL> if you find any mistakes or have any suggestions.\n", "\n", "Thanks to [<PERSON><PERSON><PERSON>](https://www.akshilpatel.com) and [<PERSON>](https://jessicanicholson1.github.io) for their help in preparing this notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "OLComQyvCIJ7"}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from PIL import Image\n", "from IPython.display import clear_output\n", "from time import sleep\n", "from copy import deepcopy"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ZsvrUszPLyEG"}, "outputs": [], "source": ["# Get local copies of components of images\n", "!wget https://raw.githubusercontent.com/udlbook/udlbook/main/Notebooks/Chap19/Empty.png\n", "!wget https://raw.githubusercontent.com/udlbook/udlbook/main/Notebooks/Chap19/Hole.png\n", "!wget https://raw.githubusercontent.com/udlbook/udlbook/main/Notebooks/Chap19/Fish.png\n", "!wget https://raw.githubusercontent.com/udlbook/udlbook/main/Notebooks/Chap19/Penguin.png"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Gq1HfJsHN3SB"}, "outputs": [], "source": ["# Ugly class that takes care of drawing pictures like in the book.\n", "# You can totally ignore this code!\n", "class DrawMDP:\n", "  # Constructor initializes parameters\n", "  def __init__(self, n_row, n_col):\n", "    self.empty_image = np.asarray(Image.open('Empty.png'))\n", "    self.hole_image = np.asarray(Image.open('Hole.png'))\n", "    self.fish_image = np.asarray(Image.open('Fish.png'))\n", "    self.penguin_image = np.asarray(Image.open('Penguin.png'))\n", "    self.fig,self.ax = plt.subplots()\n", "    self.n_row = n_row\n", "    self.n_col = n_col\n", "\n", "    my_colormap_vals_hex =('2a0902', '2b0a03', '2c0b04', '2d0c05', '2e0c06', '2f0d07', '300d08', '310e09', '320f0a', '330f0b', '34100b', '35110c', '36110d', '37120e', '38120f', '39130f', '3a1410', '3b1411', '3c1511', '3d1612', '3e1613', '3f1713', '401714', '411814', '421915', '431915', '451a16', '461b16', '471b17', '481c17', '491d18', '4a1d18', '4b1e19', '4c1f19', '4d1f1a', '4e201b', '50211b', '51211c', '52221c', '53231d', '54231d', '55241e', '56251e', '57261f', '58261f', '592720', '5b2821', '5c2821', '5d2922', '5e2a22', '5f2b23', '602b23', '612c24', '622d25', '632e25', '652e26', '662f26', '673027', '683027', '693128', '6a3229', '6b3329', '6c342a', '6d342a', '6f352b', '70362c', '71372c', '72372d', '73382e', '74392e', '753a2f', '763a2f', '773b30', '783c31', '7a3d31', '7b3e32', '7c3e33', '7d3f33', '7e4034', '7f4134', '804235', '814236', '824336', '834437', '854538', '864638', '874739', '88473a', '89483a', '8a493b', '8b4a3c', '8c4b3c', '8d4c3d', '8e4c3e', '8f4d3f', '904e3f', '924f40', '935041', '945141', '955242', '965343', '975343', '985444', '995545', '9a5646', '9b5746', '9c5847', '9d5948', '9e5a49', '9f5a49', 'a05b4a', 'a15c4b', 'a35d4b', 'a45e4c', 'a55f4d', 'a6604e', 'a7614e', 'a8624f', 'a96350', 'aa6451', 'ab6552', 'ac6552', 'ad6653', 'ae6754', 'af6855', 'b06955', 'b16a56', 'b26b57', 'b36c58', 'b46d59', 'b56e59', 'b66f5a', 'b7705b', 'b8715c', 'b9725d', 'ba735d', 'bb745e', 'bc755f', 'bd7660', 'be7761', 'bf7862', 'c07962', 'c17a63', 'c27b64', 'c27c65', 'c37d66', 'c47e67', 'c57f68', 'c68068', 'c78169', 'c8826a', 'c9836b', 'ca846c', 'cb856d', 'cc866e', 'cd876f', 'ce886f', 'ce8970', 'cf8a71', 'd08b72', 'd18c73', 'd28d74', 'd38e75', 'd48f76', 'd59077', 'd59178', 'd69279', 'd7937a', 'd8957b', 'd9967b', 'da977c', 'da987d', 'db997e', 'dc9a7f', 'dd9b80', 'de9c81', 'de9d82', 'df9e83', 'e09f84', 'e1a185', 'e2a286', 'e2a387', 'e3a488', 'e4a589', 'e5a68a', 'e5a78b', 'e6a88c', 'e7aa8d', 'e7ab8e', 'e8ac8f', 'e9ad90', 'eaae91', 'eaaf92', 'ebb093', 'ecb295', 'ecb396', 'edb497', 'eeb598', 'eeb699', 'efb79a', 'efb99b', 'f0ba9c', 'f1bb9d', 'f1bc9e', 'f2bd9f', 'f2bfa1', 'f3c0a2', 'f3c1a3', 'f4c2a4', 'f5c3a5', 'f5c5a6', 'f6c6a7', 'f6c7a8', 'f7c8aa', 'f7c9ab', 'f8cbac', 'f8ccad', 'f8cdae', 'f9ceb0', 'f9d0b1', 'fad1b2', 'fad2b3', 'fbd3b4', 'fbd5b6', 'fbd6b7', 'fcd7b8', 'fcd8b9', 'fcdaba', 'fddbbc', 'fddcbd', 'fddebe', 'fddfbf', 'fee0c1', 'fee1c2', 'fee3c3', 'fee4c5', 'ffe5c6', 'ffe7c7', 'ffe8c9', 'ffe9ca', 'ffebcb', 'ffeccd', 'ffedce', 'ffefcf', 'fff0d1', 'fff2d2', 'fff3d3', 'fff4d5', 'fff6d6', 'fff7d8', 'fff8d9', 'fffada', 'fffbdc', 'fffcdd', 'fffedf', 'ffffe0')\n", "    my_colormap_vals_dec = np.array([int(element,base=16) for element in my_colormap_vals_hex])\n", "    r = np.floor(my_colormap_vals_dec/(256*256))\n", "    g = np.floor((my_colormap_vals_dec - r *256 *256)/256)\n", "    b = np.floor(my_colormap_vals_dec - r * 256 *256 - g * 256)\n", "    self.colormap = np.vstack((r,g,b)).transpose()/255.0\n", "\n", "\n", "  def draw_text(self, text, row, col, position, color):\n", "    if position == 'bc':\n", "      self.ax.text( 83*col+41,83 * (row+1) -5, text, horizontalalignment=\"center\", color=color, fontweight='bold')\n", "    if position == 'tc':\n", "      self.ax.text( 83*col+41,83 * (row) +10, text, horizontalalignment=\"center\", color=color, fontweight='bold')\n", "    if position == 'lc':\n", "      self.ax.text( 83*col+2,83 * (row) +41, text, verticalalignment=\"center\", color=color, fontweight='bold', rotation=90)\n", "    if position == 'rc':\n", "      self.ax.text( 83*(col+1)-5,83 * (row) +41, text, horizontalalignment=\"right\", verticalalignment=\"center\", color=color, fontweight='bold', rotation=-90)\n", "    if position == 'tl':\n", "      self.ax.text( 83*col+5,83 * row +5, text, verticalalignment = 'top', horizontalalignment=\"left\", color=color, fontweight='bold')\n", "    if position == 'tr':\n", "      self.ax.text( 83*(col+1)-5, 83 * row +5, text, verticalalignment = 'top', horizontalalignment=\"right\", color=color, fontweight='bold')\n", "\n", "  # Draws a set of states\n", "  def draw_path(self, path, color1, color2):\n", "    for i in range(len(path)-1):\n", "      row_start = np.floor(path[i]/self.n_col)\n", "      row_end = np.floor(path[i+1]/self.n_col)\n", "      col_start = path[i] - row_start * self.n_col\n", "      col_end = path[i+1] - row_end * self.n_col\n", "\n", "      color_index = int(np.floor(255 * i/(len(path)-1.)))\n", "      self.ax.plot([col_start * 83+41 + i, col_end * 83+41 + i ],[row_start * 83+41 +  i, row_end * 83+41 + i ], color=(self.colormap[color_index,0],self.colormap[color_index,1],self.colormap[color_index,2]))\n", "\n", "\n", "  # Draw deterministic policy\n", "  def draw_deterministic_policy(self,i, action):\n", "      row = np.floor(i/self.n_col)\n", "      col = i - row * self.n_col\n", "      center_x = 83 * col + 41\n", "      center_y = 83 * row + 41\n", "      arrow_base_width = 10\n", "      arrow_height = 15\n", "      # Draw arrow pointing upward\n", "      if action ==0:\n", "          triangle_indices = np.array([[center_x, center_y-arrow_height/2],\n", "                              [center_x - arrow_base_width/2, center_y+arrow_height/2],\n", "                              [center_x + arrow_base_width/2, center_y+arrow_height/2]])\n", "      # Draw arrow pointing right\n", "      if action ==1:\n", "          triangle_indices = np.array([[center_x + arrow_height/2, center_y],\n", "                              [center_x - arrow_height/2, center_y-arrow_base_width/2],\n", "                              [center_x - arrow_height/2, center_y+arrow_base_width/2]])\n", "      # Draw arrow pointing downward\n", "      if action ==2:\n", "          triangle_indices = np.array([[center_x, center_y+arrow_height/2],\n", "                              [center_x - arrow_base_width/2, center_y-arrow_height/2],\n", "                              [center_x + arrow_base_width/2, center_y-arrow_height/2]])\n", "      # Draw arrow pointing left\n", "      if action ==3:\n", "          triangle_indices = np.array([[center_x - arrow_height/2, center_y],\n", "                              [center_x + arrow_height/2, center_y-arrow_base_width/2],\n", "                              [center_x + arrow_height/2, center_y+arrow_base_width/2]])\n", "      self.ax.fill(triangle_indices[:,0], triangle_indices[:,1],facecolor='cyan', edgecolor='darkcyan', linewidth=1)\n", "\n", "  # Draw stochastic policy\n", "  def draw_stochastic_policy(self,i, action_probs):\n", "      row = np.floor(i/self.n_col)\n", "      col = i - row * self.n_col\n", "      offset = 20\n", "      # Draw arrow pointing upward\n", "      center_x = 83 * col + 41\n", "      center_y = 83 * row + 41 - offset\n", "      arrow_base_width = 15 * action_probs[0]\n", "      arrow_height = 20 * action_probs[0]\n", "      triangle_indices = np.array([[center_x, center_y-arrow_height/2],\n", "                          [center_x - arrow_base_width/2, center_y+arrow_height/2],\n", "                          [center_x + arrow_base_width/2, center_y+arrow_height/2]])\n", "      self.ax.fill(triangle_indices[:,0], triangle_indices[:,1],facecolor='cyan', edgecolor='darkcyan', linewidth=1)\n", "\n", "      # Draw arrow pointing right\n", "      center_x = 83 * col + 41 + offset\n", "      center_y = 83 * row + 41\n", "      arrow_base_width = 15 * action_probs[1]\n", "      arrow_height = 20 * action_probs[1]\n", "      triangle_indices = np.array([[center_x + arrow_height/2, center_y],\n", "                          [center_x - arrow_height/2, center_y-arrow_base_width/2],\n", "                          [center_x - arrow_height/2, center_y+arrow_base_width/2]])\n", "      self.ax.fill(triangle_indices[:,0], triangle_indices[:,1],facecolor='cyan', edgecolor='darkcyan', linewidth=1)\n", "\n", "      # Draw arrow pointing downward\n", "      center_x = 83 * col + 41\n", "      center_y = 83 * row + 41 +offset\n", "      arrow_base_width = 15 * action_probs[2]\n", "      arrow_height = 20 * action_probs[2]\n", "      triangle_indices = np.array([[center_x, center_y+arrow_height/2],\n", "                          [center_x - arrow_base_width/2, center_y-arrow_height/2],\n", "                          [center_x + arrow_base_width/2, center_y-arrow_height/2]])\n", "      self.ax.fill(triangle_indices[:,0], triangle_indices[:,1],facecolor='cyan', edgecolor='darkcyan', linewidth=1)\n", "\n", "      # Draw arrow pointing left\n", "      center_x = 83 * col + 41 -offset\n", "      center_y = 83 * row + 41\n", "      arrow_base_width = 15 * action_probs[3]\n", "      arrow_height = 20 * action_probs[3]\n", "      triangle_indices = np.array([[center_x - arrow_height/2, center_y],\n", "                          [center_x + arrow_height/2, center_y-arrow_base_width/2],\n", "                          [center_x + arrow_height/2, center_y+arrow_base_width/2]])\n", "      self.ax.fill(triangle_indices[:,0], triangle_indices[:,1],facecolor='cyan', edgecolor='darkcyan', linewidth=1)\n", "\n", "\n", "  def draw(self, layout, state=None, draw_state_index= False, rewards=None, policy=None, state_values=None, state_action_values=None,path1=None, path2 = None):\n", "    # Construct the image\n", "    image_out = np.zeros((self.n_row * 83, self.n_col * 83, 4),dtype='uint8')\n", "    for c_row in range (self.n_row):\n", "      for c_col in range(self.n_col):\n", "        if layout[c_row * self.n_col + c_col]==0:\n", "          image_out[c_row*83:c_row*83+83, c_col*83:c_col*83+83,:] = self.empty_image\n", "        elif layout[c_row * self.n_col + c_col]==1:\n", "          image_out[c_row*83:c_row*83+83, c_col*83:c_col*83+83,:] = self.hole_image\n", "        else:\n", "          image_out[c_row*83:c_row*83+83, c_col*83:c_col*83+83,:] = self.fish_image\n", "        if state is not None and state == c_row * self.n_col + c_col:\n", "          image_out[c_row*83:c_row*83+83, c_col*83:c_col*83+83,:] = self.penguin_image\n", "\n", "    # Draw the image\n", "    plt.imshow(image_out)\n", "    self.ax.get_xaxis().set_visible(False)\n", "    self.ax.get_yaxis().set_visible(False)\n", "    self.ax.spines['top'].set_visible(False)\n", "    self.ax.spines['right'].set_visible(False)\n", "    self.ax.spines['bottom'].set_visible(False)\n", "    self.ax.spines['left'].set_visible(False)\n", "\n", "    if draw_state_index:\n", "      for c_cell in range(layout.size):\n", "          self.draw_text(\"%d\"%(c_cell), np.floor(c_cell/self.n_col), c_cell-np.floor(c_cell/self.n_col)*self.n_col,'tl','k')\n", "\n", "    # Draw the policy as triangles\n", "    if policy is not None:\n", "        # If the policy is deterministic\n", "        if len(policy) == len(layout):\n", "          for i in range(len(layout)):\n", "            self.draw_deterministic_policy(i, policy[i])\n", "        # Else it is stochastic\n", "        else:\n", "          for i in range(len(layout)):\n", "            self.draw_stochastic_policy(i,policy[:,i])\n", "\n", "\n", "    if path1 is not None:\n", "      self.draw_path(path1, np.array([1.0, 0.0, 0.0]), np.array([0.0, 1.0, 1.0]))\n", "\n", "    if rewards is not None:\n", "        for c_cell in range(layout.size):\n", "          self.draw_text(\"%d\"%(rewards[c_cell]), np.floor(c_cell/self.n_col), c_cell-np.floor(c_cell/self.n_col)*self.n_col,'tr','r')\n", "\n", "    if state_values is not None:\n", "        for c_cell in range(layout.size):\n", "          self.draw_text(\"%2.2f\"%(state_values[c_cell]), np.floor(c_cell/self.n_col), c_cell-np.floor(c_cell/self.n_col)*self.n_col,'bc','black')\n", "\n", "    if state_action_values is not None:\n", "        for c_cell in range(layout.size):\n", "          self.draw_text(\"%2.2f\"%(state_action_values[0, c_cell]), np.floor(c_cell/self.n_col), c_cell-np.floor(c_cell/self.n_col)*self.n_col,'tc','black')\n", "          self.draw_text(\"%2.2f\"%(state_action_values[1, c_cell]), np.floor(c_cell/self.n_col), c_cell-np.floor(c_cell/self.n_col)*self.n_col,'rc','black')\n", "          self.draw_text(\"%2.2f\"%(state_action_values[2, c_cell]), np.floor(c_cell/self.n_col), c_cell-np.floor(c_cell/self.n_col)*self.n_col,'bc','black')\n", "          self.draw_text(\"%2.2f\"%(state_action_values[3, c_cell]), np.floor(c_cell/self.n_col), c_cell-np.floor(c_cell/self.n_col)*self.n_col,'lc','black')\n", "\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "JU8gX59o76xM"}, "source": ["# Penguin Ice Environment\n", "\n", "In this implementation we have designed an icy gridworld that a penguin has to traverse to reach the fish found in the bottom right corner.\n", "\n", "## Environment Description\n", "\n", "Consider having to cross an icy surface to reach the yummy fish. In order to achieve this task as quickly as possible, the penguin needs to waddle along as fast as it can whilst simultaneously avoiding falling into the holes.\n", "\n", "In this icy environment the penguin is at one of the discrete cells in the gridworld. The agent starts each episode on a randomly chosen cell. The environment state dynamics are captured by the transition probabilities $Pr(s_{t+1} |s_t, a_t)$ where $s_t$ is the current state, $a_t$ is the action chosen, and $s_{t+1}$ is the next state at decision stage t. At each decision stage, the penguin can move in one of four directions: $a=0$ means try to go upward, $a=1$, right, $a=2$ down and $a=3$ left.\n", "\n", "However, the ice is slippery, so we don't always go the direction we want to: every time the agent chooses an action, with 0.25 probability, the environment changes the action taken to a differenct action, which is uniformly sampled from the other available actions.\n", "\n", "The rewards are deterministic; the penguin will receive a reward of +3 if it reaches the fish, -2 if it slips into a hole and 0 otherwise.\n", "\n", "Note that as for the states, we've indexed the actions from zero (unlike in the book) so they map to the indices of arrays better"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eBQ7lTpJQBSe"}, "outputs": [], "source": ["# We're going to work on the problem depicted in figure 19.10a\n", "n_rows = 4; n_cols = 4\n", "layout = np.zeros(n_rows * n_cols)\n", "reward_structure = np.zeros(n_rows * n_cols)\n", "layout[9] = 1 ; reward_structure[9] = -2    # Hole\n", "layout[10] = 1; reward_structure[10] = -2   # Hole\n", "layout[14] = 1; reward_structure[14] = -2   # Hole\n", "layout[15] = 2; reward_structure[15] = 3    # Fish\n", "initial_state = 0\n", "mdp_drawer = DrawMDP(n_rows, n_cols)\n", "mdp_drawer.draw(layout, state = initial_state, rewards=reward_structure, draw_state_index = True)"]}, {"cell_type": "markdown", "metadata": {"id": "6Vku6v_se2IG"}, "source": ["For clarity, the black numbers are the state number and the red numbers are the reward for being in that state.  Note that the states are indexed from 0 rather than 1 as in the book to make the code neater."]}, {"cell_type": "markdown", "metadata": {"id": "Fhc6DzZNOjiC"}, "source": ["Now let's define the state transition function $Pr(s_{t+1}|s_{t},a)$ in full where $a$ is the actions.  Here $a=0$ means try to go upward, $a=1$, right, $a=2$ down and $a=3$ right.  However, the ice is slippery, so we don't always go the direction we want to.\n", "\n", "Note that as for the states, we've indexed the actions from zero (unlike in the book) so they map to the indices of arrays better"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "wROjgnqh76xN"}, "outputs": [], "source": ["transition_probabilities_given_action0 = np.array(\\\n", "[[0.90, 0.05, 0.00, 0.00,  0.85, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00],\n", " [0.05, 0.85, 0.05, 0.00,  0.00, 0.85, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.05, 0.85, 0.05,  0.00, 0.00, 0.85, 0.00,  0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.05, 0.90,  0.00, 0.00, 0.00, 0.85,  0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00],\n", " [0.05, 0.00, 0.00, 0.00,  0.05, 0.05, 0.00, 0.00,  0.85, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.05, 0.00, 0.00,  0.05, 0.00, 0.05, 0.00,  0.00, 0.85, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.05, 0.00,  0.00, 0.05, 0.00, 0.05,  0.00, 0.00, 0.85, 0.00,  0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.05,  0.00, 0.00, 0.05, 0.05,  0.00, 0.00, 0.00, 0.85,  0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00,  0.05, 0.00, 0.00, 0.00,  0.05, 0.05, 0.00, 0.00,  0.85, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00,  0.00, 0.05, 0.00, 0.00,  0.05, 0.00, 0.05, 0.00,  0.00, 0.85, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.05, 0.00,  0.00, 0.05, 0.00, 0.05,  0.00, 0.00, 0.85, 0.00],\n", " [0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.05,  0.00, 0.00, 0.05, 0.05,  0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00,  0.05, 0.00, 0.00, 0.00,  0.10, 0.05, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00,  0.00, 0.05, 0.00, 0.00,  0.05, 0.05, 0.05, 0.00],\n", " [0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.05, 0.00,  0.00, 0.05, 0.05, 0.00],\n", " [0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.00,  0.00, 0.00, 0.00, 0.05,  0.00, 0.00, 0.05, 0.00]])\n", "\n", "\n", "transition_probabilities_given_action1 = np.array(\\\n", "[[0.10, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.85, 0.05, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.85, 0.05, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.85, 0.90, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.05, 0.00, 0.00, 0.00, 0.05, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.05, 0.00, 0.00, 0.85, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.05, 0.00, 0.00, 0.85, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.85, 0.85, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.05, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.85, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.85, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.85, 0.85, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.10, 0.05, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.85, 0.05, 0.05, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.85, 0.05, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.85, 0.00]])\n", "\n", "\n", "transition_probabilities_given_action2 = np.array(\\\n", "[[0.10, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.05, 0.05, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.05, 0.05, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.05, 0.10, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.85, 0.00, 0.00, 0.00, 0.05, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.85, 0.00, 0.00, 0.05, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.85, 0.00, 0.00, 0.05, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.85, 0.00, 0.00, 0.05, 0.05, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.85, 0.00, 0.00, 0.00, 0.05, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.85, 0.00, 0.00, 0.05, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.85, 0.00, 0.00, 0.05, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.85, 0.00, 0.00, 0.05, 0.05, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.85, 0.00, 0.00, 0.00, 0.90, 0.05, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.85, 0.00, 0.00, 0.05, 0.85, 0.05, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.85, 0.00, 0.00, 0.05, 0.85, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.85, 0.00, 0.00, 0.05, 0.00]])\n", "\n", "transition_probabilities_given_action3 = np.array(\\\n", "[[0.90, 0.85, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.05, 0.05, 0.85, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.05, 0.05, 0.85, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.05, 0.10, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.05, 0.00, 0.00, 0.00, 0.85, 0.85, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.05, 0.00, 0.00, 0.05, 0.00, 0.85, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00, 0.85, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.05, 0.05, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.85, 0.85, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00, 0.85, 0.00, 0.00, 0.05, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00, 0.85, 0.00, 0.00, 0.05, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.05, 0.05, 0.00, 0.00, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.00, 0.90, 0.85, 0.00, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.05, 0.05, 0.85, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.05, 0.05, 0.00],\n", " [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.05, 0.00, 0.00, 0.05, 0.00]])\n", "\n", "\n", "\n", "# Store all of these in a three dimension array\n", "# Pr(s_{t+1}=2|s_{t}=1, a_{t}=3] is stored at position [2,1,3]\n", "transition_probabilities_given_action = np.concatenate((np.expand_dims(transition_probabilities_given_action0,2),\n", "                                                        np.expand_dims(transition_probabilities_given_action1,2),\n", "                                                        np.expand_dims(transition_probabilities_given_action2,2),\n", "                                                        np.expand_dims(transition_probabilities_given_action3,2)),axis=2)\n", "\n", "print('Grid Size:', len(transition_probabilities_given_action[0]))\n", "print()\n", "print('Transition Probabilities for when next state = 2:')\n", "print(transition_probabilities_given_action[2])\n", "print()\n", "print('Transitions Probabilities for when next state = 2 and current state = 1')\n", "print(transition_probabilities_given_action[2][1])\n", "print()\n", "print('Transitions Probabilities for  when next state = 2 and current state = 1 and action = 3 (Left):')\n", "print(transition_probabilities_given_action[2][1][3])"]}, {"cell_type": "markdown", "metadata": {"id": "eblSQ6xZ76xN"}, "source": ["## Implementation Details\n", "\n", "We provide the following methods:\n", "- **`markov_decision_process_step`** - this function simulates $Pr(s_{t+1} | s_{t}, a_{t})$. It randomly selects an action, updates the state based on the transition probabilities associated with the chosen action, and returns the new state, the reward obtained for leaving the current state, and the chosen action. The randomness in action selection and state transitions reflects a random exploration process and the stochastic nature of the MDP, respectively.\n", "\n", "- **`get_policy`** - this function computes a policy that acts greedily with respect to the state-action values. The policy is computed for all states and the action that maximizes the state-action value is chosen for each state. When there are multiple optimal actions, one is chosen at random.\n", "\n", "\n", "You have to implement the following method:\n", "\n", "- **`q_learning_step`** - this function implements a single step of the Q-learning algorithm for reinforcement learning as shown below. The update follows the Q-learning formula and is controlled by parameters such as the learning rate (alpha) and the discount factor $(\\gamma)$. The function returns the updated state-action values matrix.\n", "\n", "$Q(s, a) \\leftarrow (1 - \\alpha) \\cdot Q(s, a) + \\alpha \\cdot \\left(r + \\gamma \\cdot \\max_{a'} Q(s', a')\\right)$"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cKLn4Iam76xN"}, "outputs": [], "source": ["def get_policy(state_action_values):\n", "    policy = np.zeros(state_action_values.shape[1]) # One action for each state\n", "    for state in range(state_action_values.shape[1]):\n", "        # Break ties for maximising actions randomly\n", "        policy[state] = np.random.choice(np.flatnonzero(state_action_values[:, state] == max(state_action_values[:, state])))\n", "    return policy"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "akjrncMF-FkU"}, "outputs": [], "source": ["def markov_decision_process_step(state, transition_probabilities_given_action, reward_structure, terminal_states, action=None):\n", "  # Pick action\n", "  if action is None:\n", "    action = np.random.randint(4)\n", "  # Update the state\n", "  new_state = np.random.choice(a=range(transition_probabilities_given_action.shape[0]), p = transition_probabilities_given_action[:, state,action])\n", "\n", "  # Return the reward -- here the reward is for arriving at the state\n", "  reward = reward_structure[new_state]\n", "  is_terminal = new_state in [terminal_states]\n", "\n", "  return new_state, reward, action, is_terminal"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5pO6-9ACWhiV"}, "outputs": [], "source": ["def q_learning_step(state_action_values, reward, state, new_state, action, is_terminal, gamma, alpha = 0.1):\n", "  # TODO -- write this function\n", "  # Replace this line\n", "  state_action_values_after = np.copy(state_action_values)\n", "\n", "  return state_action_values_after"]}, {"cell_type": "markdown", "metadata": {"id": "u4OHTTk176xO"}, "source": ["Lets run this for a single Q-learning step"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Fu5_VjvbSwfJ"}, "outputs": [], "source": ["# Initialize the state-action values to random numbers\n", "np.random.seed(0)\n", "n_state = transition_probabilities_given_action.shape[0]\n", "n_action = transition_probabilities_given_action.shape[2]\n", "terminal_states=[15]\n", "state_action_values = np.random.normal(size=(n_action, n_state))\n", "# Hard code value of termination state of finding fish to 0\n", "state_action_values[:, terminal_states] = 0\n", "gamma = 0.9\n", "\n", "policy = get_policy(state_action_values)\n", "mdp_drawer = DrawMDP(n_rows, n_cols)\n", "mdp_drawer.draw(layout, policy = policy, state_action_values = state_action_values, rewards = reward_structure)\n", "\n", "# Now let's simulate a single Q-learning step\n", "initial_state = 9\n", "print(\"Initial state =\",initial_state)\n", "new_state, reward, action, is_terminal = markov_decision_process_step(initial_state, transition_probabilities_given_action, reward_structure, terminal_states)\n", "print(\"Action =\",action)\n", "print(\"New state =\",new_state)\n", "print(\"Reward =\", reward)\n", "\n", "state_action_values_after = q_learning_step(state_action_values, reward, initial_state, new_state, action, is_terminal, gamma)\n", "print(\"Your value:\",state_action_values_after[action, initial_state])\n", "print(\"True value: 0.3024718977397814\")\n", "\n", "policy = get_policy(state_action_values)\n", "mdp_drawer = DrawMDP(n_rows, n_cols)\n", "mdp_drawer.draw(layout, policy = policy, state_action_values = state_action_values_after, rewards = reward_structure)\n"]}, {"cell_type": "markdown", "metadata": {"id": "Ogh0qucmb68J"}, "source": ["Now let's run this for a while (20000) steps and watch the policy improve"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "N6gFYifh76xO"}, "outputs": [], "source": ["# Initialize the state-action values to random numbers\n", "np.random.seed(0)\n", "n_state  = transition_probabilities_given_action.shape[0]\n", "n_action = transition_probabilities_given_action.shape[2]\n", "state_action_values = np.random.normal(size=(n_action, n_state))\n", "\n", "# Hard code value of termination state of finding fish to 0\n", "terminal_states = [15]\n", "state_action_values[:, terminal_states] = 0\n", "gamma = 0.9\n", "\n", "# Draw the initial setup\n", "print('Initial Policy:')\n", "policy = get_policy(state_action_values)\n", "mdp_drawer = DrawMDP(n_rows, n_cols)\n", "mdp_drawer.draw(layout, policy = policy, state_action_values = state_action_values, rewards = reward_structure)\n", "\n", "state = np.random.randint(n_state-1)\n", "\n", "# Run for a number of iterations\n", "for c_iter in range(20000):\n", "  new_state, reward, action, is_terminal = markov_decision_process_step(state, transition_probabilities_given_action, reward_structure, terminal_states)\n", "  state_action_values_after = q_learning_step(state_action_values, reward, state, new_state, action, is_terminal, gamma)\n", "\n", "  # If in termination state, reset state randomly\n", "  if is_terminal:\n", "    state = np.random.randint(n_state-1)\n", "  else:\n", "    state = new_state\n", "\n", "  # Update the policy\n", "  state_action_values = deepcopy(state_action_values_after)\n", "  policy = get_policy(state_action_values_after)\n", "\n", "print('Final Optimal Policy:')\n", "# Draw the final situation\n", "mdp_drawer = DrawMDP(n_rows, n_cols)\n", "mdp_drawer.draw(layout, policy = policy, state_action_values = state_action_values, rewards = reward_structure)"]}, {"cell_type": "markdown", "metadata": {"id": "djPTKuDk76xO"}, "source": ["Finally, lets run this for a **single** episode and visualize the penguin's actions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pWObQf2h76xO"}, "outputs": [], "source": ["def get_one_episode(n_state, state_action_values, terminal_states, gamma):\n", "\n", "    state = np.random.randint(n_state-1)\n", "\n", "    # Create lists to store all the states seen and actions taken throughout the single episode\n", "    all_states  = []\n", "    all_actions = []\n", "\n", "    # Initalize episode termination flag\n", "    done  = False\n", "    # Initialize counter for steps in the episode\n", "    steps = 0\n", "\n", "    all_states.append(state)\n", "\n", "    while not done:\n", "      steps += 1\n", "\n", "      new_state, reward, action, is_terminal = markov_decision_process_step(state, transition_probabilities_given_action, reward_structure, terminal_states)\n", "      all_states.append(new_state)\n", "      all_actions.append(action)\n", "\n", "      state_action_values_after = q_learning_step(state_action_values, reward, state, new_state, action, is_terminal, gamma)\n", "\n", "      # If in termination state, reset state randomly\n", "      if is_terminal:\n", "        state = np.random.randint(n_state-1)\n", "        print(f'Episode Terminated at {steps} Steps')\n", "        # Set episode termination flag\n", "        done = True\n", "      else:\n", "        state = new_state\n", "\n", "      # Update the policy\n", "      state_action_values = deepcopy(state_action_values_after)\n", "      policy = get_policy(state_action_values_after)\n", "\n", "    return all_states, all_actions, policy, state_action_values\n", ""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "P7cbCGT176xO"}, "outputs": [], "source": ["def visualize_one_episode(states, actions):\n", "    # Define actions for visualization\n", "    acts = ['up', 'right', 'down', 'left']\n", "\n", "    # Iterate over the states and actions\n", "    for i in range(len(states)):\n", "\n", "        if i == 0:\n", "            print('Starting State:', states[i])\n", "\n", "        elif i == len(states)-1:\n", "            print('Episode Done:', states[i])\n", "\n", "        else:\n", "            print('State', states[i-1])\n", "            a = actions[i]\n", "            print('Action:', acts[a])\n", "            print('Next State:', states[i])\n", "\n", "        # Visualize the current state using the MDP drawer\n", "        mdp_drawer.draw(layout, state=states[i], rewards=reward_structure, draw_state_index=True)\n", "        clear_output(True)\n", "\n", "        # Pause for a short duration to allow observation\n", "        sleep(1.5)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cr98F8PT76xP"}, "outputs": [], "source": ["# Initialize the state-action values to random numbers\n", "np.random.seed(2)\n", "n_state  = transition_probabilities_given_action.shape[0]\n", "n_action = transition_probabilities_given_action.shape[2]\n", "state_action_values = np.random.normal(size=(n_action, n_state))\n", "\n", "# Hard code value of termination state of finding fish to 0\n", "terminal_states = [15]\n", "state_action_values[:, terminal_states] = 0\n", "gamma = 0.9\n", "\n", "# Draw the initial setup\n", "print('Initial Policy:')\n", "policy = get_policy(state_action_values)\n", "mdp_drawer = DrawMDP(n_rows, n_cols)\n", "mdp_drawer.draw(layout, policy = policy, state_action_values = state_action_values, rewards = reward_structure)\n", "\n", "states, actions, policy, state_action_values = get_one_episode(n_state, state_action_values, terminal_states, gamma)\n", "\n", "print()\n", "print('Final Optimal Policy:')\n", "mdp_drawer = DrawMDP(n_rows, n_cols)\n", "mdp_drawer.draw(layout, policy = policy, state_action_values = state_action_values, rewards = reward_structure)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5zBu1g3776xP"}, "outputs": [], "source": ["visualize_one_episode(states, actions)"]}], "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 0}