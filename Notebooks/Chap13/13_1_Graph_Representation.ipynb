{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyMuzP1/oqTRTw4Xs/R4J/M3", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/Notebooks/Chap13/13_1_Graph_Representation.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# **Notebook 13.1: Graph representation**\n", "\n", "This notebook investigates representing graphs with matrices as illustrated in figure 13.4 from the book.\n", "\n", "Work through the cells below, running each cell in turn. In various places you will see the words \"TODO\". Follow the instructions at these places and make predictions about what is going to happen or write code to complete the functions.\n", "\n", "Contact <NAME_EMAIL> if you find any mistakes or have any suggestions.\n", "\n"], "metadata": {"id": "t9vk9Elugvmi"}}, {"cell_type": "code", "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import networkx as nx"], "metadata": {"id": "OLComQyvCIJ7"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Routine to draw graph structure\n", "def draw_graph_structure(adjacency_matrix):\n", "\n", "  G = nx.Graph()\n", "  n_node = adjacency_matrix.shape[0]\n", "  for i in range(n_node):\n", "    for j in range(i):\n", "      if adjacency_matrix[i,j]:\n", "          G.add_edge(i,j)\n", "\n", "  nx.draw(G, nx.spring_layout(G, seed = 0), with_labels=True)\n", "  plt.show()"], "metadata": {"id": "O1QMxC7X4vh9"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Define a graph\n", "# Note that the nodes are labelled from 0 rather than 1 as in the book\n", "A = np.array([[0,1,0,1,0,0,0,0],\n", "     [1,0,1,1,1,0,0,0],\n", "     [0,1,0,0,1,0,0,0],\n", "     [1,1,0,0,1,0,0,0],\n", "     [0,1,1,1,0,1,0,1],\n", "     [0,0,0,0,1,0,1,1],\n", "     [0,0,0,0,0,1,0,0],\n", "     [0,0,0,0,1,1,0,0]]);\n", "print(A)\n", "draw_graph_structure(A)"], "metadata": {"id": "TIrihEw-7DRV"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# TODO -- find algorithmically how many walks of length three are between nodes 3 and 7\n", "# Replace this line\n", "print(\"Number of  walks between nodes three and seven = ???\")"], "metadata": {"id": "PzvfUpkV4zCj"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# TODO -- find algorithmically what the minimum path distance between nodes 0 and 6 is\n", "# (i.e. what is the first walk length with non-zero count between 0 and 6)\n", "# Replace this line\n", "print(\"Minimum distance = ???\")\n", "\n", "\n", "# What is the worst case complexity of your method?"], "metadata": {"id": "MhhJr6CgCRb5"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Now let's represent node 0 as a vector\n", "x = np.array([[1],[0],[0],[0],[0],[0],[0],[0]]);\n", "print(x)"], "metadata": {"id": "lCQjXlatABGZ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# TODO: Find algorithmically how many paths of length 3 are there between node 0 and every other node\n", "# Replace this line\n", "print(np.zeros_like(x))"], "metadata": {"id": "nizLdZgLDzL4"}, "execution_count": null, "outputs": []}]}