{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/Notebooks/Chap03/3_1_Shallow_Networks_I.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "1Z6LB4Ybn1oN"}, "source": ["# **Notebook 3.1 -- Shallow neural networks I**\n", "\n", "The purpose of this notebook is to gain some familiarity with shallow neural networks with 1D inputs.  It works through an example similar to figure 3.3 and experiments with different activation functions. <br>\n", "\n", "Work through the cells below, running each cell in turn. In various places you will see the words \"TODO\". Follow the instructions at these places and write code to complete the functions. There are also questions interspersed in the text.\n", "\n", "Contact <NAME_EMAIL> if you find any mistakes or have any suggestions."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hAM55ZjSncOk"}, "outputs": [], "source": ["# Imports math library\n", "import numpy as np\n", "# Imports plotting library\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {"id": "wQDy9UzXpnf5"}, "source": ["Let's first construct the shallow neural network with one input, three hidden units, and one output described in section 3.1 of the book."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "OT7h7sSwpkrt"}, "outputs": [], "source": ["# Define the Rectified Linear Unit (ReLU) function\n", "def ReLU(preactivation):\n", "  # TODO write code to implement the ReLU and compute the activation at the\n", "  # hidden unit from the preactivation\n", "  # This should work on every element of the ndarray \"preactivation\" at once\n", "  # One way to do this is with the ndarray \"clip\" function\n", "  # https://numpy.org/doc/stable/reference/generated/numpy.ndarray.clip.html\n", "  activation = np.zeros_like(preactivation);\n", "\n", "  return activation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "okwJmSw9pVNF"}, "outputs": [], "source": ["# Make an array of inputs\n", "z = np.arange(-5,5,0.1)\n", "RelU_z = ReLU(z)\n", "\n", "# Plot the ReLU function\n", "fig, ax = plt.subplots()\n", "ax.plot(z,RelU_z,'r-')\n", "ax.set_xlim([-5,5]);ax.set_ylim([-5,5])\n", "ax.set_xlabel('z'); ax.set_ylabel('ReLU[z]')\n", "ax.set_aspect('equal')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "epk68ZCBu7uJ"}, "outputs": [], "source": ["# Define a shallow neural network with, one input, one output, and three hidden units\n", "def shallow_1_1_3(x, activation_fn, phi_0,phi_1,phi_2,phi_3, theta_10, theta_11, theta_20, theta_21, theta_30, theta_31):\n", "  # TODO Replace the code below to compute the three initial lines\n", "  # from the theta parameters (i.e. implement equations at bottom of figure 3.3a-c).  These are the preactivations\n", "  pre_1 = np.zeros_like(x)\n", "  pre_2 = np.zeros_like(x)\n", "  pre_3 = np.zeros_like(x)\n", "\n", "  # Pass these through the ReLU function to compute the activations as in\n", "  # figure 3.3 d-f\n", "  act_1 = activation_fn(pre_1)\n", "  act_2 = activation_fn(pre_2)\n", "  act_3 = activation_fn(pre_3)\n", "\n", "  # TODO Replace the code below to weight the activations using phi1, phi2 and phi3\n", "  # To create the equivalent of figure 3.3 g-i\n", "  w_act_1 = np.zeros_like(x)\n", "  w_act_2 = np.zeros_like(x)\n", "  w_act_3 = np.zeros_like(x)\n", "\n", "  # TODO Replace the code below to combining the weighted activations and add\n", "  # phi_0 to create the output as in figure 3.3 j\n", "  y = np.zeros_like(x)\n", "\n", "  # Return everything we have calculated\n", "  return y, pre_1, pre_2, pre_3, act_1, act_2, act_3, w_act_1, w_act_2, w_act_3"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CAr7n1lixuhQ"}, "outputs": [], "source": ["# Plot the shallow neural network.  We'll assume input in is range [0,1] and output [-1,1]\n", "# If the plot_all flag is set to true, then we'll plot all the intermediate stages as in Figure 3.3\n", "def plot_neural(x, y, pre_1, pre_2, pre_3, act_1, act_2, act_3, w_act_1, w_act_2, w_act_3, plot_all=False, x_data=None, y_data=None):\n", "\n", "  # Plot intermediate plots if flag set\n", "  if plot_all:\n", "    fig, ax = plt.subplots(3,3)\n", "    fig.set_size_inches(8.5, 8.5)\n", "    fig.tight_layout(pad=3.0)\n", "    ax[0,0].plot(x,pre_1,'r-'); ax[0,0].set_ylabel('Preactivation')\n", "    ax[0,1].plot(x,pre_2,'b-'); ax[0,1].set_ylabel('Preactivation')\n", "    ax[0,2].plot(x,pre_3,'g-'); ax[0,2].set_ylabel('Preactivation')\n", "    ax[1,0].plot(x,act_1,'r-'); ax[1,0].set_ylabel('Activation')\n", "    ax[1,1].plot(x,act_2,'b-'); ax[1,1].set_ylabel('Activation')\n", "    ax[1,2].plot(x,act_3,'g-'); ax[1,2].set_ylabel('Activation')\n", "    ax[2,0].plot(x,w_act_1,'r-'); ax[2,0].set_ylabel('Weighted Act')\n", "    ax[2,1].plot(x,w_act_2,'b-'); ax[2,1].set_ylabel('Weighted Act')\n", "    ax[2,2].plot(x,w_act_3,'g-'); ax[2,2].set_ylabel('Weighted Act')\n", "\n", "    for plot_y in range(3):\n", "      for plot_x in range(3):\n", "        ax[plot_y,plot_x].set_xlim([0,1]);ax[plot_x,plot_y].set_ylim([-1,1])\n", "        ax[plot_y,plot_x].set_aspect(0.5)\n", "      ax[2,plot_y].set_xlabel('Input, $x$');\n", "    plt.show()\n", "\n", "  fig, ax = plt.subplots()\n", "  ax.plot(x,y)\n", "  ax.set_xlabel('Input, $x$'); ax.set_ylabel('Output, $y$')\n", "  ax.set_xlim([0,1]);ax.set_ylim([-1,1])\n", "  ax.set_aspect(0.5)\n", "  if x_data is not None:\n", "    ax.plot(x_data, y_data, 'mo')\n", "    for i in range(len(x_data)):\n", "      ax.plot(x_data[i], y_data[i],)\n", "  plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "SzIVdp9U-JWb"}, "outputs": [], "source": ["# Now lets define some parameters and run the neural network\n", "theta_10 =  0.3 ; theta_11 = -1.0\n", "theta_20 = -1.0  ; theta_21 = 2.0\n", "theta_30 = -0.5  ; theta_31 = 0.65\n", "phi_0 = -0.3; phi_1 = 2.0; phi_2 = -1.0; phi_3 = 7.0\n", "\n", "# Define a range of input values\n", "x = np.arange(0,1,0.01)\n", "\n", "# We run the neural network for each of these input values\n", "y, pre_1, pre_2, pre_3, act_1, act_2, act_3, w_act_1, w_act_2, w_act_3 = \\\n", "    shallow_1_1_3(x, <PERSON><PERSON><PERSON>, phi_0,phi_1,phi_2,phi_3, theta_10, theta_11, theta_20, theta_21, theta_30, theta_31)\n", "# And then plot it\n", "plot_neural(x, y, pre_1, pre_2, pre_3, act_1, act_2, act_3, w_act_1, w_act_2, w_act_3, plot_all=True)"]}, {"cell_type": "markdown", "metadata": {"id": "T34bszToImKQ"}, "source": ["If your code is correct, then the final output should look like this:\n", "<img src=\"data:image/svg+xml;base64,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\">"]}, {"cell_type": "markdown", "metadata": {"id": "jhaBSS8oIWSX"}, "source": ["Now let's play with the parameters to make sure we understand how they work.  The original  parameters were:\n", "\n", "$\\theta_{10} =  0.3$ ; $\\theta_{11} = -1.0$<br>\n", "$\\theta_{20} =  -1.0$ ; $\\theta_{21} = 2.0$<br>\n", "$\\theta_{30} =  -0.5$ ; $\\theta_{31} = 0.65$<br>\n", "$\\phi_0 = -0.3; \\phi_1 = 2.0; \\phi_2 = -1.0; \\phi_3 = 7.0$"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ur4arJ8KAQWe"}, "outputs": [], "source": ["# TODO\n", "# 1. Predict what effect changing phi_0 will have on the network.\n", "\n", "# 2. Predict what effect multiplying phi_1, phi_2, phi_3 by 0.5 would have.  Check if you are correct\n", "\n", "# 3. Predict what effect multiplying phi_1 by -1 will have.  Check if you are correct.\n", "\n", "# 4. Predict what effect setting theta_20 to -1.2 will have.  Check if you are correct.\n", "\n", "# 5. Change the parameters so that there are only two \"joints\" (including outside the range of the plot)\n", "# There are actually three ways to do this. See if you can figure them all out\n", "\n", "# 6. With the original parameters, the second line segment is flat (i.e. has slope zero)\n", "# How could you change theta_10 so that all of the segments have non-zero slopes\n", "\n", "# 7. What do you predict would happen if you multiply theta_20 and theta21 by 0.5, and phi_2 by 2.0?\n", "# Check if you are correct.\n", "\n", "# 8. What do you predict would happen if you multiply theta_20 and theta21 by -0.5, and phi_2 by -2.0?\n", "# Check if you are correct.\n", "\n", "theta_10 =  0.3 ; theta_11 = -1.0\n", "theta_20 = -1.0  ; theta_21 = 2.0\n", "theta_30 = -0.5  ; theta_31 = 0.65\n", "phi_0 = -0.3; phi_1 = 2.0; phi_2 = -1.0; phi_3 = 7.0\n", "\n", "# Define a range of input values\n", "x = np.arange(0,1,0.01)\n", "\n", "# We run the neural network for each of these input values\n", "y, pre_1, pre_2, pre_3, act_1, act_2, act_3, w_act_1, w_act_2, w_act_3 = \\\n", "    shallow_1_1_3(x, <PERSON><PERSON><PERSON>, phi_0,phi_1,phi_2,phi_3, theta_10, theta_11, theta_20, theta_21, theta_30, theta_31)\n", "# And then plot it\n", "plot_neural(x, y, pre_1, pre_2, pre_3, act_1, act_2, act_3, w_act_1, w_act_2, w_act_3, plot_all=True)"]}, {"cell_type": "markdown", "metadata": {"id": "osonHsEqVp2I"}, "source": ["# Least squares loss\n", "\n", "Now let's consider fitting the network to data.  First we need to define the loss function.  We'll use the least squares loss:\n", "\n", "\\begin{equation}\n", "L[\\boldsymbol\\phi] = \\sum_{i=1}^{I}(y_{i}-\\text{f}[x_{i},\\boldsymbol\\phi])^2\n", "\\end{equation}\n", "\n", "where $(x_i,y_i)$ is an input/output training pair and $\\text{f}[\\bullet,\\boldsymbol\\phi]$ is the neural network with parameters $\\boldsymbol\\phi$.  The first term in the brackets is the ground truth output and the second term is the prediction of the model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "14d5II-TU46w"}, "outputs": [], "source": ["# Least squares function\n", "def least_squares_loss(y_train, y_predict):\n", "  # TODO Replace the line below to compute the sum of squared\n", "  # differences between the real values of y and the predicted values from the model f[x_i,phi]\n", "  # (see figure 2.2 of the book)\n", "  # you will need to use the function np.sum\n", "  loss = 0\n", "\n", "  return loss"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "o6GXjtRubZ2U"}, "outputs": [], "source": ["# Now lets define some parameters, run the neural network, and compute the loss\n", "theta_10 =  0.3 ; theta_11 = -1.0\n", "theta_20 = -1.0  ; theta_21 = 2.0\n", "theta_30 = -0.5  ; theta_31 = 0.65\n", "phi_0 = -0.3; phi_1 = 2.0; phi_2 = -1.0; phi_3 = 7.0\n", "\n", "# Define a range of input values\n", "x = np.arange(0,1,0.01)\n", "\n", "x_train = np.array([0.09291784,0.46809093,0.93089486,0.67612654,0.73441752,0.86847339,\\\n", "                   0.49873225,0.51083168,0.18343972,0.99380898,0.27840809,0.38028817,\\\n", "                   0.12055708,0.56715537,0.92005746,0.77072270,0.85278176,0.05315950,\\\n", "                   0.87168699,0.58858043])\n", "y_train = np.array([-0.15934537,0.18195445,0.451270150,0.13921448,0.09366691,0.30567674,\\\n", "                    0.372291170,0.40716968,-0.08131792,0.41187806,0.36943738,0.3994327,\\\n", "                    0.019062570,0.35820410,0.452564960,-0.0183121,0.02957665,-0.24354444, \\\n", "                    0.148038840,0.26824970])\n", "\n", "# We run the neural network for each of these input values\n", "y, pre_1, pre_2, pre_3, act_1, act_2, act_3, w_act_1, w_act_2, w_act_3 = \\\n", "    shallow_1_1_3(x, <PERSON><PERSON><PERSON>, phi_0,phi_1,phi_2,phi_3, theta_10, theta_11, theta_20, theta_21, theta_30, theta_31)\n", "# And then plot it\n", "plot_neural(x, y, pre_1, pre_2, pre_3, act_1, act_2, act_3, w_act_1, w_act_2, w_act_3, plot_all=True, x_data = x_train, y_data = y_train)\n", "\n", "# Run the neural network on the training data\n", "y_predict, *_ = shallow_1_1_3(x_train, ReLU, phi_0,phi_1,phi_2,phi_3, theta_10, theta_11, theta_20, theta_21, theta_30, theta_31)\n", "\n", "# Compute the least squares loss and print it out\n", "loss = least_squares_loss(y_train,y_predict)\n", "print('Your Loss = %3.3f, True value = 9.385'%(loss))\n", "\n", "# TODO.  Manipulate the parameters (by hand!) to make the function\n", "# fit the data better and try to reduce the loss to as small a number\n", "# as possible.  The best that I could do was 0.181\n", "# Tip... start by manipulating phi_0.\n", "# It's not that easy, so don't spend too much time on this!\n"]}], "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}