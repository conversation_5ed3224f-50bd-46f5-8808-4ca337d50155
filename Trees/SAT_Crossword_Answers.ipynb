{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyMplmIQrTkWc/1oVZK7PhOy", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/Trees/SAT_Crossword_Answers.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["<img src=\"data:image/svg+xml;base64,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\">"], "metadata": {"id": "QjHXD27ieTS-"}}, {"cell_type": "markdown", "source": ["# Crosswords with SAT\n", "\n", "The purpose of this Python notebook is to use investigate using SAT to find a valid arrangement of known answers in a crossword puzzle.\n", "\n", "You should have completed the notebook on SAT constructions before attempting this notebook.  Note:  this exercise is pretty hard.  Expect it to take a while!\n", "\n", "You can save a local copy of this notebook in your Google account and work through it in Colab (recommended) or you can download the notebook and run it locally using Jupy<PERSON> notebook or similar.\n", "\n", "Contact <NAME_EMAIL> if you find any mistakes or have any suggestions."], "metadata": {"id": "jtMs90veeZIn"}}, {"cell_type": "code", "source": ["# Install relevant packages\n", "!pip install z3-solver\n", "from z3 import *\n", "import numpy as np\n", "import time"], "metadata": {"id": "mF6ngqCses3n", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "68804cab-5138-4ed6-c87b-4d27717acc8d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: z3-solver in /usr/local/lib/python3.11/dist-packages (********)\n"]}]}, {"cell_type": "markdown", "source": ["First let's write some code to visualize a crossword problem.  We'll represent the crossword as a ndarray of integers where each integer represents a letter index and zero represents a blank spot.  "], "metadata": {"id": "3A5_7mByYrur"}}, {"cell_type": "code", "source": ["puzzle = ['ALCOVE NSEC MIC',\n", "          'LEANED ALTO ADO',\n", "          'LAVALAMPOON CON',\n", "          'ASSN  EKG GABLE',\n", "          '   DENTI MEMO  ',\n", "          ' AEOLIANHARPOON',\n", "          'MOANER SAX SKUA',\n", "          'ERS MVP TWI PTS',\n", "          'OTTO AUS ESPRIT',\n", "          'WAILINGWALLOON ',\n", "          '  NARA IDLES   ',\n", "          'REDYE UMA  ECHO',\n", "          'ARI FILMBUFFOON',\n", "          'JOE UTNE SLOPPY',\n", "          'ASS LEAR CORTEX']\n", "\n", "# Convert to a list of lists\n", "for i in range(len(puzzle)):\n", "  puzzle[i] = [char for char in puzzle[i]]\n", "\n", "# Represent the puzzle as integers in a grid\n", "puzzle_as_integers = np.zeros((len(puzzle), len(puzzle[0])), dtype=int)\n", "for i in range(len(puzzle)):\n", "  for j in range(len(puzzle[i])):\n", "    if puzzle[i][j] == ' ':\n", "      puzzle_as_integers[i][j] = 0\n", "    else:\n", "      puzzle_as_integers[i][j] = ord(puzzle[i][j]) - ord('A') + 1\n", "\n", "print(puzzle)\n", "print(puzzle_as_integers)"], "metadata": {"id": "cvGNbKkf-Qix", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "7ed1d16a-7cd8-4bdd-b790-26fdac322ab9"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[['A', 'L', 'C', 'O', 'V', 'E', ' ', 'N', 'S', 'E', 'C', ' ', 'M', 'I', 'C'], ['L', 'E', 'A', 'N', 'E', 'D', ' ', 'A', 'L', 'T', 'O', ' ', 'A', 'D', 'O'], ['L', 'A', 'V', 'A', 'L', 'A', 'M', 'P', 'O', 'O', 'N', ' ', 'C', 'O', 'N'], ['A', 'S', 'S', 'N', ' ', ' ', 'E', 'K', 'G', ' ', 'G', 'A', 'B', 'L', 'E'], [' ', ' ', ' ', 'D', 'E', 'N', 'T', 'I', ' ', 'M', 'E', 'M', 'O', ' ', ' '], [' ', 'A', 'E', 'O', 'L', 'I', 'A', 'N', 'H', 'A', 'R', 'P', 'O', 'O', 'N'], ['M', 'O', 'A', 'N', 'E', 'R', ' ', 'S', 'A', 'X', ' ', 'S', 'K', 'U', 'A'], ['E', 'R', 'S', ' ', 'M', 'V', 'P', ' ', 'T', 'W', 'I', ' ', 'P', 'T', 'S'], ['O', 'T', 'T', 'O', ' ', 'A', 'U', 'S', ' ', 'E', 'S', 'P', 'R', 'I', 'T'], ['W', 'A', 'I', 'L', 'I', 'N', 'G', 'W', 'A', 'L', 'L', 'O', 'O', 'N', ' '], [' ', ' ', 'N', 'A', 'R', 'A', ' ', 'I', 'D', 'L', 'E', 'S', ' ', ' ', ' '], ['R', 'E', 'D', 'Y', 'E', ' ', 'U', 'M', 'A', ' ', ' ', 'E', 'C', 'H', 'O'], ['A', 'R', 'I', ' ', 'F', 'I', 'L', 'M', 'B', 'U', 'F', 'F', 'O', 'O', 'N'], ['J', 'O', 'E', ' ', 'U', 'T', 'N', 'E', ' ', 'S', 'L', 'O', 'P', 'P', 'Y'], ['A', 'S', 'S', ' ', 'L', 'E', 'A', 'R', ' ', 'C', 'O', 'R', 'T', 'E', 'X']]\n", "[[ 1 12  3 15 22  5  0 14 19  5  3  0 13  9  3]\n", " [12  5  1 14  5  4  0  1 12 20 15  0  1  4 15]\n", " [12  1 22  1 12  1 13 16 15 15 14  0  3 15 14]\n", " [ 1 19 19 14  0  0  5 11  7  0  7  1  2 12  5]\n", " [ 0  0  0  4  5 14 20  9  0 13  5 13 15  0  0]\n", " [ 0  1  5 15 12  9  1 14  8  1 18 16 15 15 14]\n", " [13 15  1 14  5 18  0 19  1 24  0 19 11 21  1]\n", " [ 5 18 19  0 13 22 16  0 20 23  9  0 16 20 19]\n", " [15 20 20 15  0  1 21 19  0  5 19 16 18  9 20]\n", " [23  1  9 12  9 14  7 23  1 12 12 15 15 14  0]\n", " [ 0  0 14  1 18  1  0  9  4 12  5 19  0  0  0]\n", " [18  5  4 25  5  0 21 13  1  0  0  5  3  8 15]\n", " [ 1 18  9  0  6  9 12 13  2 21  6  6 15 15 14]\n", " [10 15  5  0 21 20 14  5  0 19 12 15 16 16 25]\n", " [ 1 19 19  0 12  5  1 18  0  3 15 18 20  5 24]]\n"]}]}, {"cell_type": "markdown", "source": ["Let's write a routine that draws this out nicely"], "metadata": {"id": "v7UbEiIjYdxj"}}, {"cell_type": "code", "source": ["def draw_crossword(puzzle_as_integers):\n", "\n", "  # Find number of rows and columns\n", "  n_rows = puzzle_as_integers.shape[0]\n", "  n_cols = puzzle_as_integers.shape[1]\n", "\n", "  # Draw the top row\n", "  print(\"╔\", end=\"\")\n", "  for i in range(n_cols-1):\n", "    print(\"═╤\", end=\"\")\n", "  print(\"═╗\")\n", "\n", "  for c_row in range(n_rows):\n", "    print(\"║\", end=\"\")\n", "    for c_col in range(n_cols):\n", "      if puzzle_as_integers[c_row][c_col] == 0:\n", "        print(u\"\\u2588\", end=\"\")  # Use block character for blank spaces\n", "      else:\n", "        print(chr(puzzle_as_integers[c_row][c_col] + ord('A') - 1), end=\"\")\n", "      if(c_col < n_cols-1):\n", "        print(\"│\", end=\"\")\n", "    print(\"║\")\n", "\n", "\n", "  # Draw the bottom row\n", "  print(\"╚\", end=\"\")\n", "  for i in range(n_cols-1):\n", "    print(\"═╧\", end=\"\")\n", "  print(\"═╝\")\n", "\n", "draw_crossword(puzzle_as_integers)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gdJakiT6TrIU", "outputId": "b89a05aa-a6b2-4fd1-ac77-60a435a30c91"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║A│L│C│O│V│E│█│N│S│E│C│█│M│I│C║\n", "║L│E│A│N│E│D│█│A│L│T│O│█│A│D│O║\n", "║L│A│V│A│L│A│M│P│O│O│N│█│C│O│N║\n", "║A│S│S│N│█│█│E│K│G│█│G│A│B│L│E║\n", "║█│█│█│D│E│N│T│I│█│M│E│M│O│█│█║\n", "║█│A│E│O│L│I│A│N│H│A│R│P│O│O│N║\n", "║M│O│A│N│E│R│█│S│A│X│█│S│K│U│A║\n", "║E│R│S│█│M│V│P│█│T│W│I│█│P│T│S║\n", "║O│T│T│O│█│A│U│S│█│E│S│P│R│I│T║\n", "║W│A│I│L│I│N│G│W│A│L│L│O│O│N│█║\n", "║█│█│N│A│R│A│█│I│D│L│E│S│█│█│█║\n", "║R│E│D│Y│E│█│U│M│A│█│█│E│C│H│O║\n", "║A│R│I│█│F│I│L│M│B│U│F│F│O│O│N║\n", "║J│O│E│█│U│T│N│E│█│S│L│O│P│P│Y║\n", "║A│S│S│█│L│E│A│R│█│C│O│R│T│E│X║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "markdown", "source": ["The goal of this notebook will be to take a set of words and create a crossword layout like this in an $n \\times n$ grid.  We'll start with just a small set of clues.  "], "metadata": {"id": "yDJLSLNtaSOL"}}, {"cell_type": "code", "source": ["words = ['JANE','AUSTEN','PRIDE','NOVEL','DARCY','SENSE','EMMA','ESTATE','BENNET','BATH']"], "metadata": {"id": "NkNMg4GuYt-h"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["This routine takes the words, the grid size and various sets of constraints (which we'll develop one at a time).  It then runs the solver and  displays the crossword."], "metadata": {"id": "NdMkEZVl_Dci"}}, {"cell_type": "code", "source": ["def solve_crossword (words, grid_size, add_constraint_set1, add_constraint_set2=None, add_constraint_set3=None, add_constraint_set4=None ):\n", "\n", "  # Fail if longest string length is not large enough to fit in grid\n", "  longest_string_length = max(len(word) for word in words)\n", "  if (longest_string_length > grid_size):\n", "    print(\"Grid too small, no solution\")\n", "    return ;\n", "\n", "  start_time = time.time()\n", "  # Set up the SAT solver\n", "  s = Solver()\n", "\n", "  # This is a dictionary indexed by the word itself that contains the possible start\n", "  # positions of that word, and whether the word is horizontal or vertical\n", "  # The number of possible positions depend on the grid size as the word cannot exceed\n", "  # grid.\n", "  placement_vars = {word: [[[z3.Bool(f'{word}_{orientation}_{y},{x}')\n", "                                for x in range(grid_size-len(word)+1 if orientation=='h' else grid_size )]\n", "                                for y in range(grid_size-len(word)+1 if orientation=='v' else grid_size )]\n", "                                for orientation in ['h', 'v']]\n", "                                for word in words}\n", "\n", "  # We will also define variables that indicate which letter is at which position\n", "  # There are 27 possible characters (26 letters and a blank)\n", "  # The variable x_i,j,k says that letter k is at position (i,j) in the grid\n", "  letter_posns = [[[ z3.Bool(\"x_{%d,%d,%d}\"%((i,j,k))) for k in range(0,27)] for j in range(0,grid_size) ] for i in range(0,grid_size) ]\n", "\n", "  # Add the first set of constraints\n", "  s = add_constraint_set1(s, placement_vars, letter_posns, words, grid_size)\n", "  # Add the second set of constraints if present\n", "  if add_constraint_set2 is not None:\n", "    s = add_constraint_set2(s, placement_vars, letter_posns, words, grid_size)\n", "  # Add the third set of constraints if present\n", "  if add_constraint_set3 is not None:\n", "    s = add_constraint_set3(s, placement_vars, letter_posns, words, grid_size)\n", "  # Add the fourth set of constraints if present\n", "  if add_constraint_set4 is not None:\n", "    s = add_constraint_set4(s, placement_vars, letter_posns, words, grid_size)\n", "\n", "  # Check if it's SAT (creates the model)\n", "  sat_result = s.check()\n", "  print(f\"Executed in {time.time()-start_time:.4f} seconds\")\n", "  print(sat_result)\n", "\n", "  # If it is then draw crossword, otherwise return\n", "  if sat_result == z3.sat:\n", "      result = s.model()\n", "      # Retrieve the letter position variables in the solution as [0,1] values\n", "      x_vals = np.array([[[int(bool(result[z3.Bool(\"x_{%d,%d,%d}\" % (i, j, k))])) for k in range(0,27)] for j in range(0,grid_size) ] for i in range(0,grid_size) ] )\n", "\n", "      # Find the position of the true value -- this is now a 2D grid with a 0 where there is a space and a value 1-26 representing a letter\n", "      solution = np.argmax(x_vals, axis=2)\n", "      # Draw the solution\n", "      draw_crossword(solution)\n", "  else:\n", "      print(\"No solution\")"], "metadata": {"id": "Ijuo4HdSefPk"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Here's a couple of helpful routines that we can make use of"], "metadata": {"id": "BBD_gK-q_AaT"}}, {"cell_type": "code", "source": ["# Takes a list of z3.Bool variables and returns constraints\n", "# ensuring that there is exactly one true\n", "def exactly_one(x):\n", "  return PbEq([(i,1) for i in x],1)\n", "\n", "# Converts a word in capital letters to its indices so 'ABD' becomes [1,2,4]\n", "def letter_to_index(word):\n", "  return [ord(char) - ord('A') + 1 for char in word]"], "metadata": {"id": "O5p-8Ul6cvsk"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Let's work on the first set of constraints.  \n", "\n", "\n", "1.   Each word can only appear at one valid position\n", "2.   Each position in the grid can have only a single letter present\n", "3.   The letters implied by the word positions must agree where the words overlap\n", "\n"], "metadata": {"id": "VT-QNf-FHClF"}}, {"cell_type": "code", "source": ["def add_constraint_set1(s, placement_vars, letter_posns, words, grid_size):\n", "    # Constraint 1: Each word can only be placed in exactly one position\n", "  for word in words:\n", "    # Flatten the possible positions into a list\n", "    flat_list = [item for sublist1 in placement_vars[word] for sublist2 in sublist1 for item in sublist2]\n", "    # Add the constraint that exactly one must be true\n", "    s.add(exactly_one(flat_list))\n", "\n", "  # Constraint 2: Each grid position can only have one letter present\n", "  for i in range(0,grid_size):\n", "    for j in range(0,grid_size):\n", "      s.add(exactly_one(letter_posns[i][j]))\n", "\n", "  # Constraint 3: If a word is in a given position and orientation, the letters at the\n", "  # appropriate grid positions must correspond (uses the routine letter_to_index() defined above)\n", "  for word in words:\n", "    for i in range(0,grid_size):\n", "      for j in range(0,grid_size-len(word)+1):\n", "        for letter_index in range(0,len(word)):\n", "          s.add(Implies(placement_vars[word][0][i][j], letter_posns[i][j+letter_index][letter_to_index(word)[letter_index]]))\n", "    for i in range(0,grid_size-len(word)+1):\n", "      for j in range(0,grid_size):\n", "        for letter_index in range(0,len(word)):\n", "          s.add(Implies(placement_vars[word][1][i][j], letter_posns[i+letter_index][j][letter_to_index(word)[letter_index]]))\n", "\n", "  return s"], "metadata": {"id": "NdjsISBCFr6K"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Let's test this routine so far\n", "solve_crossword(words, 10, add_constraint_set1)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ksTQlgvnHiqK", "outputId": "d453c0b1-9507-4adf-9336-f38e4e6ff5ad"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║A│█│█│E│S│T│A│T│E│S║\n", "║U│B│E│N│N│E│T│O│J│C║\n", "║S│F│S│V│E│Z│A│Z│A│G║\n", "║T│E│H│X│V│B│R│D│N│T║\n", "║E│S│E│N│S│E│C│A│E│A║\n", "║N│T│O│█│█│K│Y│R│█│█║\n", "║█│█│P│R│I│D│E│C│B│B║\n", "║B│Z│A│B│B│J│I│Y│Q│█║\n", "║J│B│A│T│H│B│E│M│M│A║\n", "║N│O│V│E│L│A│E│B│U│S║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "markdown", "source": ["You can see that the words are all in there, but we don't have blank spaces where we should.  We need to add two further constraints to improve matters\n", "\n", "1. Horizontal words must have a blank space or an edge to the left and right of their positions.  Vertical words must have a blank space or and edge above or below their positions.\n", "2. Any position that is not part of a word should be blank\n", "\n"], "metadata": {"id": "SlzqglXAH1wG"}}, {"cell_type": "code", "source": ["def add_constraint_set2(s, placement_vars, letter_posns, words, grid_size):\n", "  # Constraint 1:  Horizontal words must either start in the first column or have a 0 to their left\n", "  #                Horizontal words must either finish in the last column of have a 0 to their right\n", "  #                Vertical words must either start in the first row or have a 0 above them\n", "  #                Vertical words must either end in the last row of have a 0 below them\n", "  for word in words:\n", "    # Horizontal words\n", "    for i in range(grid_size):\n", "        for j in range(1, grid_size - len(word)+1 ):\n", "            # Check for border or blank square before the word starts\n", "            s.add(Implies(placement_vars[word][0][i][j], letter_posns[i][j-1][0]))\n", "            s.add(Implies(placement_vars[word][0][i][j-1], letter_posns[i][j+len(word)-1][0]))\n", "\n", "    # Vertical words\n", "    for i in range(1,grid_size - len(word)+1 ):\n", "        for j in range(grid_size):\n", "            # Check blank square before the word starts\n", "            s.add(Implies(placement_vars[word][1][i][j], letter_posns[i-1][j][0]))\n", "            s.add(Implies(placement_vars[word][1][i-1][j], letter_posns[i+len(word)-1][j][0]))\n", "\n", "  # Constraint 2:  Any position in the crossword grid that is not part of a word must be a blank space\n", "  #                This stops random characters appearing outside the solution\n", "  for i in range(grid_size):\n", "      for j in range(grid_size):\n", "          # Create a list of placement variables that affect the current square\n", "          relevant_placements = []\n", "          for word in words:\n", "              # Horizontal words\n", "              for col in range(grid_size - len(word) + 1):\n", "                  if j >= col and j < col + len(word):\n", "                      relevant_placements.append(placement_vars[word][0][i][col])\n", "\n", "              # Vertical words\n", "              for row in range(grid_size - len(word) + 1):\n", "                  if i >= row and i < row + len(word):\n", "                      relevant_placements.append(placement_vars[word][1][row][j])\n", "\n", "\n", "          # If none of the relevant placements are true, the square must be blank\n", "          s.add(Implies(Not(Or(relevant_placements)), letter_posns[i][j][0]))\n", "\n", "  return s"], "metadata": {"id": "7iHXNe_0F7ej"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Let's test this routine so far\n", "solve_crossword(words, 10, add_constraint_set1, add_constraint_set2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lPEcYEIbItHp", "outputId": "b6a560d2-9962-4be1-bc8b-86b853c77c64"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│P│█│█│█│E│█│█│█║\n", "║█│█│R│A│█│B│S│█│█│█║\n", "║█│N│I│U│█│E│T│█│S│D║\n", "║B│O│D│S│J│N│A│E│E│A║\n", "║A│V│E│T│A│N│T│M│N│R║\n", "║T│E│█│E│N│E│E│M│S│C║\n", "║H│L│█│N│E│T│█│A│E│Y║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "markdown", "source": ["That's an improvement, but it's not perfect; every letter is now part of either a horizontal or a vertical word.  However, when there are several vertical words adjacent to each other and we read horizontally across these words, we get nonsense.  We can fix this by adding another constraint:\n", "\n", "*   If a letter is in a horizontal word, it is either inside a vertical word as well *OR* it has a blank square (or the edge of the grid) above and below it.\n", "*   If a letter is in a vertical word, it is either inside a horizontal word as well *OR* it has a blank square (or the edge of the grid) to the left and the right of it."], "metadata": {"id": "2X4guZwZI_JW"}}, {"cell_type": "code", "source": ["def add_constraint_set3(s, placement_vars, letter_posns, words, grid_size):\n", "  # Constraint 1:   If a letter is in a horizontal word, it either\n", "  #                     -- is inside a vertical word as well\n", "  #                     -- has a blank (or edge) above and below it\n", "  #                 If a letter in a vertical word exists, it is either\n", "  #                     -- is inside a horizontal word too\n", "  #                      -- has a blank (or edge) to the left and to the right of it.\n", "  for i in range(0,grid_size):\n", "      for j in range(0,grid_size):\n", "          relevant_placements_horz = []\n", "          relevant_placements_vert = []\n", "          for word in words:\n", "            for j2 in range (max(0,j-len(word)+1), min(j+1,grid_size-len(word)+1)):\n", "                relevant_placements_horz.append(placement_vars[word][0][i][j2])\n", "            for i2 in range(max(0,i-len(word)+1), min(i+1,grid_size-len(word)+1)):\n", "                relevant_placements_vert.append(placement_vars[word][1][i2][j])\n", "          in_horizontal_word = Or(relevant_placements_horz)\n", "          in_vertical_word = Or(relevant_placements_vert)\n", "\n", "          if(i == 0):\n", "            above_and_below_are_blank = letter_posns[i+1][j][0]\n", "          else:\n", "            if(i == grid_size-1):\n", "              above_and_below_are_blank = letter_posns[i-1][j][0]\n", "            else:\n", "              above_and_below_are_blank = And(letter_posns[i-1][j][0],letter_posns[i+1][j][0])\n", "\n", "          if(j == 0):\n", "            left_and_right_are_blank = letter_posns[i][j+1][0]\n", "          else:\n", "            if(j == grid_size-1):\n", "              left_and_right_are_blank = letter_posns[i][j-1][0]\n", "            else:\n", "              left_and_right_are_blank = And(letter_posns[i][j-1][0],letter_posns[i][j+1][0])\n", "          s.add(Implies(in_horizontal_word, Or(in_vertical_word, above_and_below_are_blank)))\n", "          s.add(Implies(in_vertical_word, Or(in_horizontal_word, left_and_right_are_blank)))\n", "\n", "  return s"], "metadata": {"id": "4LSgimAjGQdT"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Let's see how this improves things\n", "solve_crossword(words, 10, add_constraint_set1, add_constraint_set2, add_constraint_set3)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8HHqCWMzKCTL", "outputId": "76f583ca-fcd5-4f67-9373-8a29596564ff"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║D│A│R│C│Y│█│E│M│M│A║\n", "║█│█│█│█│█│█│S│█│█│█║\n", "║█│B│E│N│N│E│T│█│█│█║\n", "║█│█│█│█│█│█│A│█│█│█║\n", "║J│A│N│E│█│█│T│█│█│█║\n", "║█│█│█│█│█│S│E│N│S│E║\n", "║N│O│V│E│L│█│█│█│█│█║\n", "║█│█│█│█│█│P│R│I│D│E║\n", "║B│A│T│H│█│█│█│█│█│█║\n", "║█│█│█│█│A│U│S│T│E│N║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "markdown", "source": ["This looks like it's working better, but we now have the problem that words do not all connect to each other.  Hence, we have to add a final constraint that all of the letters are connected.\n", "\n", "First we form an $N\\times N$ adjacency matrix which the value is true if word $i$ intersects with word $j$.  Then we use the 'is_fully_connected' construction that we developed in the notebook on SAT constructions."], "metadata": {"id": "R69urJyN9wlc"}}, {"cell_type": "code", "source": ["def is_fully_connected(s, adjacency):\n", "  # Size of the adjacency matrix\n", "  n_components = len(adjacency)\n", "  # We'll construct a N x N x log[N] array of variables\n", "  # The NxN variables in the first layer represent A, the variables in the second layer represent B and so on\n", "  n_layers = math.ceil(math.log(n_components,2))+1\n", "  connected = [[[ z3.Bool(\"conn_{%d,%d,%d}\"%((i,j,n))) for n in range(0, n_layers)] for j in range(0, n_components) ] for i in range(0, n_components) ]\n", "\n", "  # Constraint 1\n", "  # The value in the top layer of the connected structure is equal to the adjacency matrix\n", "  for i in range(n_components):\n", "    for j in range(n_components):\n", "      s.add(connected[i][j][0]==adjacency[i][j])\n", "\n", "  # Constraint 2\n", "  # Value at position [i,j] in layer n is value at position [i,j] of the binary matrix product of layer n-1 with itself\n", "  for n in range(1,n_layers):\n", "    for i in range(n_components):\n", "      for j in range(n_components):\n", "        matrix_entry_ij = False\n", "        for k in range(n_components):\n", "          matrix_entry_ij = Or(matrix_entry_ij, And(connected[i][k][n-1],connected[k][j][n-1]))\n", "        s.add(connected[i][j][n]==matrix_entry_ij)\n", "\n", "  # Constraint 3 -- any row of column of the matrix should be full of ones at the end (everything is connected)\n", "  for i in range(n_components):\n", "    s.add(connected[i][0][n_layers-1])\n", "\n", "  return s"], "metadata": {"id": "2uPPXENwLugr"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Helper routine that returns true if the current word is at position (i,j) in the grid\n", "def word_at_position_ij(i,j, placement_vars, word, grid_size):\n", "\n", "    relevant_placements = [] ;\n", "    # Deal with horizontal words first\n", "    for horz_pos in range(np.max([0, j-len(word)+1]), np.min([j+1, grid_size-len(word)+1])):\n", "      # First the horizontal words\n", "      relevant_placements.append(placement_vars[word][0][i][horz_pos])\n", "    # Then the vertical words\n", "    for vert_pos in range(np.max([0, i-len(word)+1]), np.min([i+1, grid_size-len(word)+1])):\n", "      relevant_placements.append(placement_vars[word][1][vert_pos][j])\n", "\n", "    return Or(relevant_placements) ;"], "metadata": {"id": "FhJPmmAOV3AS"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def add_constraint_set4(s, placement_vars, letter_posns, words, grid_size):\n", "  # First lets create a new variable that represents the adjacency matrix of the words\n", "  adjacency = [[ z3.Bool(\"adj_{%d,%d}\"%((i,j))) for j in range(0, len(words)) ] for i in range(0, len(words)) ]\n", "\n", "  # Run through each word\n", "  for c_w1 in range(len(words)):\n", "    for c_w2 in range(c_w1, len(words)):\n", "      # If indices are the same then adjacency is true\n", "      if c_w1 == c_w2:\n", "        s.add(adjacency[c_w1][c_w2])\n", "\n", "      word1 = words[c_w1]\n", "      word2 = words[c_w2]\n", "      words_intersect = False\n", "      # Run through each position in the grid\n", "      for i in range(grid_size):\n", "        for j in range(grid_size):\n", "            # Words interset if both at a given position for any position\n", "            words_intersect = Or(words_intersect, And(word_at_position_ij(i,j, placement_vars, word1, grid_size), word_at_position_ij(i,j, placement_vars, word2, grid_size)))\n", "      # Set value and symmetric value of adjacency matrix\n", "      s.add(adjacency[c_w1][c_w2] == words_intersect)\n", "      s.add(adjacency[c_w2][c_w1] == adjacency[c_w1][c_w2])\n", "\n", "  # Add the constraint that the adjacency matrix must be fully connected\n", "  s = is_fully_connected(s, adjacency)\n", "  return s"], "metadata": {"id": "Xmzv8g_-RIid"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Let's see how this improves things (took 32 seconds to run for me, but might be longer)\n", "solve_crossword(words, 11, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M7pPhYTfaLEd", "outputId": "fe616dd9-caca-4aea-e94c-fefaf5fe0ec4"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Executed in 32.3613 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "║J│█│█│█│█│█│█│█│█│█│█║\n", "║A│█│█│B│█│█│█│█│█│█│P║\n", "║N│O│V│E│L│█│█│█│█│█│R║\n", "║E│█│█│N│█│█│█│B│█│█│I║\n", "║█│█│█│N│█│S│█│A│█│█│D║\n", "║D│█│█│E│█│E│S│T│A│T│E║\n", "║A│U│S│T│E│N│█│H│█│█│█║\n", "║R│█│█│█│█│S│█│█│█│█│█║\n", "║C│█│█│█│█│E│M│M│A│█│█║\n", "║Y│█│█│█│█│█│█│█│█│█│█║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "code", "source": ["# Now let's see what the smallest grid we can fit this in is.  The longest word is 6 letters, so can't be shorter than this\n", "solve_crossword(words, 10, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)\n", "solve_crossword(words, 9, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)\n", "solve_crossword(words, 8, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)\n", "solve_crossword(words, 7, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)\n", "solve_crossword(words, 6, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OFLdFlaP6g7L", "outputId": "d7d8b512-84ef-4b48-a9f9-9912cd01622b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Executed in 139.9588 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│█│█│█│█│B│█║\n", "║█│█│█│█│█│J│█│█│E│█║\n", "║D│█│█│█│█│A│█│█│N│█║\n", "║A│U│S│T│E│N│█│█│N│█║\n", "║R│█│█│█│S│E│N│S│E│█║\n", "║C│█│█│█│T│█│O│█│T│█║\n", "║Y│█│█│█│A│█│V│█│█│B║\n", "║█│█│█│█│T│█│E│M│M│A║\n", "║P│R│I│D│E│█│L│█│█│T║\n", "║█│█│█│█│█│█│█│█│█│H║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n", "Executed in 261.2188 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│B│A│T│H│█║\n", "║P│█│█│█│E│█│█│█│█║\n", "║R│█│A│█│N│█│█│S│█║\n", "║I│█│U│█│N│O│V│E│L║\n", "║D│█│S│█│E│█│█│N│█║\n", "║E│S│T│A│T│E│█│S│█║\n", "║█│█│E│█│█│M│█│E│█║\n", "║J│A│N│E│█│M│█│█│█║\n", "║█│█│█│█│D│A│R│C│Y║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╝\n", "Executed in 199.8628 seconds\n", "unsat\n", "No solution\n", "Executed in 4.0513 seconds\n", "unsat\n", "No solution\n", "Executed in 1.7816 seconds\n", "unsat\n", "No solution\n"]}]}, {"cell_type": "markdown", "source": ["These were some random words that I chose for an example.  Here are some real words from an NY Times mini puzzle.  You can see that it recovers the minimal possible solution correctly."], "metadata": {"id": "kFQs0iZXmipH"}}, {"cell_type": "code", "source": ["words2 = ['GUM','TAB','ERA','END','IRA','MAP','TIMWALZ','ONE','ELI','COATS','POPHITS', \\\n", "          'GOT', 'PIE','BIZ','SPA','ALLSTAR','UNICORN','MEMOPAD','WAH','TEATIME']\n", "solve_crossword(words2, 7, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sDmahKMoldGi", "outputId": "0cf09d11-d14f-431b-e6e8-c08a4e7ea201"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Executed in 13.5120 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╗\n", "║G│U│M│█│T│A│B║\n", "║O│N│E│█│E│L│I║\n", "║T│I│M│W│A│L│Z║\n", "║█│C│O│A│T│S│█║\n", "║P│O│P│H│I│T│S║\n", "║I│R│A│█│M│A│P║\n", "║E│N│D│█│E│R│A║\n", "╚═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "markdown", "source": ["Further work for keeners:\n", "\n", "1. Technically, we should also put in a constraint to stop two words starting in the same place.  For example, the current solver would allow the words 'EAT' and 'EATEN' to start in the same place and overlap.  Add this constraint.\n", "\n", "2. As proposed, this is fairly impractical to help make crosswords.  It would be more useful to have a superset of answers and allow the SAT solver to choose from these to make a sensible crossword.  You would then ask the SAT solver to build an crossword of size $N$ that included at least $W$ words.  Add this feature.\n"], "metadata": {"id": "c2ZiS3OgzHXW"}}]}