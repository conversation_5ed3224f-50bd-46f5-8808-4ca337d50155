{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyMB6nkfgiGNF9TB3L9/dTxZ", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/Trees/SAT_Crossword.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["<img src=\"data:image/svg+xml;base64,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\">"], "metadata": {"id": "QjHXD27ieTS-"}}, {"cell_type": "markdown", "source": ["# Crosswords with SAT\n", "\n", "The purpose of this Python notebook is to use investigate using SAT to find a valid arrangement of known answers in a crossword puzzle.\n", "\n", "You should have completed the notebook on SAT constructions before attempting this notebook.  Note:  this exercise is pretty hard.  Expect it to take a while!\n", "\n", "Work through the cells below, running each cell in turn. In various places you will see the words \"TODO\". Follow the instructions at these places and write code to complete the functions.\n", "\n", "You can save a local copy of this notebook in your Google account and work through it in Colab (recommended) or you can download the notebook and run it locally using Jupy<PERSON> notebook or similar. If you are using CoLab, we recommend that turn off AI autocomplete (under cog icon in top-right corner), which will give you the answers and defeat the purpose of the exercise.\n", "\n", "A fully working version of this notebook with the complete answers can be found [here](https://github.com/udlbook/udlbook/blob/main/Trees/SAT_Crossword_Answers.ipynb).\n", "\n", "Contact <NAME_EMAIL> if you find any mistakes or have any suggestions."], "metadata": {"id": "jtMs90veeZIn"}}, {"cell_type": "code", "source": ["# Install relevant packages\n", "!pip install z3-solver\n", "from z3 import *\n", "import numpy as np\n", "import time"], "metadata": {"id": "mF6ngqCses3n", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "9325fa59-8ad2-4cea-acd1-16843db6b19a"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting z3-solver\n", "  Downloading z3_solver-********-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (602 bytes)\n", "Downloading z3_solver-********-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (29.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m29.5/29.5 MB\u001b[0m \u001b[31m27.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: z3-solver\n", "Successfully installed z3-solver-********\n"]}]}, {"cell_type": "markdown", "source": ["First let's write some code to visualize a crossword problem.  We'll represent the crossword as a ndarray of integers where each integer represents a letter index and zero represents a blank spot.  "], "metadata": {"id": "3A5_7mByYrur"}}, {"cell_type": "code", "source": ["puzzle = ['ALCOVE NSEC MIC',\n", "          'LEANED ALTO ADO',\n", "          'LAVALAMPOON CON',\n", "          'ASSN  EKG GABLE',\n", "          '   DENTI MEMO  ',\n", "          ' AEOLIANHARPOON',\n", "          'MOANER SAX SKUA',\n", "          'ERS MVP TWI PTS',\n", "          'OTTO AUS ESPRIT',\n", "          'WAILINGWALLOON ',\n", "          '  NARA IDLES   ',\n", "          'REDYE UMA  ECHO',\n", "          'ARI FILMBUFFOON',\n", "          'JOE UTNE SLOPPY',\n", "          'ASS LEAR CORTEX']\n", "\n", "# Convert to a list of lists\n", "for i in range(len(puzzle)):\n", "  puzzle[i] = [char for char in puzzle[i]]\n", "\n", "# Represent the puzzle as integers in a grid\n", "puzzle_as_integers = np.zeros((len(puzzle), len(puzzle[0])), dtype=int)\n", "for i in range(len(puzzle)):\n", "  for j in range(len(puzzle[i])):\n", "    if puzzle[i][j] == ' ':\n", "      puzzle_as_integers[i][j] = 0\n", "    else:\n", "      puzzle_as_integers[i][j] = ord(puzzle[i][j]) - ord('A') + 1\n", "\n", "print(puzzle)\n", "print(puzzle_as_integers)"], "metadata": {"id": "cvGNbKkf-Qix", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "6fb16e40-886c-4633-c768-11f5a6178aa8"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[['A', 'L', 'C', 'O', 'V', 'E', ' ', 'N', 'S', 'E', 'C', ' ', 'M', 'I', 'C'], ['L', 'E', 'A', 'N', 'E', 'D', ' ', 'A', 'L', 'T', 'O', ' ', 'A', 'D', 'O'], ['L', 'A', 'V', 'A', 'L', 'A', 'M', 'P', 'O', 'O', 'N', ' ', 'C', 'O', 'N'], ['A', 'S', 'S', 'N', ' ', ' ', 'E', 'K', 'G', ' ', 'G', 'A', 'B', 'L', 'E'], [' ', ' ', ' ', 'D', 'E', 'N', 'T', 'I', ' ', 'M', 'E', 'M', 'O', ' ', ' '], [' ', 'A', 'E', 'O', 'L', 'I', 'A', 'N', 'H', 'A', 'R', 'P', 'O', 'O', 'N'], ['M', 'O', 'A', 'N', 'E', 'R', ' ', 'S', 'A', 'X', ' ', 'S', 'K', 'U', 'A'], ['E', 'R', 'S', ' ', 'M', 'V', 'P', ' ', 'T', 'W', 'I', ' ', 'P', 'T', 'S'], ['O', 'T', 'T', 'O', ' ', 'A', 'U', 'S', ' ', 'E', 'S', 'P', 'R', 'I', 'T'], ['W', 'A', 'I', 'L', 'I', 'N', 'G', 'W', 'A', 'L', 'L', 'O', 'O', 'N', ' '], [' ', ' ', 'N', 'A', 'R', 'A', ' ', 'I', 'D', 'L', 'E', 'S', ' ', ' ', ' '], ['R', 'E', 'D', 'Y', 'E', ' ', 'U', 'M', 'A', ' ', ' ', 'E', 'C', 'H', 'O'], ['A', 'R', 'I', ' ', 'F', 'I', 'L', 'M', 'B', 'U', 'F', 'F', 'O', 'O', 'N'], ['J', 'O', 'E', ' ', 'U', 'T', 'N', 'E', ' ', 'S', 'L', 'O', 'P', 'P', 'Y'], ['A', 'S', 'S', ' ', 'L', 'E', 'A', 'R', ' ', 'C', 'O', 'R', 'T', 'E', 'X']]\n", "[[ 1 12  3 15 22  5  0 14 19  5  3  0 13  9  3]\n", " [12  5  1 14  5  4  0  1 12 20 15  0  1  4 15]\n", " [12  1 22  1 12  1 13 16 15 15 14  0  3 15 14]\n", " [ 1 19 19 14  0  0  5 11  7  0  7  1  2 12  5]\n", " [ 0  0  0  4  5 14 20  9  0 13  5 13 15  0  0]\n", " [ 0  1  5 15 12  9  1 14  8  1 18 16 15 15 14]\n", " [13 15  1 14  5 18  0 19  1 24  0 19 11 21  1]\n", " [ 5 18 19  0 13 22 16  0 20 23  9  0 16 20 19]\n", " [15 20 20 15  0  1 21 19  0  5 19 16 18  9 20]\n", " [23  1  9 12  9 14  7 23  1 12 12 15 15 14  0]\n", " [ 0  0 14  1 18  1  0  9  4 12  5 19  0  0  0]\n", " [18  5  4 25  5  0 21 13  1  0  0  5  3  8 15]\n", " [ 1 18  9  0  6  9 12 13  2 21  6  6 15 15 14]\n", " [10 15  5  0 21 20 14  5  0 19 12 15 16 16 25]\n", " [ 1 19 19  0 12  5  1 18  0  3 15 18 20  5 24]]\n"]}]}, {"cell_type": "markdown", "source": ["Let's write a routine that draws this out nicely"], "metadata": {"id": "v7UbEiIjYdxj"}}, {"cell_type": "code", "source": ["def draw_crossword(puzzle_as_integers):\n", "\n", "  # Find number of rows and columns\n", "  n_rows = puzzle_as_integers.shape[0]\n", "  n_cols = puzzle_as_integers.shape[1]\n", "\n", "  # Draw the top row\n", "  print(\"╔\", end=\"\")\n", "  for i in range(n_cols-1):\n", "    print(\"═╤\", end=\"\")\n", "  print(\"═╗\")\n", "\n", "  for c_row in range(n_rows):\n", "    print(\"║\", end=\"\")\n", "    for c_col in range(n_cols):\n", "      if puzzle_as_integers[c_row][c_col] == 0:\n", "        print(u\"\\u2588\", end=\"\")  # Use block character for blank spaces\n", "      else:\n", "        print(chr(puzzle_as_integers[c_row][c_col] + ord('A') - 1), end=\"\")\n", "      if(c_col < n_cols-1):\n", "        print(\"│\", end=\"\")\n", "    print(\"║\")\n", "\n", "\n", "  # Draw the bottom row\n", "  print(\"╚\", end=\"\")\n", "  for i in range(n_cols-1):\n", "    print(\"═╧\", end=\"\")\n", "  print(\"═╝\")\n", "\n", "draw_crossword(puzzle_as_integers)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gdJakiT6TrIU", "outputId": "aa8f182f-ad60-47a6-e204-8d88c55e0501"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║A│L│C│O│V│E│█│N│S│E│C│█│M│I│C║\n", "║L│E│A│N│E│D│█│A│L│T│O│█│A│D│O║\n", "║L│A│V│A│L│A│M│P│O│O│N│█│C│O│N║\n", "║A│S│S│N│█│█│E│K│G│█│G│A│B│L│E║\n", "║█│█│█│D│E│N│T│I│█│M│E│M│O│█│█║\n", "║█│A│E│O│L│I│A│N│H│A│R│P│O│O│N║\n", "║M│O│A│N│E│R│█│S│A│X│█│S│K│U│A║\n", "║E│R│S│█│M│V│P│█│T│W│I│█│P│T│S║\n", "║O│T│T│O│█│A│U│S│█│E│S│P│R│I│T║\n", "║W│A│I│L│I│N│G│W│A│L│L│O│O│N│█║\n", "║█│█│N│A│R│A│█│I│D│L│E│S│█│█│█║\n", "║R│E│D│Y│E│█│U│M│A│█│█│E│C│H│O║\n", "║A│R│I│█│F│I│L│M│B│U│F│F│O│O│N║\n", "║J│O│E│█│U│T│N│E│█│S│L│O│P│P│Y║\n", "║A│S│S│█│L│E│A│R│█│C│O│R│T│E│X║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "markdown", "source": ["The goal of this notebook will be to take a set of words and create a crossword layout like this in an $n \\times n$ grid.  We'll start with just a small set of clues.  "], "metadata": {"id": "yDJLSLNtaSOL"}}, {"cell_type": "code", "source": ["words = ['JANE','AUSTEN','PRIDE','NOVEL','DARCY','SENSE','EMMA','ESTATE','BENNET','BATH']"], "metadata": {"id": "NkNMg4GuYt-h"}, "execution_count": 4, "outputs": []}, {"cell_type": "markdown", "source": ["This routine takes the words, the grid size and various sets of constraints (which we'll develop one at a time).  It then runs the solver and  displays the crossword."], "metadata": {"id": "NdMkEZVl_Dci"}}, {"cell_type": "code", "source": ["def solve_crossword (words, grid_size, add_constraint_set1, add_constraint_set2=None, add_constraint_set3=None, add_constraint_set4=None ):\n", "\n", "  # Fail if longest string length is not large enough to fit in grid\n", "  longest_string_length = max(len(word) for word in words)\n", "  if (longest_string_length > grid_size):\n", "    print(\"Grid too small, no solution\")\n", "    return ;\n", "\n", "  start_time = time.time()\n", "  # Set up the SAT solver\n", "  s = Solver()\n", "\n", "  # This is a dictionary indexed by the word itself that contains the possible start\n", "  # positions of that word, and whether the word is horizontal or vertical\n", "  # The number of possible positions depend on the grid size as the word cannot exceed\n", "  # grid.\n", "  placement_vars = {word: [[[z3.Bool(f'{word}_{orientation}_{y},{x}')\n", "                                for x in range(grid_size-len(word)+1 if orientation=='h' else grid_size )]\n", "                                for y in range(grid_size-len(word)+1 if orientation=='v' else grid_size )]\n", "                                for orientation in ['h', 'v']]\n", "                                for word in words}\n", "\n", "  # We will also define variables that indicate which letter is at which position\n", "  # There are 27 possible characters (26 letters and a blank)\n", "  # The variable x_i,j,k says that letter k is at position (i,j) in the grid\n", "  letter_posns = [[[ z3.Bool(\"x_{%d,%d,%d}\"%((i,j,k))) for k in range(0,27)] for j in range(0,grid_size) ] for i in range(0,grid_size) ]\n", "\n", "  # Add the first set of constraints\n", "  s = add_constraint_set1(s, placement_vars, letter_posns, words, grid_size)\n", "  # Add the second set of constraints if present\n", "  if add_constraint_set2 is not None:\n", "    s = add_constraint_set2(s, placement_vars, letter_posns, words, grid_size)\n", "  # Add the third set of constraints if present\n", "  if add_constraint_set3 is not None:\n", "    s = add_constraint_set3(s, placement_vars, letter_posns, words, grid_size)\n", "  # Add the fourth set of constraints if present\n", "  if add_constraint_set4 is not None:\n", "    s = add_constraint_set4(s, placement_vars, letter_posns, words, grid_size)\n", "\n", "  # Check if it's SAT (creates the model)\n", "  sat_result = s.check()\n", "  print(f\"Executed in {time.time()-start_time:.4f} seconds\")\n", "  print(sat_result)\n", "\n", "  # If it is then draw crossword, otherwise return\n", "  if sat_result == z3.sat:\n", "      result = s.model()\n", "      # Retrieve the letter position variables in the solution as [0,1] values\n", "      x_vals = np.array([[[int(bool(result[z3.Bool(\"x_{%d,%d,%d}\" % (i, j, k))])) for k in range(0,27)] for j in range(0,grid_size) ] for i in range(0,grid_size) ] )\n", "\n", "      # Find the position of the true value -- this is now a 2D grid with a 0 where there is a space and a value 1-26 representing a letter\n", "      solution = np.argmax(x_vals, axis=2)\n", "      # Draw the solution\n", "      draw_crossword(solution)\n", "  else:\n", "      print(\"No solution\")"], "metadata": {"id": "Ijuo4HdSefPk"}, "execution_count": 5, "outputs": []}, {"cell_type": "markdown", "source": ["Here's a couple of helpful routines that we can make use of"], "metadata": {"id": "BBD_gK-q_AaT"}}, {"cell_type": "code", "source": ["# Takes a list of z3.Bool variables and returns constraints\n", "# ensuring that there is exactly one true\n", "def exactly_one(x):\n", "  return PbEq([(i,1) for i in x],1)\n", "\n", "# Converts a word in capital letters to its indices so 'ABD' becomes [1,2,4]\n", "def letter_to_index(word):\n", "  return [ord(char) - ord('A') + 1 for char in word]"], "metadata": {"id": "O5p-8Ul6cvsk"}, "execution_count": 6, "outputs": []}, {"cell_type": "markdown", "source": ["Let's work on the first set of constraints.  \n", "\n", "\n", "1.   Each word can only appear at one valid position\n", "2.   Each position in the grid can have only a single letter present\n", "3.   The letters implied by the word positions must agree where the words overlap\n", "\n"], "metadata": {"id": "VT-QNf-FHClF"}}, {"cell_type": "code", "source": ["def add_constraint_set1(s, placement_vars, letter_posns, words, grid_size):\n", "  # Constraint 1: Each word can only be placed in exactly one position\n", "  for word in words:\n", "    # TODO implement this constraint\n", "    # Replace these lines\n", "    print(placement_vars[word]) # Will help you understand what to do!\n", "    s.add(placement_vars[word][0][0][0])\n", "\n", "  # Constraint 2: Each grid position can only have one letter present\n", "  for i in range(0,grid_size):\n", "    for j in range(0,grid_size):\n", "      #TODO implement this constraint\n", "      # Replace this line\n", "      s.add(letter_posns[0][0][0])\n", "\n", "  # Constraint 3: If a word is in a given position and orientation, the letters at the\n", "  # appropriate grid positions must correspond (uses the routine letter_to_index() defined above)\n", "  for word in words:\n", "    for i in range(0,grid_size):\n", "      # We'll do the horizontal words for you.  Read this code closely.\n", "      for j in range(0,grid_size-len(word)+1):\n", "        for letter_index in range(0,len(word)):\n", "          s.add(Implies(placement_vars[word][0][i][j], letter_posns[i][j+letter_index][letter_to_index(word)[letter_index]]))\n", "      # TODO define an equivalent constraint for the vertical positions\n", "      # Replace this line\n", "      s.add(letter_posns[0][0][0])\n", "\n", "  return s"], "metadata": {"id": "NdjsISBCFr6K"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["# Let's test this routine so far\n", "solve_crossword(words, 10, add_constraint_set1)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ksTQlgvnHiqK", "outputId": "498bce1e-26fc-47ed-b067-31b01ac4e65c"}, "execution_count": 20, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[[JAN<PERSON>_h_0,0, <PERSON><PERSON><PERSON><PERSON>h_0,1, <PERSON><PERSON><PERSON><PERSON>h_0,2, <PERSON><PERSON><PERSON><PERSON>h_0,3, <PERSON><PERSON><PERSON><PERSON>h_0,4, <PERSON><PERSON><PERSON><PERSON>h_0,5, JAN<PERSON>_h_0,6], [JANE_h_1,0, J<PERSON><PERSON><PERSON>h_1,1, <PERSON><PERSON><PERSON><PERSON>h_1,2, <PERSON><PERSON><PERSON><PERSON>h_1,3, JANE_h_1,4, <PERSON><PERSON><PERSON><PERSON>h_1,5, JANE_h_1,6], [JANE_h_2,0, JANE_h_2,1, JANE_h_2,2, JANE_h_2,3, JANE_h_2,4, JANE_h_2,5, JANE_h_2,6], [JANE_h_3,0, JANE_h_3,1, JANE_h_3,2, JANE_h_3,3, JANE_h_3,4, <PERSON><PERSON><PERSON>_h_3,5, <PERSON>AN<PERSON>_h_3,6], [JANE_h_4,0, <PERSON><PERSON><PERSON><PERSON>h_4,1, <PERSON><PERSON><PERSON><PERSON>h_4,2, <PERSON><PERSON><PERSON><PERSON><PERSON>_4,3, <PERSON><PERSON><PERSON><PERSON><PERSON>_4,4, JAN<PERSON><PERSON>h_4,5, <PERSON><PERSON><PERSON><PERSON>h_4,6], [JANE_h_5,0, <PERSON><PERSON><PERSON><PERSON>h_5,1, JANE_h_5,2, JANE_h_5,3, JANE_h_5,4, JAN<PERSON>_h_5,5, JANE_h_5,6], [JANE_h_6,0, JANE_h_6,1, JANE_h_6,2, JANE_h_6,3, JANE_h_6,4, JANE_h_6,5, JANE_h_6,6], [JANE_h_7,0, JANE_h_7,1, JANE_h_7,2, JANE_h_7,3, JANE_h_7,4, JANE_h_7,5, JANE_h_7,6], [JANE_h_8,0, JANE_h_8,1, JANE_h_8,2, JANE_h_8,3, JANE_h_8,4, JANE_h_8,5, JANE_h_8,6], [JANE_h_9,0, JANE_h_9,1, JANE_h_9,2, JANE_h_9,3, JANE_h_9,4, JANE_h_9,5, JANE_h_9,6]], [[JANE_v_0,0, JANE_v_0,1, JANE_v_0,2, JANE_v_0,3, JANE_v_0,4, JANE_v_0,5, JANE_v_0,6, JANE_v_0,7, JANE_v_0,8, JANE_v_0,9], [JANE_v_1,0, JANE_v_1,1, JANE_v_1,2, JANE_v_1,3, JANE_v_1,4, JANE_v_1,5, JANE_v_1,6, JANE_v_1,7, JANE_v_1,8, JANE_v_1,9], [JANE_v_2,0, JANE_v_2,1, JANE_v_2,2, JANE_v_2,3, JANE_v_2,4, JANE_v_2,5, JANE_v_2,6, JANE_v_2,7, JANE_v_2,8, JANE_v_2,9], [JANE_v_3,0, JANE_v_3,1, JANE_v_3,2, JANE_v_3,3, JANE_v_3,4, JANE_v_3,5, JANE_v_3,6, JANE_v_3,7, JANE_v_3,8, JANE_v_3,9], [JANE_v_4,0, JANE_v_4,1, JANE_v_4,2, JANE_v_4,3, JANE_v_4,4, JANE_v_4,5, JANE_v_4,6, JANE_v_4,7, JANE_v_4,8, JANE_v_4,9], [JANE_v_5,0, JANE_v_5,1, JANE_v_5,2, JANE_v_5,3, JANE_v_5,4, JANE_v_5,5, JANE_v_5,6, JANE_v_5,7, JANE_v_5,8, JANE_v_5,9], [JANE_v_6,0, JANE_v_6,1, JANE_v_6,2, JANE_v_6,3, JANE_v_6,4, JANE_v_6,5, JANE_v_6,6, JANE_v_6,7, JANE_v_6,8, JANE_v_6,9]]]\n", "[[[AUSTEN_h_0,0, AUSTEN_h_0,1, AUSTEN_h_0,2, AUSTEN_h_0,3, AUSTEN_h_0,4], [AUSTEN_h_1,0, AUSTEN_h_1,1, AUSTEN_h_1,2, AUSTEN_h_1,3, AUSTEN_h_1,4], [AUSTEN_h_2,0, AUSTEN_h_2,1, AUSTEN_h_2,2, AUSTEN_h_2,3, AUSTEN_h_2,4], [AUSTEN_h_3,0, AUSTEN_h_3,1, AUSTEN_h_3,2, AUSTEN_h_3,3, AUSTEN_h_3,4], [AUSTEN_h_4,0, AUSTEN_h_4,1, AUSTEN_h_4,2, AUSTEN_h_4,3, AUSTEN_h_4,4], [AUSTEN_h_5,0, AUSTEN_h_5,1, AUSTEN_h_5,2, AUSTEN_h_5,3, AUSTEN_h_5,4], [AUSTEN_h_6,0, AUSTEN_h_6,1, AUSTEN_h_6,2, AUSTEN_h_6,3, AUSTEN_h_6,4], [AUSTEN_h_7,0, AUSTEN_h_7,1, AUSTEN_h_7,2, AUSTEN_h_7,3, AUSTEN_h_7,4], [AUSTEN_h_8,0, AUSTEN_h_8,1, AUSTEN_h_8,2, AUSTEN_h_8,3, AUSTEN_h_8,4], [AUSTEN_h_9,0, AUSTEN_h_9,1, AUSTEN_h_9,2, AUSTEN_h_9,3, AUSTEN_h_9,4]], [[AUSTEN_v_0,0, AUSTEN_v_0,1, AUSTEN_v_0,2, AUSTEN_v_0,3, AUSTEN_v_0,4, AUSTEN_v_0,5, AUSTEN_v_0,6, AUSTEN_v_0,7, AUSTEN_v_0,8, AUSTEN_v_0,9], [AUSTEN_v_1,0, AUSTEN_v_1,1, AUSTEN_v_1,2, AUSTEN_v_1,3, AUSTEN_v_1,4, AUSTEN_v_1,5, AUSTEN_v_1,6, AUSTEN_v_1,7, AUSTEN_v_1,8, AUSTEN_v_1,9], [AUSTEN_v_2,0, AUSTEN_v_2,1, AUSTEN_v_2,2, AUSTEN_v_2,3, AUSTEN_v_2,4, AUSTEN_v_2,5, AUSTEN_v_2,6, AUSTEN_v_2,7, AUSTEN_v_2,8, AUSTEN_v_2,9], [AUSTEN_v_3,0, AUSTEN_v_3,1, AUSTEN_v_3,2, AUSTEN_v_3,3, AUSTEN_v_3,4, AUSTEN_v_3,5, AUSTEN_v_3,6, AUSTEN_v_3,7, AUSTEN_v_3,8, AUSTEN_v_3,9], [AUSTEN_v_4,0, AUSTEN_v_4,1, AUSTEN_v_4,2, AUSTEN_v_4,3, AUSTEN_v_4,4, AUSTEN_v_4,5, AUSTEN_v_4,6, AUSTEN_v_4,7, AUSTEN_v_4,8, AUSTEN_v_4,9]]]\n", "[[[PRIDE_h_0,0, <PERSON><PERSON><PERSON>_h_0,1, PR<PERSON>E_h_0,2, PRIDE_h_0,3, PRIDE_h_0,4, PRIDE_h_0,5], [PRIDE_h_1,0, PRIDE_h_1,1, PRIDE_h_1,2, PRIDE_h_1,3, PRIDE_h_1,4, PRIDE_h_1,5], [PRIDE_h_2,0, PRIDE_h_2,1, PRIDE_h_2,2, PRIDE_h_2,3, PRIDE_h_2,4, PRIDE_h_2,5], [PRIDE_h_3,0, PRIDE_h_3,1, PRIDE_h_3,2, PRIDE_h_3,3, PRIDE_h_3,4, PRIDE_h_3,5], [PRIDE_h_4,0, PRIDE_h_4,1, PRIDE_h_4,2, PRIDE_h_4,3, PRIDE_h_4,4, PRIDE_h_4,5], [PRIDE_h_5,0, <PERSON><PERSON><PERSON>_h_5,1, PR<PERSON>E_h_5,2, PRIDE_h_5,3, PRIDE_h_5,4, PRIDE_h_5,5], [PRIDE_h_6,0, PRIDE_h_6,1, PRIDE_h_6,2, PRIDE_h_6,3, PRIDE_h_6,4, PRIDE_h_6,5], [PRIDE_h_7,0, PRIDE_h_7,1, PRIDE_h_7,2, PRIDE_h_7,3, PRIDE_h_7,4, PRIDE_h_7,5], [PRIDE_h_8,0, PRIDE_h_8,1, PRIDE_h_8,2, PRIDE_h_8,3, PRIDE_h_8,4, PRIDE_h_8,5], [PRIDE_h_9,0, PRIDE_h_9,1, PRIDE_h_9,2, PRIDE_h_9,3, PRIDE_h_9,4, PRIDE_h_9,5]], [[PRIDE_v_0,0, PRIDE_v_0,1, PRIDE_v_0,2, PRIDE_v_0,3, PRIDE_v_0,4, PRIDE_v_0,5, PRIDE_v_0,6, PRIDE_v_0,7, PRIDE_v_0,8, PRIDE_v_0,9], [PRIDE_v_1,0, PRIDE_v_1,1, PRIDE_v_1,2, PRIDE_v_1,3, PRIDE_v_1,4, PRIDE_v_1,5, PRIDE_v_1,6, PRIDE_v_1,7, PRIDE_v_1,8, PRIDE_v_1,9], [PRIDE_v_2,0, PRIDE_v_2,1, PRIDE_v_2,2, PRIDE_v_2,3, PRIDE_v_2,4, PRIDE_v_2,5, PRIDE_v_2,6, PRIDE_v_2,7, PRIDE_v_2,8, PRIDE_v_2,9], [PRIDE_v_3,0, PRIDE_v_3,1, PRIDE_v_3,2, PRIDE_v_3,3, PRIDE_v_3,4, PRIDE_v_3,5, PRIDE_v_3,6, PRIDE_v_3,7, PRIDE_v_3,8, PRIDE_v_3,9], [PRIDE_v_4,0, PRIDE_v_4,1, PRIDE_v_4,2, PRIDE_v_4,3, PRIDE_v_4,4, PRIDE_v_4,5, PRIDE_v_4,6, PRIDE_v_4,7, PRIDE_v_4,8, PRIDE_v_4,9], [PRIDE_v_5,0, PRIDE_v_5,1, PRIDE_v_5,2, PRIDE_v_5,3, PRIDE_v_5,4, PRIDE_v_5,5, PRIDE_v_5,6, PRIDE_v_5,7, PRIDE_v_5,8, PRIDE_v_5,9]]]\n", "[[[NOVEL_h_0,0, NOVEL_h_0,1, NOVEL_h_0,2, NOVEL_h_0,3, NOVEL_h_0,4, NOVEL_h_0,5], [NOVEL_h_1,0, NOVEL_h_1,1, NOVEL_h_1,2, NOVEL_h_1,3, NOVEL_h_1,4, NOVEL_h_1,5], [NOVEL_h_2,0, NOVEL_h_2,1, NOVEL_h_2,2, NOVEL_h_2,3, NOVEL_h_2,4, NOVEL_h_2,5], [NOVEL_h_3,0, NOVEL_h_3,1, NOVEL_h_3,2, NOVEL_h_3,3, NOVEL_h_3,4, NOVEL_h_3,5], [NOVEL_h_4,0, NOVEL_h_4,1, NOVEL_h_4,2, NOVEL_h_4,3, NOVEL_h_4,4, NOVEL_h_4,5], [NOVEL_h_5,0, NO<PERSON>L_h_5,1, NOVEL_h_5,2, NOVEL_h_5,3, NOVEL_h_5,4, NOVEL_h_5,5], [NOVEL_h_6,0, NOVEL_h_6,1, NOVEL_h_6,2, NOVEL_h_6,3, NOVEL_h_6,4, NOVEL_h_6,5], [NOVEL_h_7,0, NOVEL_h_7,1, NOVEL_h_7,2, NOVEL_h_7,3, NOVEL_h_7,4, NOVEL_h_7,5], [NOVEL_h_8,0, NOVEL_h_8,1, NOVEL_h_8,2, NOVEL_h_8,3, NOVEL_h_8,4, NOVEL_h_8,5], [NOVEL_h_9,0, NOVEL_h_9,1, NOVEL_h_9,2, NOVEL_h_9,3, NOVEL_h_9,4, NOVEL_h_9,5]], [[NOVEL_v_0,0, NOVEL_v_0,1, NOVEL_v_0,2, NOVEL_v_0,3, NOVEL_v_0,4, NOVEL_v_0,5, NOVEL_v_0,6, NOVEL_v_0,7, NOVEL_v_0,8, NOVEL_v_0,9], [NOVEL_v_1,0, NOVEL_v_1,1, NOVEL_v_1,2, NOVEL_v_1,3, NOVEL_v_1,4, NOVEL_v_1,5, NOVEL_v_1,6, NOVEL_v_1,7, NOVEL_v_1,8, NOVEL_v_1,9], [NOVEL_v_2,0, NOVEL_v_2,1, NOVEL_v_2,2, NOVEL_v_2,3, NOVEL_v_2,4, NOVEL_v_2,5, NOVEL_v_2,6, NOVEL_v_2,7, NOVEL_v_2,8, NOVEL_v_2,9], [NOVEL_v_3,0, NOVEL_v_3,1, NOVEL_v_3,2, NOVEL_v_3,3, NOVEL_v_3,4, NOVEL_v_3,5, NOVEL_v_3,6, NOVEL_v_3,7, NOVEL_v_3,8, NOVEL_v_3,9], [NOVEL_v_4,0, NOVEL_v_4,1, NOVEL_v_4,2, NOVEL_v_4,3, NOVEL_v_4,4, NOVEL_v_4,5, NOVEL_v_4,6, NOVEL_v_4,7, NOVEL_v_4,8, NOVEL_v_4,9], [NOVEL_v_5,0, NOVEL_v_5,1, NOVEL_v_5,2, NOVEL_v_5,3, NOVEL_v_5,4, NOVEL_v_5,5, NOVEL_v_5,6, NOVEL_v_5,7, NOVEL_v_5,8, NOVEL_v_5,9]]]\n", "[[[DARCY_h_0,0, <PERSON>ARC<PERSON>_h_0,1, <PERSON>ARC<PERSON>_h_0,2, DARCY_h_0,3, DARCY_h_0,4, DARCY_h_0,5], [DARCY_h_1,0, DARCY_h_1,1, DARCY_h_1,2, DARCY_h_1,3, DARCY_h_1,4, DARCY_h_1,5], [DARCY_h_2,0, DARCY_h_2,1, DARCY_h_2,2, DARCY_h_2,3, DARCY_h_2,4, DARCY_h_2,5], [DARCY_h_3,0, DARCY_h_3,1, DARCY_h_3,2, DARCY_h_3,3, DARCY_h_3,4, DARCY_h_3,5], [DARCY_h_4,0, DARCY_h_4,1, DARCY_h_4,2, DARCY_h_4,3, <PERSON>AR<PERSON><PERSON>_h_4,4, <PERSON>ARC<PERSON>_h_4,5], [DARCY_h_5,0, DARCY_h_5,1, DARCY_h_5,2, DARCY_h_5,3, DARCY_h_5,4, DARCY_h_5,5], [DARCY_h_6,0, DARCY_h_6,1, DARCY_h_6,2, DARCY_h_6,3, DARCY_h_6,4, DARCY_h_6,5], [DARCY_h_7,0, DARCY_h_7,1, DARCY_h_7,2, DARCY_h_7,3, DARCY_h_7,4, DARCY_h_7,5], [DARCY_h_8,0, DARCY_h_8,1, DARCY_h_8,2, DARCY_h_8,3, DARCY_h_8,4, DARCY_h_8,5], [DARCY_h_9,0, DARCY_h_9,1, DARCY_h_9,2, DARCY_h_9,3, DARCY_h_9,4, DARCY_h_9,5]], [[DARCY_v_0,0, DARCY_v_0,1, DARCY_v_0,2, DARCY_v_0,3, DARCY_v_0,4, DARCY_v_0,5, DARCY_v_0,6, DARCY_v_0,7, DARCY_v_0,8, DARCY_v_0,9], [DARCY_v_1,0, DARCY_v_1,1, DARCY_v_1,2, DARCY_v_1,3, DARCY_v_1,4, DARCY_v_1,5, DARCY_v_1,6, DARCY_v_1,7, DARCY_v_1,8, DARCY_v_1,9], [DARCY_v_2,0, DARCY_v_2,1, DARCY_v_2,2, DARCY_v_2,3, DARCY_v_2,4, DARCY_v_2,5, DARCY_v_2,6, DARCY_v_2,7, DARCY_v_2,8, DARCY_v_2,9], [DARCY_v_3,0, DARCY_v_3,1, DARCY_v_3,2, DARCY_v_3,3, DARCY_v_3,4, DARCY_v_3,5, DARCY_v_3,6, DARCY_v_3,7, DARCY_v_3,8, DARCY_v_3,9], [DARCY_v_4,0, DARCY_v_4,1, DARCY_v_4,2, DARCY_v_4,3, DARCY_v_4,4, DARCY_v_4,5, DARCY_v_4,6, DARCY_v_4,7, DARCY_v_4,8, DARCY_v_4,9], [DARCY_v_5,0, DARCY_v_5,1, DARCY_v_5,2, DARCY_v_5,3, DARCY_v_5,4, DARCY_v_5,5, DARCY_v_5,6, DARCY_v_5,7, DARCY_v_5,8, DARCY_v_5,9]]]\n", "[[[SENSE_h_0,0, SENSE_h_0,1, SENSE_h_0,2, SENSE_h_0,3, SENSE_h_0,4, SENSE_h_0,5], [SENSE_h_1,0, SENSE_h_1,1, SENSE_h_1,2, SENSE_h_1,3, SENSE_h_1,4, SENSE_h_1,5], [SENSE_h_2,0, SENSE_h_2,1, SENSE_h_2,2, SENSE_h_2,3, SENSE_h_2,4, SENSE_h_2,5], [SENSE_h_3,0, SENSE_h_3,1, SENSE_h_3,2, SENSE_h_3,3, SENSE_h_3,4, SENSE_h_3,5], [SENSE_h_4,0, SENSE_h_4,1, SENSE_h_4,2, SENSE_h_4,3, SENSE_h_4,4, SENSE_h_4,5], [SENSE_h_5,0, SENSE_h_5,1, SENSE_h_5,2, SENSE_h_5,3, SENSE_h_5,4, SENSE_h_5,5], [SENSE_h_6,0, SENSE_h_6,1, SENSE_h_6,2, SENSE_h_6,3, SENSE_h_6,4, SENSE_h_6,5], [SENSE_h_7,0, SENSE_h_7,1, SENSE_h_7,2, SENSE_h_7,3, SENSE_h_7,4, SENSE_h_7,5], [SENSE_h_8,0, SENSE_h_8,1, SENSE_h_8,2, SENSE_h_8,3, SENSE_h_8,4, SENSE_h_8,5], [SENSE_h_9,0, SENSE_h_9,1, SENSE_h_9,2, SENSE_h_9,3, SENSE_h_9,4, SENSE_h_9,5]], [[SENSE_v_0,0, SENSE_v_0,1, SENSE_v_0,2, SENSE_v_0,3, SENSE_v_0,4, SENSE_v_0,5, SENSE_v_0,6, SENSE_v_0,7, SENSE_v_0,8, SENSE_v_0,9], [SENSE_v_1,0, SENSE_v_1,1, SENSE_v_1,2, SENSE_v_1,3, SENSE_v_1,4, SENSE_v_1,5, SENSE_v_1,6, SENSE_v_1,7, SENSE_v_1,8, SENSE_v_1,9], [SENSE_v_2,0, SENSE_v_2,1, SENSE_v_2,2, SENSE_v_2,3, SENSE_v_2,4, SENSE_v_2,5, SENSE_v_2,6, SENSE_v_2,7, SENSE_v_2,8, SENSE_v_2,9], [SENSE_v_3,0, SENSE_v_3,1, SENSE_v_3,2, SENSE_v_3,3, SENSE_v_3,4, SENSE_v_3,5, SENSE_v_3,6, SENSE_v_3,7, SENSE_v_3,8, SENSE_v_3,9], [SENSE_v_4,0, SENSE_v_4,1, SENSE_v_4,2, SENSE_v_4,3, SENSE_v_4,4, SENSE_v_4,5, SENSE_v_4,6, SENSE_v_4,7, SENSE_v_4,8, SENSE_v_4,9], [SENSE_v_5,0, SENSE_v_5,1, SENSE_v_5,2, SENSE_v_5,3, SENSE_v_5,4, SENSE_v_5,5, SENSE_v_5,6, SENSE_v_5,7, SENSE_v_5,8, SENSE_v_5,9]]]\n", "[[[EMMA_h_0,0, <PERSON><PERSON><PERSON>_h_0,1, E<PERSON><PERSON>_h_0,2, EMMA_h_0,3, EMMA_h_0,4, EMMA_h_0,5, EMMA_h_0,6], [EMMA_h_1,0, EMMA_h_1,1, EMMA_h_1,2, EMMA_h_1,3, EMMA_h_1,4, EMMA_h_1,5, EMMA_h_1,6], [EMMA_h_2,0, EMMA_h_2,1, EMMA_h_2,2, EMMA_h_2,3, EMMA_h_2,4, EMMA_h_2,5, EMMA_h_2,6], [EMMA_h_3,0, EMMA_h_3,1, EMMA_h_3,2, EMMA_h_3,3, EMMA_h_3,4, EMMA_h_3,5, EMMA_h_3,6], [EMMA_h_4,0, EMMA_h_4,1, EMM<PERSON>_h_4,2, <PERSON><PERSON><PERSON><PERSON>h_4,3, <PERSON><PERSON><PERSON><PERSON>h_4,4, EMMA_h_4,5, EMM<PERSON>_h_4,6], [EMMA_h_5,0, EMMA_h_5,1, EMMA_h_5,2, EMMA_h_5,3, EMMA_h_5,4, <PERSON>MMA_h_5,5, EMMA_h_5,6], [<PERSON>MMA_h_6,0, EMMA_h_6,1, EMMA_h_6,2, <PERSON>MMA_h_6,3, <PERSON>MMA_h_6,4, EMMA_h_6,5, EMMA_h_6,6], [EMMA_h_7,0, EMMA_h_7,1, EMMA_h_7,2, EMMA_h_7,3, EMMA_h_7,4, EMMA_h_7,5, EMMA_h_7,6], [EMMA_h_8,0, EMMA_h_8,1, EMMA_h_8,2, EMMA_h_8,3, EMMA_h_8,4, EMMA_h_8,5, EMMA_h_8,6], [EMMA_h_9,0, EMMA_h_9,1, EMMA_h_9,2, EMMA_h_9,3, EMMA_h_9,4, EMMA_h_9,5, EMMA_h_9,6]], [[EMMA_v_0,0, EMMA_v_0,1, EMMA_v_0,2, EMMA_v_0,3, EMMA_v_0,4, EMMA_v_0,5, EMMA_v_0,6, EMMA_v_0,7, EMMA_v_0,8, EMMA_v_0,9], [EMMA_v_1,0, EMMA_v_1,1, EMMA_v_1,2, EMMA_v_1,3, EMMA_v_1,4, EMMA_v_1,5, EMMA_v_1,6, EMMA_v_1,7, EMMA_v_1,8, EMMA_v_1,9], [EMMA_v_2,0, EMMA_v_2,1, EMMA_v_2,2, EMMA_v_2,3, EMMA_v_2,4, EMMA_v_2,5, EMMA_v_2,6, EMMA_v_2,7, EMMA_v_2,8, EMMA_v_2,9], [EMMA_v_3,0, EMMA_v_3,1, EMMA_v_3,2, EMMA_v_3,3, EMMA_v_3,4, EMMA_v_3,5, EMMA_v_3,6, EMMA_v_3,7, EMMA_v_3,8, EMMA_v_3,9], [EMMA_v_4,0, EMMA_v_4,1, EMMA_v_4,2, EMMA_v_4,3, EMMA_v_4,4, EMMA_v_4,5, EMMA_v_4,6, EMMA_v_4,7, EMMA_v_4,8, EMMA_v_4,9], [EMMA_v_5,0, EMMA_v_5,1, EMMA_v_5,2, EMMA_v_5,3, EMMA_v_5,4, EMMA_v_5,5, EMMA_v_5,6, EMMA_v_5,7, EMMA_v_5,8, EMMA_v_5,9], [EMMA_v_6,0, EMMA_v_6,1, EMMA_v_6,2, EMMA_v_6,3, EMMA_v_6,4, EMMA_v_6,5, EMMA_v_6,6, EMMA_v_6,7, EMMA_v_6,8, EMMA_v_6,9]]]\n", "[[[ESTATE_h_0,0, ESTATE_h_0,1, <PERSON>STATE_h_0,2, ESTATE_h_0,3, ESTATE_h_0,4], [ESTATE_h_1,0, ESTATE_h_1,1, ESTATE_h_1,2, ESTATE_h_1,3, ESTATE_h_1,4], [ESTATE_h_2,0, ESTATE_h_2,1, ESTATE_h_2,2, ESTATE_h_2,3, ESTATE_h_2,4], [ESTATE_h_3,0, ESTATE_h_3,1, ESTATE_h_3,2, ESTATE_h_3,3, ESTATE_h_3,4], [ESTATE_h_4,0, ESTATE_h_4,1, ESTATE_h_4,2, ESTATE_h_4,3, ESTATE_h_4,4], [ESTATE_h_5,0, ESTATE_h_5,1, <PERSON>STATE_h_5,2, <PERSON><PERSON><PERSON><PERSON>_h_5,3, ESTATE_h_5,4], [ESTATE_h_6,0, ESTATE_h_6,1, ESTATE_h_6,2, ESTATE_h_6,3, ESTATE_h_6,4], [ESTATE_h_7,0, ESTATE_h_7,1, ESTATE_h_7,2, ESTATE_h_7,3, ESTATE_h_7,4], [ESTATE_h_8,0, ESTATE_h_8,1, ESTATE_h_8,2, ESTATE_h_8,3, ESTATE_h_8,4], [ESTATE_h_9,0, ESTATE_h_9,1, ESTATE_h_9,2, ESTATE_h_9,3, ESTATE_h_9,4]], [[ESTATE_v_0,0, ESTATE_v_0,1, ESTATE_v_0,2, ESTATE_v_0,3, ESTATE_v_0,4, ESTATE_v_0,5, ESTATE_v_0,6, ESTATE_v_0,7, ESTATE_v_0,8, ESTATE_v_0,9], [ESTATE_v_1,0, ESTATE_v_1,1, ESTATE_v_1,2, ESTATE_v_1,3, ESTATE_v_1,4, ESTATE_v_1,5, ESTATE_v_1,6, ESTATE_v_1,7, ESTATE_v_1,8, ESTATE_v_1,9], [ESTATE_v_2,0, ESTATE_v_2,1, ESTATE_v_2,2, ESTATE_v_2,3, ESTATE_v_2,4, ESTATE_v_2,5, ESTATE_v_2,6, ESTATE_v_2,7, ESTATE_v_2,8, ESTATE_v_2,9], [ESTATE_v_3,0, ESTATE_v_3,1, ESTATE_v_3,2, ESTATE_v_3,3, ESTATE_v_3,4, ESTATE_v_3,5, ESTATE_v_3,6, ESTATE_v_3,7, ESTATE_v_3,8, ESTATE_v_3,9], [ESTATE_v_4,0, ESTATE_v_4,1, ESTATE_v_4,2, ESTATE_v_4,3, ESTATE_v_4,4, ESTATE_v_4,5, ESTATE_v_4,6, ESTATE_v_4,7, ESTATE_v_4,8, ESTATE_v_4,9]]]\n", "[[[BENNET_h_0,0, BENNET_h_0,1, BENNET_h_0,2, BENNET_h_0,3, BENNET_h_0,4], [BENNET_h_1,0, BENNET_h_1,1, BENNET_h_1,2, BENNET_h_1,3, BENNET_h_1,4], [BENNET_h_2,0, BENNET_h_2,1, BENNET_h_2,2, BENNET_h_2,3, BENNET_h_2,4], [BENNET_h_3,0, BENNET_h_3,1, BENNET_h_3,2, BENNET_h_3,3, BENNET_h_3,4], [BENNET_h_4,0, BENNET_h_4,1, BENNET_h_4,2, BENNET_h_4,3, BENNET_h_4,4], [BENNET_h_5,0, BENNET_h_5,1, BENNET_h_5,2, BENNET_h_5,3, BENNET_h_5,4], [BENNET_h_6,0, BENNET_h_6,1, BENNET_h_6,2, BENNET_h_6,3, BENNET_h_6,4], [BENNET_h_7,0, BENNET_h_7,1, BENNET_h_7,2, BENNET_h_7,3, BENNET_h_7,4], [BENNET_h_8,0, BENNET_h_8,1, BENNET_h_8,2, BENNET_h_8,3, BENNET_h_8,4], [BENNET_h_9,0, BENNET_h_9,1, BENNET_h_9,2, BENNET_h_9,3, BENNET_h_9,4]], [[BENNET_v_0,0, BENNET_v_0,1, BENNET_v_0,2, BENNET_v_0,3, BENNET_v_0,4, BENNET_v_0,5, BENNET_v_0,6, BENNET_v_0,7, BENNET_v_0,8, BENNET_v_0,9], [BENNET_v_1,0, BENNET_v_1,1, BENNET_v_1,2, BENNET_v_1,3, BENNET_v_1,4, BENNET_v_1,5, BENNET_v_1,6, BENNET_v_1,7, BENNET_v_1,8, BENNET_v_1,9], [BENNET_v_2,0, BENNET_v_2,1, BENNET_v_2,2, BENNET_v_2,3, BENNET_v_2,4, BENNET_v_2,5, BENNET_v_2,6, BENNET_v_2,7, BENNET_v_2,8, BENNET_v_2,9], [BENNET_v_3,0, BENNET_v_3,1, BENNET_v_3,2, BENNET_v_3,3, BENNET_v_3,4, BENNET_v_3,5, BENNET_v_3,6, BENNET_v_3,7, BENNET_v_3,8, BENNET_v_3,9], [BENNET_v_4,0, BENNET_v_4,1, BENNET_v_4,2, BENNET_v_4,3, BENNET_v_4,4, BENNET_v_4,5, BENNET_v_4,6, BENNET_v_4,7, BENNET_v_4,8, BENNET_v_4,9]]]\n", "[[[BATH_h_0,0, BATH_h_0,1, BATH_h_0,2, BATH_h_0,3, BATH_h_0,4, BATH_h_0,5, BATH_h_0,6], [BATH_h_1,0, BATH_h_1,1, BATH_h_1,2, BATH_h_1,3, BATH_h_1,4, BATH_h_1,5, BATH_h_1,6], [BATH_h_2,0, BATH_h_2,1, BATH_h_2,2, BATH_h_2,3, BATH_h_2,4, BATH_h_2,5, BATH_h_2,6], [BATH_h_3,0, BATH_h_3,1, BATH_h_3,2, BATH_h_3,3, BATH_h_3,4, BATH_h_3,5, BATH_h_3,6], [BATH_h_4,0, BATH_h_4,1, BATH_h_4,2, BATH_h_4,3, BATH_h_4,4, BATH_h_4,5, BATH_h_4,6], [BATH_h_5,0, BATH_h_5,1, BATH_h_5,2, BATH_h_5,3, BATH_h_5,4, BATH_h_5,5, BATH_h_5,6], [BATH_h_6,0, BATH_h_6,1, BATH_h_6,2, BATH_h_6,3, BATH_h_6,4, BATH_h_6,5, BATH_h_6,6], [BATH_h_7,0, BATH_h_7,1, BATH_h_7,2, BATH_h_7,3, BATH_h_7,4, BATH_h_7,5, BATH_h_7,6], [BATH_h_8,0, BATH_h_8,1, BATH_h_8,2, BATH_h_8,3, BATH_h_8,4, BATH_h_8,5, BATH_h_8,6], [BATH_h_9,0, BATH_h_9,1, BATH_h_9,2, BATH_h_9,3, BATH_h_9,4, BATH_h_9,5, BATH_h_9,6]], [[BATH_v_0,0, BATH_v_0,1, BATH_v_0,2, BATH_v_0,3, BATH_v_0,4, BATH_v_0,5, BATH_v_0,6, BATH_v_0,7, BATH_v_0,8, BATH_v_0,9], [BATH_v_1,0, BATH_v_1,1, BATH_v_1,2, BATH_v_1,3, BATH_v_1,4, BATH_v_1,5, BATH_v_1,6, BATH_v_1,7, BATH_v_1,8, BATH_v_1,9], [BATH_v_2,0, BATH_v_2,1, BATH_v_2,2, BATH_v_2,3, BATH_v_2,4, BATH_v_2,5, BATH_v_2,6, BATH_v_2,7, BATH_v_2,8, BATH_v_2,9], [BATH_v_3,0, BATH_v_3,1, BATH_v_3,2, BATH_v_3,3, BATH_v_3,4, BATH_v_3,5, BATH_v_3,6, BATH_v_3,7, BATH_v_3,8, BATH_v_3,9], [BATH_v_4,0, BATH_v_4,1, BATH_v_4,2, BATH_v_4,3, BATH_v_4,4, BATH_v_4,5, BATH_v_4,6, BATH_v_4,7, BATH_v_4,8, BATH_v_4,9], [BATH_v_5,0, BATH_v_5,1, BATH_v_5,2, BATH_v_5,3, BATH_v_5,4, BATH_v_5,5, BATH_v_5,6, BATH_v_5,7, BATH_v_5,8, BATH_v_5,9], [BATH_v_6,0, BATH_v_6,1, BATH_v_6,2, BATH_v_6,3, BATH_v_6,4, BATH_v_6,5, BATH_v_6,6, BATH_v_6,7, BATH_v_6,8, BATH_v_6,9]]]\n", "Executed in 0.2938 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║█│A│I│A│E│E│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "markdown", "source": ["If you did this correctly, you should see that the words are all in there, but we don't have blank spaces where we should.  We need to add two further constraints to improve matters\n", "\n", "1. Horizontal words must have a blank space or an edge to the left and right of their positions.  Vertical words must have a blank space or and edge above or below their positions.\n", "2. Any position that is not part of a word should be blank\n", "\n"], "metadata": {"id": "SlzqglXAH1wG"}}, {"cell_type": "code", "source": ["def add_constraint_set2(s, placement_vars, letter_posns, words, grid_size):\n", "  # Constraint 1:  Horizontal words must either start in the first column or have a 0 to their left\n", "  #                Horizontal words must either finish in the last column of have a 0 to their right\n", "  #                Vertical words must either start in the first row or have a 0 above them\n", "  #                Vertical words must either end in the last row of have a 0 below them\n", "  for word in words:\n", "    # Horizontal words -- We'll do this one for you (read this code carefully)\n", "    for i in range(grid_size):\n", "        for j in range(1, grid_size - len(word)+1 ):\n", "            # Check for border or blank square before the word starts\n", "            s.add(Implies(placement_vars[word][0][i][j], letter_posns[i][j-1][0]))\n", "            s.add(Implies(placement_vars[word][0][i][j-1], letter_posns[i][j+len(word)-1][0]))\n", "\n", "    # Vertical words\n", "    for i in range(1,grid_size - len(word)+1 ):\n", "        for j in range(grid_size):\n", "            # TODO -- write the equivalent constraint for the vertical words\n", "            # Replace this line\n", "            s.add(letter_posns[0][0][0])\n", "\n", "  # Constraint 2:  Any position in the crossword grid that is not part of a word must be a blank space\n", "  #                This stops random characters appearing outside the solution\n", "  for i in range(grid_size):\n", "      for j in range(grid_size):\n", "          # Create a list of placement variables that add a letter to the current square\n", "          relevant_placements = []\n", "          for word in words:\n", "              # Horizontal words\n", "              for col in range(grid_size - len(word) + 1):\n", "                  if j >= col and j < col + len(word):\n", "                      relevant_placements.append(placement_vars[word][0][i][col])\n", "\n", "              # Vertical words\n", "              for row in range(grid_size - len(word) + 1):\n", "                  if i >= row and i < row + len(word):\n", "                      relevant_placements.append(placement_vars[word][1][row][j])\n", "\n", "\n", "          # If none of the relevant placements are true, the square must be blank\n", "          # TODO implement this constraint\n", "          # Replace this line\n", "          s.add(letter_posns[0][0][0])\n", "\n", "  return s"], "metadata": {"id": "7iHXNe_0F7ej"}, "execution_count": 21, "outputs": []}, {"cell_type": "code", "source": ["# Let's test this routine so far\n", "solve_crossword(words, 10, add_constraint_set1, add_constraint_set2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lPEcYEIbItHp", "outputId": "15cc8da2-8688-4cfc-8cda-32362f603583"}, "execution_count": 22, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[[JAN<PERSON>_h_0,0, <PERSON><PERSON><PERSON><PERSON>h_0,1, <PERSON><PERSON><PERSON><PERSON>h_0,2, <PERSON><PERSON><PERSON><PERSON>h_0,3, <PERSON><PERSON><PERSON><PERSON>h_0,4, <PERSON><PERSON><PERSON><PERSON>h_0,5, JAN<PERSON>_h_0,6], [JANE_h_1,0, J<PERSON><PERSON><PERSON>h_1,1, <PERSON><PERSON><PERSON><PERSON>h_1,2, <PERSON><PERSON><PERSON><PERSON>h_1,3, JANE_h_1,4, <PERSON><PERSON><PERSON><PERSON>h_1,5, JANE_h_1,6], [JANE_h_2,0, JANE_h_2,1, JANE_h_2,2, JANE_h_2,3, JANE_h_2,4, JANE_h_2,5, JANE_h_2,6], [JANE_h_3,0, JANE_h_3,1, JANE_h_3,2, JANE_h_3,3, JANE_h_3,4, <PERSON><PERSON><PERSON>_h_3,5, <PERSON>AN<PERSON>_h_3,6], [JANE_h_4,0, <PERSON><PERSON><PERSON><PERSON>h_4,1, <PERSON><PERSON><PERSON><PERSON>h_4,2, <PERSON><PERSON><PERSON><PERSON><PERSON>_4,3, <PERSON><PERSON><PERSON><PERSON><PERSON>_4,4, JAN<PERSON><PERSON>h_4,5, <PERSON><PERSON><PERSON><PERSON>h_4,6], [JANE_h_5,0, <PERSON><PERSON><PERSON><PERSON>h_5,1, JANE_h_5,2, JANE_h_5,3, JANE_h_5,4, JAN<PERSON>_h_5,5, JANE_h_5,6], [JANE_h_6,0, JANE_h_6,1, JANE_h_6,2, JANE_h_6,3, JANE_h_6,4, JANE_h_6,5, JANE_h_6,6], [JANE_h_7,0, JANE_h_7,1, JANE_h_7,2, JANE_h_7,3, JANE_h_7,4, JANE_h_7,5, JANE_h_7,6], [JANE_h_8,0, JANE_h_8,1, JANE_h_8,2, JANE_h_8,3, JANE_h_8,4, JANE_h_8,5, JANE_h_8,6], [JANE_h_9,0, JANE_h_9,1, JANE_h_9,2, JANE_h_9,3, JANE_h_9,4, JANE_h_9,5, JANE_h_9,6]], [[JANE_v_0,0, JANE_v_0,1, JANE_v_0,2, JANE_v_0,3, JANE_v_0,4, JANE_v_0,5, JANE_v_0,6, JANE_v_0,7, JANE_v_0,8, JANE_v_0,9], [JANE_v_1,0, JANE_v_1,1, JANE_v_1,2, JANE_v_1,3, JANE_v_1,4, JANE_v_1,5, JANE_v_1,6, JANE_v_1,7, JANE_v_1,8, JANE_v_1,9], [JANE_v_2,0, JANE_v_2,1, JANE_v_2,2, JANE_v_2,3, JANE_v_2,4, JANE_v_2,5, JANE_v_2,6, JANE_v_2,7, JANE_v_2,8, JANE_v_2,9], [JANE_v_3,0, JANE_v_3,1, JANE_v_3,2, JANE_v_3,3, JANE_v_3,4, JANE_v_3,5, JANE_v_3,6, JANE_v_3,7, JANE_v_3,8, JANE_v_3,9], [JANE_v_4,0, JANE_v_4,1, JANE_v_4,2, JANE_v_4,3, JANE_v_4,4, JANE_v_4,5, JANE_v_4,6, JANE_v_4,7, JANE_v_4,8, JANE_v_4,9], [JANE_v_5,0, JANE_v_5,1, JANE_v_5,2, JANE_v_5,3, JANE_v_5,4, JANE_v_5,5, JANE_v_5,6, JANE_v_5,7, JANE_v_5,8, JANE_v_5,9], [JANE_v_6,0, JANE_v_6,1, JANE_v_6,2, JANE_v_6,3, JANE_v_6,4, JANE_v_6,5, JANE_v_6,6, JANE_v_6,7, JANE_v_6,8, JANE_v_6,9]]]\n", "[[[AUSTEN_h_0,0, AUSTEN_h_0,1, AUSTEN_h_0,2, AUSTEN_h_0,3, AUSTEN_h_0,4], [AUSTEN_h_1,0, AUSTEN_h_1,1, AUSTEN_h_1,2, AUSTEN_h_1,3, AUSTEN_h_1,4], [AUSTEN_h_2,0, AUSTEN_h_2,1, AUSTEN_h_2,2, AUSTEN_h_2,3, AUSTEN_h_2,4], [AUSTEN_h_3,0, AUSTEN_h_3,1, AUSTEN_h_3,2, AUSTEN_h_3,3, AUSTEN_h_3,4], [AUSTEN_h_4,0, AUSTEN_h_4,1, AUSTEN_h_4,2, AUSTEN_h_4,3, AUSTEN_h_4,4], [AUSTEN_h_5,0, AUSTEN_h_5,1, AUSTEN_h_5,2, AUSTEN_h_5,3, AUSTEN_h_5,4], [AUSTEN_h_6,0, AUSTEN_h_6,1, AUSTEN_h_6,2, AUSTEN_h_6,3, AUSTEN_h_6,4], [AUSTEN_h_7,0, AUSTEN_h_7,1, AUSTEN_h_7,2, AUSTEN_h_7,3, AUSTEN_h_7,4], [AUSTEN_h_8,0, AUSTEN_h_8,1, AUSTEN_h_8,2, AUSTEN_h_8,3, AUSTEN_h_8,4], [AUSTEN_h_9,0, AUSTEN_h_9,1, AUSTEN_h_9,2, AUSTEN_h_9,3, AUSTEN_h_9,4]], [[AUSTEN_v_0,0, AUSTEN_v_0,1, AUSTEN_v_0,2, AUSTEN_v_0,3, AUSTEN_v_0,4, AUSTEN_v_0,5, AUSTEN_v_0,6, AUSTEN_v_0,7, AUSTEN_v_0,8, AUSTEN_v_0,9], [AUSTEN_v_1,0, AUSTEN_v_1,1, AUSTEN_v_1,2, AUSTEN_v_1,3, AUSTEN_v_1,4, AUSTEN_v_1,5, AUSTEN_v_1,6, AUSTEN_v_1,7, AUSTEN_v_1,8, AUSTEN_v_1,9], [AUSTEN_v_2,0, AUSTEN_v_2,1, AUSTEN_v_2,2, AUSTEN_v_2,3, AUSTEN_v_2,4, AUSTEN_v_2,5, AUSTEN_v_2,6, AUSTEN_v_2,7, AUSTEN_v_2,8, AUSTEN_v_2,9], [AUSTEN_v_3,0, AUSTEN_v_3,1, AUSTEN_v_3,2, AUSTEN_v_3,3, AUSTEN_v_3,4, AUSTEN_v_3,5, AUSTEN_v_3,6, AUSTEN_v_3,7, AUSTEN_v_3,8, AUSTEN_v_3,9], [AUSTEN_v_4,0, AUSTEN_v_4,1, AUSTEN_v_4,2, AUSTEN_v_4,3, AUSTEN_v_4,4, AUSTEN_v_4,5, AUSTEN_v_4,6, AUSTEN_v_4,7, AUSTEN_v_4,8, AUSTEN_v_4,9]]]\n", "[[[PRIDE_h_0,0, <PERSON><PERSON><PERSON>_h_0,1, PR<PERSON>E_h_0,2, PRIDE_h_0,3, PRIDE_h_0,4, PRIDE_h_0,5], [PRIDE_h_1,0, PRIDE_h_1,1, PRIDE_h_1,2, PRIDE_h_1,3, PRIDE_h_1,4, PRIDE_h_1,5], [PRIDE_h_2,0, PRIDE_h_2,1, PRIDE_h_2,2, PRIDE_h_2,3, PRIDE_h_2,4, PRIDE_h_2,5], [PRIDE_h_3,0, PRIDE_h_3,1, PRIDE_h_3,2, PRIDE_h_3,3, PRIDE_h_3,4, PRIDE_h_3,5], [PRIDE_h_4,0, PRIDE_h_4,1, PRIDE_h_4,2, PRIDE_h_4,3, PRIDE_h_4,4, PRIDE_h_4,5], [PRIDE_h_5,0, <PERSON><PERSON><PERSON>_h_5,1, PR<PERSON>E_h_5,2, PRIDE_h_5,3, PRIDE_h_5,4, PRIDE_h_5,5], [PRIDE_h_6,0, PRIDE_h_6,1, PRIDE_h_6,2, PRIDE_h_6,3, PRIDE_h_6,4, PRIDE_h_6,5], [PRIDE_h_7,0, PRIDE_h_7,1, PRIDE_h_7,2, PRIDE_h_7,3, PRIDE_h_7,4, PRIDE_h_7,5], [PRIDE_h_8,0, PRIDE_h_8,1, PRIDE_h_8,2, PRIDE_h_8,3, PRIDE_h_8,4, PRIDE_h_8,5], [PRIDE_h_9,0, PRIDE_h_9,1, PRIDE_h_9,2, PRIDE_h_9,3, PRIDE_h_9,4, PRIDE_h_9,5]], [[PRIDE_v_0,0, PRIDE_v_0,1, PRIDE_v_0,2, PRIDE_v_0,3, PRIDE_v_0,4, PRIDE_v_0,5, PRIDE_v_0,6, PRIDE_v_0,7, PRIDE_v_0,8, PRIDE_v_0,9], [PRIDE_v_1,0, PRIDE_v_1,1, PRIDE_v_1,2, PRIDE_v_1,3, PRIDE_v_1,4, PRIDE_v_1,5, PRIDE_v_1,6, PRIDE_v_1,7, PRIDE_v_1,8, PRIDE_v_1,9], [PRIDE_v_2,0, PRIDE_v_2,1, PRIDE_v_2,2, PRIDE_v_2,3, PRIDE_v_2,4, PRIDE_v_2,5, PRIDE_v_2,6, PRIDE_v_2,7, PRIDE_v_2,8, PRIDE_v_2,9], [PRIDE_v_3,0, PRIDE_v_3,1, PRIDE_v_3,2, PRIDE_v_3,3, PRIDE_v_3,4, PRIDE_v_3,5, PRIDE_v_3,6, PRIDE_v_3,7, PRIDE_v_3,8, PRIDE_v_3,9], [PRIDE_v_4,0, PRIDE_v_4,1, PRIDE_v_4,2, PRIDE_v_4,3, PRIDE_v_4,4, PRIDE_v_4,5, PRIDE_v_4,6, PRIDE_v_4,7, PRIDE_v_4,8, PRIDE_v_4,9], [PRIDE_v_5,0, PRIDE_v_5,1, PRIDE_v_5,2, PRIDE_v_5,3, PRIDE_v_5,4, PRIDE_v_5,5, PRIDE_v_5,6, PRIDE_v_5,7, PRIDE_v_5,8, PRIDE_v_5,9]]]\n", "[[[NOVEL_h_0,0, NOVEL_h_0,1, NOVEL_h_0,2, NOVEL_h_0,3, NOVEL_h_0,4, NOVEL_h_0,5], [NOVEL_h_1,0, NOVEL_h_1,1, NOVEL_h_1,2, NOVEL_h_1,3, NOVEL_h_1,4, NOVEL_h_1,5], [NOVEL_h_2,0, NOVEL_h_2,1, NOVEL_h_2,2, NOVEL_h_2,3, NOVEL_h_2,4, NOVEL_h_2,5], [NOVEL_h_3,0, NOVEL_h_3,1, NOVEL_h_3,2, NOVEL_h_3,3, NOVEL_h_3,4, NOVEL_h_3,5], [NOVEL_h_4,0, NOVEL_h_4,1, NOVEL_h_4,2, NOVEL_h_4,3, NOVEL_h_4,4, NOVEL_h_4,5], [NOVEL_h_5,0, NO<PERSON>L_h_5,1, NOVEL_h_5,2, NOVEL_h_5,3, NOVEL_h_5,4, NOVEL_h_5,5], [NOVEL_h_6,0, NOVEL_h_6,1, NOVEL_h_6,2, NOVEL_h_6,3, NOVEL_h_6,4, NOVEL_h_6,5], [NOVEL_h_7,0, NOVEL_h_7,1, NOVEL_h_7,2, NOVEL_h_7,3, NOVEL_h_7,4, NOVEL_h_7,5], [NOVEL_h_8,0, NOVEL_h_8,1, NOVEL_h_8,2, NOVEL_h_8,3, NOVEL_h_8,4, NOVEL_h_8,5], [NOVEL_h_9,0, NOVEL_h_9,1, NOVEL_h_9,2, NOVEL_h_9,3, NOVEL_h_9,4, NOVEL_h_9,5]], [[NOVEL_v_0,0, NOVEL_v_0,1, NOVEL_v_0,2, NOVEL_v_0,3, NOVEL_v_0,4, NOVEL_v_0,5, NOVEL_v_0,6, NOVEL_v_0,7, NOVEL_v_0,8, NOVEL_v_0,9], [NOVEL_v_1,0, NOVEL_v_1,1, NOVEL_v_1,2, NOVEL_v_1,3, NOVEL_v_1,4, NOVEL_v_1,5, NOVEL_v_1,6, NOVEL_v_1,7, NOVEL_v_1,8, NOVEL_v_1,9], [NOVEL_v_2,0, NOVEL_v_2,1, NOVEL_v_2,2, NOVEL_v_2,3, NOVEL_v_2,4, NOVEL_v_2,5, NOVEL_v_2,6, NOVEL_v_2,7, NOVEL_v_2,8, NOVEL_v_2,9], [NOVEL_v_3,0, NOVEL_v_3,1, NOVEL_v_3,2, NOVEL_v_3,3, NOVEL_v_3,4, NOVEL_v_3,5, NOVEL_v_3,6, NOVEL_v_3,7, NOVEL_v_3,8, NOVEL_v_3,9], [NOVEL_v_4,0, NOVEL_v_4,1, NOVEL_v_4,2, NOVEL_v_4,3, NOVEL_v_4,4, NOVEL_v_4,5, NOVEL_v_4,6, NOVEL_v_4,7, NOVEL_v_4,8, NOVEL_v_4,9], [NOVEL_v_5,0, NOVEL_v_5,1, NOVEL_v_5,2, NOVEL_v_5,3, NOVEL_v_5,4, NOVEL_v_5,5, NOVEL_v_5,6, NOVEL_v_5,7, NOVEL_v_5,8, NOVEL_v_5,9]]]\n", "[[[DARCY_h_0,0, <PERSON>ARC<PERSON>_h_0,1, <PERSON>ARC<PERSON>_h_0,2, DARCY_h_0,3, DARCY_h_0,4, DARCY_h_0,5], [DARCY_h_1,0, DARCY_h_1,1, DARCY_h_1,2, DARCY_h_1,3, DARCY_h_1,4, DARCY_h_1,5], [DARCY_h_2,0, DARCY_h_2,1, DARCY_h_2,2, DARCY_h_2,3, DARCY_h_2,4, DARCY_h_2,5], [DARCY_h_3,0, DARCY_h_3,1, DARCY_h_3,2, DARCY_h_3,3, DARCY_h_3,4, DARCY_h_3,5], [DARCY_h_4,0, DARCY_h_4,1, DARCY_h_4,2, DARCY_h_4,3, <PERSON>AR<PERSON><PERSON>_h_4,4, <PERSON>ARC<PERSON>_h_4,5], [DARCY_h_5,0, DARCY_h_5,1, DARCY_h_5,2, DARCY_h_5,3, DARCY_h_5,4, DARCY_h_5,5], [DARCY_h_6,0, DARCY_h_6,1, DARCY_h_6,2, DARCY_h_6,3, DARCY_h_6,4, DARCY_h_6,5], [DARCY_h_7,0, DARCY_h_7,1, DARCY_h_7,2, DARCY_h_7,3, DARCY_h_7,4, DARCY_h_7,5], [DARCY_h_8,0, DARCY_h_8,1, DARCY_h_8,2, DARCY_h_8,3, DARCY_h_8,4, DARCY_h_8,5], [DARCY_h_9,0, DARCY_h_9,1, DARCY_h_9,2, DARCY_h_9,3, DARCY_h_9,4, DARCY_h_9,5]], [[DARCY_v_0,0, DARCY_v_0,1, DARCY_v_0,2, DARCY_v_0,3, DARCY_v_0,4, DARCY_v_0,5, DARCY_v_0,6, DARCY_v_0,7, DARCY_v_0,8, DARCY_v_0,9], [DARCY_v_1,0, DARCY_v_1,1, DARCY_v_1,2, DARCY_v_1,3, DARCY_v_1,4, DARCY_v_1,5, DARCY_v_1,6, DARCY_v_1,7, DARCY_v_1,8, DARCY_v_1,9], [DARCY_v_2,0, DARCY_v_2,1, DARCY_v_2,2, DARCY_v_2,3, DARCY_v_2,4, DARCY_v_2,5, DARCY_v_2,6, DARCY_v_2,7, DARCY_v_2,8, DARCY_v_2,9], [DARCY_v_3,0, DARCY_v_3,1, DARCY_v_3,2, DARCY_v_3,3, DARCY_v_3,4, DARCY_v_3,5, DARCY_v_3,6, DARCY_v_3,7, DARCY_v_3,8, DARCY_v_3,9], [DARCY_v_4,0, DARCY_v_4,1, DARCY_v_4,2, DARCY_v_4,3, DARCY_v_4,4, DARCY_v_4,5, DARCY_v_4,6, DARCY_v_4,7, DARCY_v_4,8, DARCY_v_4,9], [DARCY_v_5,0, DARCY_v_5,1, DARCY_v_5,2, DARCY_v_5,3, DARCY_v_5,4, DARCY_v_5,5, DARCY_v_5,6, DARCY_v_5,7, DARCY_v_5,8, DARCY_v_5,9]]]\n", "[[[SENSE_h_0,0, SENSE_h_0,1, SENSE_h_0,2, SENSE_h_0,3, SENSE_h_0,4, SENSE_h_0,5], [SENSE_h_1,0, SENSE_h_1,1, SENSE_h_1,2, SENSE_h_1,3, SENSE_h_1,4, SENSE_h_1,5], [SENSE_h_2,0, SENSE_h_2,1, SENSE_h_2,2, SENSE_h_2,3, SENSE_h_2,4, SENSE_h_2,5], [SENSE_h_3,0, SENSE_h_3,1, SENSE_h_3,2, SENSE_h_3,3, SENSE_h_3,4, SENSE_h_3,5], [SENSE_h_4,0, SENSE_h_4,1, SENSE_h_4,2, SENSE_h_4,3, SENSE_h_4,4, SENSE_h_4,5], [SENSE_h_5,0, SENSE_h_5,1, SENSE_h_5,2, SENSE_h_5,3, SENSE_h_5,4, SENSE_h_5,5], [SENSE_h_6,0, SENSE_h_6,1, SENSE_h_6,2, SENSE_h_6,3, SENSE_h_6,4, SENSE_h_6,5], [SENSE_h_7,0, SENSE_h_7,1, SENSE_h_7,2, SENSE_h_7,3, SENSE_h_7,4, SENSE_h_7,5], [SENSE_h_8,0, SENSE_h_8,1, SENSE_h_8,2, SENSE_h_8,3, SENSE_h_8,4, SENSE_h_8,5], [SENSE_h_9,0, SENSE_h_9,1, SENSE_h_9,2, SENSE_h_9,3, SENSE_h_9,4, SENSE_h_9,5]], [[SENSE_v_0,0, SENSE_v_0,1, SENSE_v_0,2, SENSE_v_0,3, SENSE_v_0,4, SENSE_v_0,5, SENSE_v_0,6, SENSE_v_0,7, SENSE_v_0,8, SENSE_v_0,9], [SENSE_v_1,0, SENSE_v_1,1, SENSE_v_1,2, SENSE_v_1,3, SENSE_v_1,4, SENSE_v_1,5, SENSE_v_1,6, SENSE_v_1,7, SENSE_v_1,8, SENSE_v_1,9], [SENSE_v_2,0, SENSE_v_2,1, SENSE_v_2,2, SENSE_v_2,3, SENSE_v_2,4, SENSE_v_2,5, SENSE_v_2,6, SENSE_v_2,7, SENSE_v_2,8, SENSE_v_2,9], [SENSE_v_3,0, SENSE_v_3,1, SENSE_v_3,2, SENSE_v_3,3, SENSE_v_3,4, SENSE_v_3,5, SENSE_v_3,6, SENSE_v_3,7, SENSE_v_3,8, SENSE_v_3,9], [SENSE_v_4,0, SENSE_v_4,1, SENSE_v_4,2, SENSE_v_4,3, SENSE_v_4,4, SENSE_v_4,5, SENSE_v_4,6, SENSE_v_4,7, SENSE_v_4,8, SENSE_v_4,9], [SENSE_v_5,0, SENSE_v_5,1, SENSE_v_5,2, SENSE_v_5,3, SENSE_v_5,4, SENSE_v_5,5, SENSE_v_5,6, SENSE_v_5,7, SENSE_v_5,8, SENSE_v_5,9]]]\n", "[[[EMMA_h_0,0, <PERSON><PERSON><PERSON>_h_0,1, E<PERSON><PERSON>_h_0,2, EMMA_h_0,3, EMMA_h_0,4, EMMA_h_0,5, EMMA_h_0,6], [EMMA_h_1,0, EMMA_h_1,1, EMMA_h_1,2, EMMA_h_1,3, EMMA_h_1,4, EMMA_h_1,5, EMMA_h_1,6], [EMMA_h_2,0, EMMA_h_2,1, EMMA_h_2,2, EMMA_h_2,3, EMMA_h_2,4, EMMA_h_2,5, EMMA_h_2,6], [EMMA_h_3,0, EMMA_h_3,1, EMMA_h_3,2, EMMA_h_3,3, EMMA_h_3,4, EMMA_h_3,5, EMMA_h_3,6], [EMMA_h_4,0, EMMA_h_4,1, EMM<PERSON>_h_4,2, <PERSON><PERSON><PERSON><PERSON>h_4,3, <PERSON><PERSON><PERSON><PERSON>h_4,4, EMMA_h_4,5, EMM<PERSON>_h_4,6], [EMMA_h_5,0, EMMA_h_5,1, EMMA_h_5,2, EMMA_h_5,3, EMMA_h_5,4, <PERSON>MMA_h_5,5, EMMA_h_5,6], [<PERSON>MMA_h_6,0, EMMA_h_6,1, EMMA_h_6,2, <PERSON>MMA_h_6,3, <PERSON>MMA_h_6,4, EMMA_h_6,5, EMMA_h_6,6], [EMMA_h_7,0, EMMA_h_7,1, EMMA_h_7,2, EMMA_h_7,3, EMMA_h_7,4, EMMA_h_7,5, EMMA_h_7,6], [EMMA_h_8,0, EMMA_h_8,1, EMMA_h_8,2, EMMA_h_8,3, EMMA_h_8,4, EMMA_h_8,5, EMMA_h_8,6], [EMMA_h_9,0, EMMA_h_9,1, EMMA_h_9,2, EMMA_h_9,3, EMMA_h_9,4, EMMA_h_9,5, EMMA_h_9,6]], [[EMMA_v_0,0, EMMA_v_0,1, EMMA_v_0,2, EMMA_v_0,3, EMMA_v_0,4, EMMA_v_0,5, EMMA_v_0,6, EMMA_v_0,7, EMMA_v_0,8, EMMA_v_0,9], [EMMA_v_1,0, EMMA_v_1,1, EMMA_v_1,2, EMMA_v_1,3, EMMA_v_1,4, EMMA_v_1,5, EMMA_v_1,6, EMMA_v_1,7, EMMA_v_1,8, EMMA_v_1,9], [EMMA_v_2,0, EMMA_v_2,1, EMMA_v_2,2, EMMA_v_2,3, EMMA_v_2,4, EMMA_v_2,5, EMMA_v_2,6, EMMA_v_2,7, EMMA_v_2,8, EMMA_v_2,9], [EMMA_v_3,0, EMMA_v_3,1, EMMA_v_3,2, EMMA_v_3,3, EMMA_v_3,4, EMMA_v_3,5, EMMA_v_3,6, EMMA_v_3,7, EMMA_v_3,8, EMMA_v_3,9], [EMMA_v_4,0, EMMA_v_4,1, EMMA_v_4,2, EMMA_v_4,3, EMMA_v_4,4, EMMA_v_4,5, EMMA_v_4,6, EMMA_v_4,7, EMMA_v_4,8, EMMA_v_4,9], [EMMA_v_5,0, EMMA_v_5,1, EMMA_v_5,2, EMMA_v_5,3, EMMA_v_5,4, EMMA_v_5,5, EMMA_v_5,6, EMMA_v_5,7, EMMA_v_5,8, EMMA_v_5,9], [EMMA_v_6,0, EMMA_v_6,1, EMMA_v_6,2, EMMA_v_6,3, EMMA_v_6,4, EMMA_v_6,5, EMMA_v_6,6, EMMA_v_6,7, EMMA_v_6,8, EMMA_v_6,9]]]\n", "[[[ESTATE_h_0,0, ESTATE_h_0,1, <PERSON>STATE_h_0,2, ESTATE_h_0,3, ESTATE_h_0,4], [ESTATE_h_1,0, ESTATE_h_1,1, ESTATE_h_1,2, ESTATE_h_1,3, ESTATE_h_1,4], [ESTATE_h_2,0, ESTATE_h_2,1, ESTATE_h_2,2, ESTATE_h_2,3, ESTATE_h_2,4], [ESTATE_h_3,0, ESTATE_h_3,1, ESTATE_h_3,2, ESTATE_h_3,3, ESTATE_h_3,4], [ESTATE_h_4,0, ESTATE_h_4,1, ESTATE_h_4,2, ESTATE_h_4,3, ESTATE_h_4,4], [ESTATE_h_5,0, ESTATE_h_5,1, <PERSON>STATE_h_5,2, <PERSON><PERSON><PERSON><PERSON>_h_5,3, ESTATE_h_5,4], [ESTATE_h_6,0, ESTATE_h_6,1, ESTATE_h_6,2, ESTATE_h_6,3, ESTATE_h_6,4], [ESTATE_h_7,0, ESTATE_h_7,1, ESTATE_h_7,2, ESTATE_h_7,3, ESTATE_h_7,4], [ESTATE_h_8,0, ESTATE_h_8,1, ESTATE_h_8,2, ESTATE_h_8,3, ESTATE_h_8,4], [ESTATE_h_9,0, ESTATE_h_9,1, ESTATE_h_9,2, ESTATE_h_9,3, ESTATE_h_9,4]], [[ESTATE_v_0,0, ESTATE_v_0,1, ESTATE_v_0,2, ESTATE_v_0,3, ESTATE_v_0,4, ESTATE_v_0,5, ESTATE_v_0,6, ESTATE_v_0,7, ESTATE_v_0,8, ESTATE_v_0,9], [ESTATE_v_1,0, ESTATE_v_1,1, ESTATE_v_1,2, ESTATE_v_1,3, ESTATE_v_1,4, ESTATE_v_1,5, ESTATE_v_1,6, ESTATE_v_1,7, ESTATE_v_1,8, ESTATE_v_1,9], [ESTATE_v_2,0, ESTATE_v_2,1, ESTATE_v_2,2, ESTATE_v_2,3, ESTATE_v_2,4, ESTATE_v_2,5, ESTATE_v_2,6, ESTATE_v_2,7, ESTATE_v_2,8, ESTATE_v_2,9], [ESTATE_v_3,0, ESTATE_v_3,1, ESTATE_v_3,2, ESTATE_v_3,3, ESTATE_v_3,4, ESTATE_v_3,5, ESTATE_v_3,6, ESTATE_v_3,7, ESTATE_v_3,8, ESTATE_v_3,9], [ESTATE_v_4,0, ESTATE_v_4,1, ESTATE_v_4,2, ESTATE_v_4,3, ESTATE_v_4,4, ESTATE_v_4,5, ESTATE_v_4,6, ESTATE_v_4,7, ESTATE_v_4,8, ESTATE_v_4,9]]]\n", "[[[BENNET_h_0,0, BENNET_h_0,1, BENNET_h_0,2, BENNET_h_0,3, BENNET_h_0,4], [BENNET_h_1,0, BENNET_h_1,1, BENNET_h_1,2, BENNET_h_1,3, BENNET_h_1,4], [BENNET_h_2,0, BENNET_h_2,1, BENNET_h_2,2, BENNET_h_2,3, BENNET_h_2,4], [BENNET_h_3,0, BENNET_h_3,1, BENNET_h_3,2, BENNET_h_3,3, BENNET_h_3,4], [BENNET_h_4,0, BENNET_h_4,1, BENNET_h_4,2, BENNET_h_4,3, BENNET_h_4,4], [BENNET_h_5,0, BENNET_h_5,1, BENNET_h_5,2, BENNET_h_5,3, BENNET_h_5,4], [BENNET_h_6,0, BENNET_h_6,1, BENNET_h_6,2, BENNET_h_6,3, BENNET_h_6,4], [BENNET_h_7,0, BENNET_h_7,1, BENNET_h_7,2, BENNET_h_7,3, BENNET_h_7,4], [BENNET_h_8,0, BENNET_h_8,1, BENNET_h_8,2, BENNET_h_8,3, BENNET_h_8,4], [BENNET_h_9,0, BENNET_h_9,1, BENNET_h_9,2, BENNET_h_9,3, BENNET_h_9,4]], [[BENNET_v_0,0, BENNET_v_0,1, BENNET_v_0,2, BENNET_v_0,3, BENNET_v_0,4, BENNET_v_0,5, BENNET_v_0,6, BENNET_v_0,7, BENNET_v_0,8, BENNET_v_0,9], [BENNET_v_1,0, BENNET_v_1,1, BENNET_v_1,2, BENNET_v_1,3, BENNET_v_1,4, BENNET_v_1,5, BENNET_v_1,6, BENNET_v_1,7, BENNET_v_1,8, BENNET_v_1,9], [BENNET_v_2,0, BENNET_v_2,1, BENNET_v_2,2, BENNET_v_2,3, BENNET_v_2,4, BENNET_v_2,5, BENNET_v_2,6, BENNET_v_2,7, BENNET_v_2,8, BENNET_v_2,9], [BENNET_v_3,0, BENNET_v_3,1, BENNET_v_3,2, BENNET_v_3,3, BENNET_v_3,4, BENNET_v_3,5, BENNET_v_3,6, BENNET_v_3,7, BENNET_v_3,8, BENNET_v_3,9], [BENNET_v_4,0, BENNET_v_4,1, BENNET_v_4,2, BENNET_v_4,3, BENNET_v_4,4, BENNET_v_4,5, BENNET_v_4,6, BENNET_v_4,7, BENNET_v_4,8, BENNET_v_4,9]]]\n", "[[[BATH_h_0,0, BATH_h_0,1, BATH_h_0,2, BATH_h_0,3, BATH_h_0,4, BATH_h_0,5, BATH_h_0,6], [BATH_h_1,0, BATH_h_1,1, BATH_h_1,2, BATH_h_1,3, BATH_h_1,4, BATH_h_1,5, BATH_h_1,6], [BATH_h_2,0, BATH_h_2,1, BATH_h_2,2, BATH_h_2,3, BATH_h_2,4, BATH_h_2,5, BATH_h_2,6], [BATH_h_3,0, BATH_h_3,1, BATH_h_3,2, BATH_h_3,3, BATH_h_3,4, BATH_h_3,5, BATH_h_3,6], [BATH_h_4,0, BATH_h_4,1, BATH_h_4,2, BATH_h_4,3, BATH_h_4,4, BATH_h_4,5, BATH_h_4,6], [BATH_h_5,0, BATH_h_5,1, BATH_h_5,2, BATH_h_5,3, BATH_h_5,4, BATH_h_5,5, BATH_h_5,6], [BATH_h_6,0, BATH_h_6,1, BATH_h_6,2, BATH_h_6,3, BATH_h_6,4, BATH_h_6,5, BATH_h_6,6], [BATH_h_7,0, BATH_h_7,1, BATH_h_7,2, BATH_h_7,3, BATH_h_7,4, BATH_h_7,5, BATH_h_7,6], [BATH_h_8,0, BATH_h_8,1, BATH_h_8,2, BATH_h_8,3, BATH_h_8,4, BATH_h_8,5, BATH_h_8,6], [BATH_h_9,0, BATH_h_9,1, BATH_h_9,2, BATH_h_9,3, BATH_h_9,4, BATH_h_9,5, BATH_h_9,6]], [[BATH_v_0,0, BATH_v_0,1, BATH_v_0,2, BATH_v_0,3, BATH_v_0,4, BATH_v_0,5, BATH_v_0,6, BATH_v_0,7, BATH_v_0,8, BATH_v_0,9], [BATH_v_1,0, BATH_v_1,1, BATH_v_1,2, BATH_v_1,3, BATH_v_1,4, BATH_v_1,5, BATH_v_1,6, BATH_v_1,7, BATH_v_1,8, BATH_v_1,9], [BATH_v_2,0, BATH_v_2,1, BATH_v_2,2, BATH_v_2,3, BATH_v_2,4, BATH_v_2,5, BATH_v_2,6, BATH_v_2,7, BATH_v_2,8, BATH_v_2,9], [BATH_v_3,0, BATH_v_3,1, BATH_v_3,2, BATH_v_3,3, BATH_v_3,4, BATH_v_3,5, BATH_v_3,6, BATH_v_3,7, BATH_v_3,8, BATH_v_3,9], [BATH_v_4,0, BATH_v_4,1, BATH_v_4,2, BATH_v_4,3, BATH_v_4,4, BATH_v_4,5, BATH_v_4,6, BATH_v_4,7, BATH_v_4,8, BATH_v_4,9], [BATH_v_5,0, BATH_v_5,1, BATH_v_5,2, BATH_v_5,3, BATH_v_5,4, BATH_v_5,5, BATH_v_5,6, BATH_v_5,7, BATH_v_5,8, BATH_v_5,9], [BATH_v_6,0, BATH_v_6,1, BATH_v_6,2, BATH_v_6,3, BATH_v_6,4, BATH_v_6,5, BATH_v_6,6, BATH_v_6,7, BATH_v_6,8, BATH_v_6,9]]]\n", "Executed in 0.3757 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║█│A│I│A│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "markdown", "source": ["If you did this correctly, it should be an improvement, but it's not perfect; every letter is now part of either a horizontal or a vertical word.  However, when there are several vertical words adjacent to each other and we read horizontally across these words, we get nonsense. Similarly, when there are several horizontal words adjacent to each other an we read vertically through these words, we get nonsense.  We can fix this by adding another constraint:\n", "\n", "*   If a letter is in a horizontal word, it is either inside a vertical word as well *OR* it has a blank square (or the edge of the grid) above and below it.\n", "*   If a letter is in a vertical word, it is either inside a horizontal word as well *OR* it has a blank square (or the edge of the grid) to the left and the right of it.\n", "\n", "This one is pretty tricky to get right, so just read the code and try to understand how it works."], "metadata": {"id": "2X4guZwZI_JW"}}, {"cell_type": "code", "source": ["def add_constraint_set3(s, placement_vars, letter_posns, words, grid_size):\n", "  # Constraint 1:   If a letter is in a horizontal word, it either\n", "  #                     -- is inside a vertical word as well\n", "  #                     -- has a blank (or edge) above and below it\n", "  #                 If a letter in a vertical word exists, it is either\n", "  #                     -- is inside a horizontal word too\n", "  #                      -- has a blank (or edge) to the left and to the right of it.\n", "  for i in range(0,grid_size):\n", "      for j in range(0,grid_size):\n", "          relevant_placements_horz = []\n", "          relevant_placements_vert = []\n", "          for word in words:\n", "            for j2 in range (max(0,j-len(word)+1), min(j+1,grid_size-len(word)+1)):\n", "                relevant_placements_horz.append(placement_vars[word][0][i][j2])\n", "            for i2 in range(max(0,i-len(word)+1), min(i+1,grid_size-len(word)+1)):\n", "                relevant_placements_vert.append(placement_vars[word][1][i2][j])\n", "          in_horizontal_word = Or(relevant_placements_horz)\n", "          in_vertical_word = Or(relevant_placements_vert)\n", "\n", "          if(i == 0):\n", "            above_and_below_are_blank = letter_posns[i+1][j][0]\n", "          else:\n", "            if(i == grid_size-1):\n", "              above_and_below_are_blank = letter_posns[i-1][j][0]\n", "            else:\n", "              above_and_below_are_blank = And(letter_posns[i-1][j][0],letter_posns[i+1][j][0])\n", "\n", "          if(j == 0):\n", "            left_and_right_are_blank = letter_posns[i][j+1][0]\n", "          else:\n", "            if(j == grid_size-1):\n", "              left_and_right_are_blank = letter_posns[i][j-1][0]\n", "            else:\n", "              left_and_right_are_blank = And(letter_posns[i][j-1][0],letter_posns[i][j+1][0])\n", "          s.add(Implies(in_horizontal_word, Or(in_vertical_word, above_and_below_are_blank)))\n", "          s.add(Implies(in_vertical_word, Or(in_horizontal_word, left_and_right_are_blank)))\n", "\n", "  return s"], "metadata": {"id": "4LSgimAjGQdT"}, "execution_count": 23, "outputs": []}, {"cell_type": "code", "source": ["# Let's see how this improves things\n", "solve_crossword(words, 10, add_constraint_set1, add_constraint_set2, add_constraint_set3)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8HHqCWMzKCTL", "outputId": "401fbfd2-194b-4258-a890-7d99fc601937"}, "execution_count": 24, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[[JAN<PERSON>_h_0,0, <PERSON><PERSON><PERSON><PERSON>h_0,1, <PERSON><PERSON><PERSON><PERSON>h_0,2, <PERSON><PERSON><PERSON><PERSON>h_0,3, <PERSON><PERSON><PERSON><PERSON>h_0,4, <PERSON><PERSON><PERSON><PERSON>h_0,5, JAN<PERSON>_h_0,6], [JANE_h_1,0, J<PERSON><PERSON><PERSON>h_1,1, <PERSON><PERSON><PERSON><PERSON>h_1,2, <PERSON><PERSON><PERSON><PERSON>h_1,3, JANE_h_1,4, <PERSON><PERSON><PERSON><PERSON>h_1,5, JANE_h_1,6], [JANE_h_2,0, JANE_h_2,1, JANE_h_2,2, JANE_h_2,3, JANE_h_2,4, JANE_h_2,5, JANE_h_2,6], [JANE_h_3,0, JANE_h_3,1, JANE_h_3,2, JANE_h_3,3, JANE_h_3,4, <PERSON><PERSON><PERSON>_h_3,5, <PERSON>AN<PERSON>_h_3,6], [JANE_h_4,0, <PERSON><PERSON><PERSON><PERSON>h_4,1, <PERSON><PERSON><PERSON><PERSON>h_4,2, <PERSON><PERSON><PERSON><PERSON><PERSON>_4,3, <PERSON><PERSON><PERSON><PERSON><PERSON>_4,4, JAN<PERSON><PERSON>h_4,5, <PERSON><PERSON><PERSON><PERSON>h_4,6], [JANE_h_5,0, <PERSON><PERSON><PERSON><PERSON>h_5,1, JANE_h_5,2, JANE_h_5,3, JANE_h_5,4, JAN<PERSON>_h_5,5, JANE_h_5,6], [JANE_h_6,0, JANE_h_6,1, JANE_h_6,2, JANE_h_6,3, JANE_h_6,4, JANE_h_6,5, JANE_h_6,6], [JANE_h_7,0, JANE_h_7,1, JANE_h_7,2, JANE_h_7,3, JANE_h_7,4, JANE_h_7,5, JANE_h_7,6], [JANE_h_8,0, JANE_h_8,1, JANE_h_8,2, JANE_h_8,3, JANE_h_8,4, JANE_h_8,5, JANE_h_8,6], [JANE_h_9,0, JANE_h_9,1, JANE_h_9,2, JANE_h_9,3, JANE_h_9,4, JANE_h_9,5, JANE_h_9,6]], [[JANE_v_0,0, JANE_v_0,1, JANE_v_0,2, JANE_v_0,3, JANE_v_0,4, JANE_v_0,5, JANE_v_0,6, JANE_v_0,7, JANE_v_0,8, JANE_v_0,9], [JANE_v_1,0, JANE_v_1,1, JANE_v_1,2, JANE_v_1,3, JANE_v_1,4, JANE_v_1,5, JANE_v_1,6, JANE_v_1,7, JANE_v_1,8, JANE_v_1,9], [JANE_v_2,0, JANE_v_2,1, JANE_v_2,2, JANE_v_2,3, JANE_v_2,4, JANE_v_2,5, JANE_v_2,6, JANE_v_2,7, JANE_v_2,8, JANE_v_2,9], [JANE_v_3,0, JANE_v_3,1, JANE_v_3,2, JANE_v_3,3, JANE_v_3,4, JANE_v_3,5, JANE_v_3,6, JANE_v_3,7, JANE_v_3,8, JANE_v_3,9], [JANE_v_4,0, JANE_v_4,1, JANE_v_4,2, JANE_v_4,3, JANE_v_4,4, JANE_v_4,5, JANE_v_4,6, JANE_v_4,7, JANE_v_4,8, JANE_v_4,9], [JANE_v_5,0, JANE_v_5,1, JANE_v_5,2, JANE_v_5,3, JANE_v_5,4, JANE_v_5,5, JANE_v_5,6, JANE_v_5,7, JANE_v_5,8, JANE_v_5,9], [JANE_v_6,0, JANE_v_6,1, JANE_v_6,2, JANE_v_6,3, JANE_v_6,4, JANE_v_6,5, JANE_v_6,6, JANE_v_6,7, JANE_v_6,8, JANE_v_6,9]]]\n", "[[[AUSTEN_h_0,0, AUSTEN_h_0,1, AUSTEN_h_0,2, AUSTEN_h_0,3, AUSTEN_h_0,4], [AUSTEN_h_1,0, AUSTEN_h_1,1, AUSTEN_h_1,2, AUSTEN_h_1,3, AUSTEN_h_1,4], [AUSTEN_h_2,0, AUSTEN_h_2,1, AUSTEN_h_2,2, AUSTEN_h_2,3, AUSTEN_h_2,4], [AUSTEN_h_3,0, AUSTEN_h_3,1, AUSTEN_h_3,2, AUSTEN_h_3,3, AUSTEN_h_3,4], [AUSTEN_h_4,0, AUSTEN_h_4,1, AUSTEN_h_4,2, AUSTEN_h_4,3, AUSTEN_h_4,4], [AUSTEN_h_5,0, AUSTEN_h_5,1, AUSTEN_h_5,2, AUSTEN_h_5,3, AUSTEN_h_5,4], [AUSTEN_h_6,0, AUSTEN_h_6,1, AUSTEN_h_6,2, AUSTEN_h_6,3, AUSTEN_h_6,4], [AUSTEN_h_7,0, AUSTEN_h_7,1, AUSTEN_h_7,2, AUSTEN_h_7,3, AUSTEN_h_7,4], [AUSTEN_h_8,0, AUSTEN_h_8,1, AUSTEN_h_8,2, AUSTEN_h_8,3, AUSTEN_h_8,4], [AUSTEN_h_9,0, AUSTEN_h_9,1, AUSTEN_h_9,2, AUSTEN_h_9,3, AUSTEN_h_9,4]], [[AUSTEN_v_0,0, AUSTEN_v_0,1, AUSTEN_v_0,2, AUSTEN_v_0,3, AUSTEN_v_0,4, AUSTEN_v_0,5, AUSTEN_v_0,6, AUSTEN_v_0,7, AUSTEN_v_0,8, AUSTEN_v_0,9], [AUSTEN_v_1,0, AUSTEN_v_1,1, AUSTEN_v_1,2, AUSTEN_v_1,3, AUSTEN_v_1,4, AUSTEN_v_1,5, AUSTEN_v_1,6, AUSTEN_v_1,7, AUSTEN_v_1,8, AUSTEN_v_1,9], [AUSTEN_v_2,0, AUSTEN_v_2,1, AUSTEN_v_2,2, AUSTEN_v_2,3, AUSTEN_v_2,4, AUSTEN_v_2,5, AUSTEN_v_2,6, AUSTEN_v_2,7, AUSTEN_v_2,8, AUSTEN_v_2,9], [AUSTEN_v_3,0, AUSTEN_v_3,1, AUSTEN_v_3,2, AUSTEN_v_3,3, AUSTEN_v_3,4, AUSTEN_v_3,5, AUSTEN_v_3,6, AUSTEN_v_3,7, AUSTEN_v_3,8, AUSTEN_v_3,9], [AUSTEN_v_4,0, AUSTEN_v_4,1, AUSTEN_v_4,2, AUSTEN_v_4,3, AUSTEN_v_4,4, AUSTEN_v_4,5, AUSTEN_v_4,6, AUSTEN_v_4,7, AUSTEN_v_4,8, AUSTEN_v_4,9]]]\n", "[[[PRIDE_h_0,0, <PERSON><PERSON><PERSON>_h_0,1, PR<PERSON>E_h_0,2, PRIDE_h_0,3, PRIDE_h_0,4, PRIDE_h_0,5], [PRIDE_h_1,0, PRIDE_h_1,1, PRIDE_h_1,2, PRIDE_h_1,3, PRIDE_h_1,4, PRIDE_h_1,5], [PRIDE_h_2,0, PRIDE_h_2,1, PRIDE_h_2,2, PRIDE_h_2,3, PRIDE_h_2,4, PRIDE_h_2,5], [PRIDE_h_3,0, PRIDE_h_3,1, PRIDE_h_3,2, PRIDE_h_3,3, PRIDE_h_3,4, PRIDE_h_3,5], [PRIDE_h_4,0, PRIDE_h_4,1, PRIDE_h_4,2, PRIDE_h_4,3, PRIDE_h_4,4, PRIDE_h_4,5], [PRIDE_h_5,0, <PERSON><PERSON><PERSON>_h_5,1, PR<PERSON>E_h_5,2, PRIDE_h_5,3, PRIDE_h_5,4, PRIDE_h_5,5], [PRIDE_h_6,0, PRIDE_h_6,1, PRIDE_h_6,2, PRIDE_h_6,3, PRIDE_h_6,4, PRIDE_h_6,5], [PRIDE_h_7,0, PRIDE_h_7,1, PRIDE_h_7,2, PRIDE_h_7,3, PRIDE_h_7,4, PRIDE_h_7,5], [PRIDE_h_8,0, PRIDE_h_8,1, PRIDE_h_8,2, PRIDE_h_8,3, PRIDE_h_8,4, PRIDE_h_8,5], [PRIDE_h_9,0, PRIDE_h_9,1, PRIDE_h_9,2, PRIDE_h_9,3, PRIDE_h_9,4, PRIDE_h_9,5]], [[PRIDE_v_0,0, PRIDE_v_0,1, PRIDE_v_0,2, PRIDE_v_0,3, PRIDE_v_0,4, PRIDE_v_0,5, PRIDE_v_0,6, PRIDE_v_0,7, PRIDE_v_0,8, PRIDE_v_0,9], [PRIDE_v_1,0, PRIDE_v_1,1, PRIDE_v_1,2, PRIDE_v_1,3, PRIDE_v_1,4, PRIDE_v_1,5, PRIDE_v_1,6, PRIDE_v_1,7, PRIDE_v_1,8, PRIDE_v_1,9], [PRIDE_v_2,0, PRIDE_v_2,1, PRIDE_v_2,2, PRIDE_v_2,3, PRIDE_v_2,4, PRIDE_v_2,5, PRIDE_v_2,6, PRIDE_v_2,7, PRIDE_v_2,8, PRIDE_v_2,9], [PRIDE_v_3,0, PRIDE_v_3,1, PRIDE_v_3,2, PRIDE_v_3,3, PRIDE_v_3,4, PRIDE_v_3,5, PRIDE_v_3,6, PRIDE_v_3,7, PRIDE_v_3,8, PRIDE_v_3,9], [PRIDE_v_4,0, PRIDE_v_4,1, PRIDE_v_4,2, PRIDE_v_4,3, PRIDE_v_4,4, PRIDE_v_4,5, PRIDE_v_4,6, PRIDE_v_4,7, PRIDE_v_4,8, PRIDE_v_4,9], [PRIDE_v_5,0, PRIDE_v_5,1, PRIDE_v_5,2, PRIDE_v_5,3, PRIDE_v_5,4, PRIDE_v_5,5, PRIDE_v_5,6, PRIDE_v_5,7, PRIDE_v_5,8, PRIDE_v_5,9]]]\n", "[[[NOVEL_h_0,0, NOVEL_h_0,1, NOVEL_h_0,2, NOVEL_h_0,3, NOVEL_h_0,4, NOVEL_h_0,5], [NOVEL_h_1,0, NOVEL_h_1,1, NOVEL_h_1,2, NOVEL_h_1,3, NOVEL_h_1,4, NOVEL_h_1,5], [NOVEL_h_2,0, NOVEL_h_2,1, NOVEL_h_2,2, NOVEL_h_2,3, NOVEL_h_2,4, NOVEL_h_2,5], [NOVEL_h_3,0, NOVEL_h_3,1, NOVEL_h_3,2, NOVEL_h_3,3, NOVEL_h_3,4, NOVEL_h_3,5], [NOVEL_h_4,0, NOVEL_h_4,1, NOVEL_h_4,2, NOVEL_h_4,3, NOVEL_h_4,4, NOVEL_h_4,5], [NOVEL_h_5,0, NO<PERSON>L_h_5,1, NOVEL_h_5,2, NOVEL_h_5,3, NOVEL_h_5,4, NOVEL_h_5,5], [NOVEL_h_6,0, NOVEL_h_6,1, NOVEL_h_6,2, NOVEL_h_6,3, NOVEL_h_6,4, NOVEL_h_6,5], [NOVEL_h_7,0, NOVEL_h_7,1, NOVEL_h_7,2, NOVEL_h_7,3, NOVEL_h_7,4, NOVEL_h_7,5], [NOVEL_h_8,0, NOVEL_h_8,1, NOVEL_h_8,2, NOVEL_h_8,3, NOVEL_h_8,4, NOVEL_h_8,5], [NOVEL_h_9,0, NOVEL_h_9,1, NOVEL_h_9,2, NOVEL_h_9,3, NOVEL_h_9,4, NOVEL_h_9,5]], [[NOVEL_v_0,0, NOVEL_v_0,1, NOVEL_v_0,2, NOVEL_v_0,3, NOVEL_v_0,4, NOVEL_v_0,5, NOVEL_v_0,6, NOVEL_v_0,7, NOVEL_v_0,8, NOVEL_v_0,9], [NOVEL_v_1,0, NOVEL_v_1,1, NOVEL_v_1,2, NOVEL_v_1,3, NOVEL_v_1,4, NOVEL_v_1,5, NOVEL_v_1,6, NOVEL_v_1,7, NOVEL_v_1,8, NOVEL_v_1,9], [NOVEL_v_2,0, NOVEL_v_2,1, NOVEL_v_2,2, NOVEL_v_2,3, NOVEL_v_2,4, NOVEL_v_2,5, NOVEL_v_2,6, NOVEL_v_2,7, NOVEL_v_2,8, NOVEL_v_2,9], [NOVEL_v_3,0, NOVEL_v_3,1, NOVEL_v_3,2, NOVEL_v_3,3, NOVEL_v_3,4, NOVEL_v_3,5, NOVEL_v_3,6, NOVEL_v_3,7, NOVEL_v_3,8, NOVEL_v_3,9], [NOVEL_v_4,0, NOVEL_v_4,1, NOVEL_v_4,2, NOVEL_v_4,3, NOVEL_v_4,4, NOVEL_v_4,5, NOVEL_v_4,6, NOVEL_v_4,7, NOVEL_v_4,8, NOVEL_v_4,9], [NOVEL_v_5,0, NOVEL_v_5,1, NOVEL_v_5,2, NOVEL_v_5,3, NOVEL_v_5,4, NOVEL_v_5,5, NOVEL_v_5,6, NOVEL_v_5,7, NOVEL_v_5,8, NOVEL_v_5,9]]]\n", "[[[DARCY_h_0,0, <PERSON>ARC<PERSON>_h_0,1, <PERSON>ARC<PERSON>_h_0,2, DARCY_h_0,3, DARCY_h_0,4, DARCY_h_0,5], [DARCY_h_1,0, DARCY_h_1,1, DARCY_h_1,2, DARCY_h_1,3, DARCY_h_1,4, DARCY_h_1,5], [DARCY_h_2,0, DARCY_h_2,1, DARCY_h_2,2, DARCY_h_2,3, DARCY_h_2,4, DARCY_h_2,5], [DARCY_h_3,0, DARCY_h_3,1, DARCY_h_3,2, DARCY_h_3,3, DARCY_h_3,4, DARCY_h_3,5], [DARCY_h_4,0, DARCY_h_4,1, DARCY_h_4,2, DARCY_h_4,3, <PERSON>AR<PERSON><PERSON>_h_4,4, <PERSON>ARC<PERSON>_h_4,5], [DARCY_h_5,0, DARCY_h_5,1, DARCY_h_5,2, DARCY_h_5,3, DARCY_h_5,4, DARCY_h_5,5], [DARCY_h_6,0, DARCY_h_6,1, DARCY_h_6,2, DARCY_h_6,3, DARCY_h_6,4, DARCY_h_6,5], [DARCY_h_7,0, DARCY_h_7,1, DARCY_h_7,2, DARCY_h_7,3, DARCY_h_7,4, DARCY_h_7,5], [DARCY_h_8,0, DARCY_h_8,1, DARCY_h_8,2, DARCY_h_8,3, DARCY_h_8,4, DARCY_h_8,5], [DARCY_h_9,0, DARCY_h_9,1, DARCY_h_9,2, DARCY_h_9,3, DARCY_h_9,4, DARCY_h_9,5]], [[DARCY_v_0,0, DARCY_v_0,1, DARCY_v_0,2, DARCY_v_0,3, DARCY_v_0,4, DARCY_v_0,5, DARCY_v_0,6, DARCY_v_0,7, DARCY_v_0,8, DARCY_v_0,9], [DARCY_v_1,0, DARCY_v_1,1, DARCY_v_1,2, DARCY_v_1,3, DARCY_v_1,4, DARCY_v_1,5, DARCY_v_1,6, DARCY_v_1,7, DARCY_v_1,8, DARCY_v_1,9], [DARCY_v_2,0, DARCY_v_2,1, DARCY_v_2,2, DARCY_v_2,3, DARCY_v_2,4, DARCY_v_2,5, DARCY_v_2,6, DARCY_v_2,7, DARCY_v_2,8, DARCY_v_2,9], [DARCY_v_3,0, DARCY_v_3,1, DARCY_v_3,2, DARCY_v_3,3, DARCY_v_3,4, DARCY_v_3,5, DARCY_v_3,6, DARCY_v_3,7, DARCY_v_3,8, DARCY_v_3,9], [DARCY_v_4,0, DARCY_v_4,1, DARCY_v_4,2, DARCY_v_4,3, DARCY_v_4,4, DARCY_v_4,5, DARCY_v_4,6, DARCY_v_4,7, DARCY_v_4,8, DARCY_v_4,9], [DARCY_v_5,0, DARCY_v_5,1, DARCY_v_5,2, DARCY_v_5,3, DARCY_v_5,4, DARCY_v_5,5, DARCY_v_5,6, DARCY_v_5,7, DARCY_v_5,8, DARCY_v_5,9]]]\n", "[[[SENSE_h_0,0, SENSE_h_0,1, SENSE_h_0,2, SENSE_h_0,3, SENSE_h_0,4, SENSE_h_0,5], [SENSE_h_1,0, SENSE_h_1,1, SENSE_h_1,2, SENSE_h_1,3, SENSE_h_1,4, SENSE_h_1,5], [SENSE_h_2,0, SENSE_h_2,1, SENSE_h_2,2, SENSE_h_2,3, SENSE_h_2,4, SENSE_h_2,5], [SENSE_h_3,0, SENSE_h_3,1, SENSE_h_3,2, SENSE_h_3,3, SENSE_h_3,4, SENSE_h_3,5], [SENSE_h_4,0, SENSE_h_4,1, SENSE_h_4,2, SENSE_h_4,3, SENSE_h_4,4, SENSE_h_4,5], [SENSE_h_5,0, SENSE_h_5,1, SENSE_h_5,2, SENSE_h_5,3, SENSE_h_5,4, SENSE_h_5,5], [SENSE_h_6,0, SENSE_h_6,1, SENSE_h_6,2, SENSE_h_6,3, SENSE_h_6,4, SENSE_h_6,5], [SENSE_h_7,0, SENSE_h_7,1, SENSE_h_7,2, SENSE_h_7,3, SENSE_h_7,4, SENSE_h_7,5], [SENSE_h_8,0, SENSE_h_8,1, SENSE_h_8,2, SENSE_h_8,3, SENSE_h_8,4, SENSE_h_8,5], [SENSE_h_9,0, SENSE_h_9,1, SENSE_h_9,2, SENSE_h_9,3, SENSE_h_9,4, SENSE_h_9,5]], [[SENSE_v_0,0, SENSE_v_0,1, SENSE_v_0,2, SENSE_v_0,3, SENSE_v_0,4, SENSE_v_0,5, SENSE_v_0,6, SENSE_v_0,7, SENSE_v_0,8, SENSE_v_0,9], [SENSE_v_1,0, SENSE_v_1,1, SENSE_v_1,2, SENSE_v_1,3, SENSE_v_1,4, SENSE_v_1,5, SENSE_v_1,6, SENSE_v_1,7, SENSE_v_1,8, SENSE_v_1,9], [SENSE_v_2,0, SENSE_v_2,1, SENSE_v_2,2, SENSE_v_2,3, SENSE_v_2,4, SENSE_v_2,5, SENSE_v_2,6, SENSE_v_2,7, SENSE_v_2,8, SENSE_v_2,9], [SENSE_v_3,0, SENSE_v_3,1, SENSE_v_3,2, SENSE_v_3,3, SENSE_v_3,4, SENSE_v_3,5, SENSE_v_3,6, SENSE_v_3,7, SENSE_v_3,8, SENSE_v_3,9], [SENSE_v_4,0, SENSE_v_4,1, SENSE_v_4,2, SENSE_v_4,3, SENSE_v_4,4, SENSE_v_4,5, SENSE_v_4,6, SENSE_v_4,7, SENSE_v_4,8, SENSE_v_4,9], [SENSE_v_5,0, SENSE_v_5,1, SENSE_v_5,2, SENSE_v_5,3, SENSE_v_5,4, SENSE_v_5,5, SENSE_v_5,6, SENSE_v_5,7, SENSE_v_5,8, SENSE_v_5,9]]]\n", "[[[EMMA_h_0,0, <PERSON><PERSON><PERSON>_h_0,1, E<PERSON><PERSON>_h_0,2, EMMA_h_0,3, EMMA_h_0,4, EMMA_h_0,5, EMMA_h_0,6], [EMMA_h_1,0, EMMA_h_1,1, EMMA_h_1,2, EMMA_h_1,3, EMMA_h_1,4, EMMA_h_1,5, EMMA_h_1,6], [EMMA_h_2,0, EMMA_h_2,1, EMMA_h_2,2, EMMA_h_2,3, EMMA_h_2,4, EMMA_h_2,5, EMMA_h_2,6], [EMMA_h_3,0, EMMA_h_3,1, EMMA_h_3,2, EMMA_h_3,3, EMMA_h_3,4, EMMA_h_3,5, EMMA_h_3,6], [EMMA_h_4,0, EMMA_h_4,1, EMM<PERSON>_h_4,2, <PERSON><PERSON><PERSON><PERSON>h_4,3, <PERSON><PERSON><PERSON><PERSON>h_4,4, EMMA_h_4,5, EMM<PERSON>_h_4,6], [EMMA_h_5,0, EMMA_h_5,1, EMMA_h_5,2, EMMA_h_5,3, EMMA_h_5,4, <PERSON>MMA_h_5,5, EMMA_h_5,6], [<PERSON>MMA_h_6,0, EMMA_h_6,1, EMMA_h_6,2, <PERSON>MMA_h_6,3, <PERSON>MMA_h_6,4, EMMA_h_6,5, EMMA_h_6,6], [EMMA_h_7,0, EMMA_h_7,1, EMMA_h_7,2, EMMA_h_7,3, EMMA_h_7,4, EMMA_h_7,5, EMMA_h_7,6], [EMMA_h_8,0, EMMA_h_8,1, EMMA_h_8,2, EMMA_h_8,3, EMMA_h_8,4, EMMA_h_8,5, EMMA_h_8,6], [EMMA_h_9,0, EMMA_h_9,1, EMMA_h_9,2, EMMA_h_9,3, EMMA_h_9,4, EMMA_h_9,5, EMMA_h_9,6]], [[EMMA_v_0,0, EMMA_v_0,1, EMMA_v_0,2, EMMA_v_0,3, EMMA_v_0,4, EMMA_v_0,5, EMMA_v_0,6, EMMA_v_0,7, EMMA_v_0,8, EMMA_v_0,9], [EMMA_v_1,0, EMMA_v_1,1, EMMA_v_1,2, EMMA_v_1,3, EMMA_v_1,4, EMMA_v_1,5, EMMA_v_1,6, EMMA_v_1,7, EMMA_v_1,8, EMMA_v_1,9], [EMMA_v_2,0, EMMA_v_2,1, EMMA_v_2,2, EMMA_v_2,3, EMMA_v_2,4, EMMA_v_2,5, EMMA_v_2,6, EMMA_v_2,7, EMMA_v_2,8, EMMA_v_2,9], [EMMA_v_3,0, EMMA_v_3,1, EMMA_v_3,2, EMMA_v_3,3, EMMA_v_3,4, EMMA_v_3,5, EMMA_v_3,6, EMMA_v_3,7, EMMA_v_3,8, EMMA_v_3,9], [EMMA_v_4,0, EMMA_v_4,1, EMMA_v_4,2, EMMA_v_4,3, EMMA_v_4,4, EMMA_v_4,5, EMMA_v_4,6, EMMA_v_4,7, EMMA_v_4,8, EMMA_v_4,9], [EMMA_v_5,0, EMMA_v_5,1, EMMA_v_5,2, EMMA_v_5,3, EMMA_v_5,4, EMMA_v_5,5, EMMA_v_5,6, EMMA_v_5,7, EMMA_v_5,8, EMMA_v_5,9], [EMMA_v_6,0, EMMA_v_6,1, EMMA_v_6,2, EMMA_v_6,3, EMMA_v_6,4, EMMA_v_6,5, EMMA_v_6,6, EMMA_v_6,7, EMMA_v_6,8, EMMA_v_6,9]]]\n", "[[[ESTATE_h_0,0, ESTATE_h_0,1, <PERSON>STATE_h_0,2, ESTATE_h_0,3, ESTATE_h_0,4], [ESTATE_h_1,0, ESTATE_h_1,1, ESTATE_h_1,2, ESTATE_h_1,3, ESTATE_h_1,4], [ESTATE_h_2,0, ESTATE_h_2,1, ESTATE_h_2,2, ESTATE_h_2,3, ESTATE_h_2,4], [ESTATE_h_3,0, ESTATE_h_3,1, ESTATE_h_3,2, ESTATE_h_3,3, ESTATE_h_3,4], [ESTATE_h_4,0, ESTATE_h_4,1, ESTATE_h_4,2, ESTATE_h_4,3, ESTATE_h_4,4], [ESTATE_h_5,0, ESTATE_h_5,1, <PERSON>STATE_h_5,2, <PERSON><PERSON><PERSON><PERSON>_h_5,3, ESTATE_h_5,4], [ESTATE_h_6,0, ESTATE_h_6,1, ESTATE_h_6,2, ESTATE_h_6,3, ESTATE_h_6,4], [ESTATE_h_7,0, ESTATE_h_7,1, ESTATE_h_7,2, ESTATE_h_7,3, ESTATE_h_7,4], [ESTATE_h_8,0, ESTATE_h_8,1, ESTATE_h_8,2, ESTATE_h_8,3, ESTATE_h_8,4], [ESTATE_h_9,0, ESTATE_h_9,1, ESTATE_h_9,2, ESTATE_h_9,3, ESTATE_h_9,4]], [[ESTATE_v_0,0, ESTATE_v_0,1, ESTATE_v_0,2, ESTATE_v_0,3, ESTATE_v_0,4, ESTATE_v_0,5, ESTATE_v_0,6, ESTATE_v_0,7, ESTATE_v_0,8, ESTATE_v_0,9], [ESTATE_v_1,0, ESTATE_v_1,1, ESTATE_v_1,2, ESTATE_v_1,3, ESTATE_v_1,4, ESTATE_v_1,5, ESTATE_v_1,6, ESTATE_v_1,7, ESTATE_v_1,8, ESTATE_v_1,9], [ESTATE_v_2,0, ESTATE_v_2,1, ESTATE_v_2,2, ESTATE_v_2,3, ESTATE_v_2,4, ESTATE_v_2,5, ESTATE_v_2,6, ESTATE_v_2,7, ESTATE_v_2,8, ESTATE_v_2,9], [ESTATE_v_3,0, ESTATE_v_3,1, ESTATE_v_3,2, ESTATE_v_3,3, ESTATE_v_3,4, ESTATE_v_3,5, ESTATE_v_3,6, ESTATE_v_3,7, ESTATE_v_3,8, ESTATE_v_3,9], [ESTATE_v_4,0, ESTATE_v_4,1, ESTATE_v_4,2, ESTATE_v_4,3, ESTATE_v_4,4, ESTATE_v_4,5, ESTATE_v_4,6, ESTATE_v_4,7, ESTATE_v_4,8, ESTATE_v_4,9]]]\n", "[[[BENNET_h_0,0, BENNET_h_0,1, BENNET_h_0,2, BENNET_h_0,3, BENNET_h_0,4], [BENNET_h_1,0, BENNET_h_1,1, BENNET_h_1,2, BENNET_h_1,3, BENNET_h_1,4], [BENNET_h_2,0, BENNET_h_2,1, BENNET_h_2,2, BENNET_h_2,3, BENNET_h_2,4], [BENNET_h_3,0, BENNET_h_3,1, BENNET_h_3,2, BENNET_h_3,3, BENNET_h_3,4], [BENNET_h_4,0, BENNET_h_4,1, BENNET_h_4,2, BENNET_h_4,3, BENNET_h_4,4], [BENNET_h_5,0, BENNET_h_5,1, BENNET_h_5,2, BENNET_h_5,3, BENNET_h_5,4], [BENNET_h_6,0, BENNET_h_6,1, BENNET_h_6,2, BENNET_h_6,3, BENNET_h_6,4], [BENNET_h_7,0, BENNET_h_7,1, BENNET_h_7,2, BENNET_h_7,3, BENNET_h_7,4], [BENNET_h_8,0, BENNET_h_8,1, BENNET_h_8,2, BENNET_h_8,3, BENNET_h_8,4], [BENNET_h_9,0, BENNET_h_9,1, BENNET_h_9,2, BENNET_h_9,3, BENNET_h_9,4]], [[BENNET_v_0,0, BENNET_v_0,1, BENNET_v_0,2, BENNET_v_0,3, BENNET_v_0,4, BENNET_v_0,5, BENNET_v_0,6, BENNET_v_0,7, BENNET_v_0,8, BENNET_v_0,9], [BENNET_v_1,0, BENNET_v_1,1, BENNET_v_1,2, BENNET_v_1,3, BENNET_v_1,4, BENNET_v_1,5, BENNET_v_1,6, BENNET_v_1,7, BENNET_v_1,8, BENNET_v_1,9], [BENNET_v_2,0, BENNET_v_2,1, BENNET_v_2,2, BENNET_v_2,3, BENNET_v_2,4, BENNET_v_2,5, BENNET_v_2,6, BENNET_v_2,7, BENNET_v_2,8, BENNET_v_2,9], [BENNET_v_3,0, BENNET_v_3,1, BENNET_v_3,2, BENNET_v_3,3, BENNET_v_3,4, BENNET_v_3,5, BENNET_v_3,6, BENNET_v_3,7, BENNET_v_3,8, BENNET_v_3,9], [BENNET_v_4,0, BENNET_v_4,1, BENNET_v_4,2, BENNET_v_4,3, BENNET_v_4,4, BENNET_v_4,5, BENNET_v_4,6, BENNET_v_4,7, BENNET_v_4,8, BENNET_v_4,9]]]\n", "[[[BATH_h_0,0, BATH_h_0,1, BATH_h_0,2, BATH_h_0,3, BATH_h_0,4, BATH_h_0,5, BATH_h_0,6], [BATH_h_1,0, BATH_h_1,1, BATH_h_1,2, BATH_h_1,3, BATH_h_1,4, BATH_h_1,5, BATH_h_1,6], [BATH_h_2,0, BATH_h_2,1, BATH_h_2,2, BATH_h_2,3, BATH_h_2,4, BATH_h_2,5, BATH_h_2,6], [BATH_h_3,0, BATH_h_3,1, BATH_h_3,2, BATH_h_3,3, BATH_h_3,4, BATH_h_3,5, BATH_h_3,6], [BATH_h_4,0, BATH_h_4,1, BATH_h_4,2, BATH_h_4,3, BATH_h_4,4, BATH_h_4,5, BATH_h_4,6], [BATH_h_5,0, BATH_h_5,1, BATH_h_5,2, BATH_h_5,3, BATH_h_5,4, BATH_h_5,5, BATH_h_5,6], [BATH_h_6,0, BATH_h_6,1, BATH_h_6,2, BATH_h_6,3, BATH_h_6,4, BATH_h_6,5, BATH_h_6,6], [BATH_h_7,0, BATH_h_7,1, BATH_h_7,2, BATH_h_7,3, BATH_h_7,4, BATH_h_7,5, BATH_h_7,6], [BATH_h_8,0, BATH_h_8,1, BATH_h_8,2, BATH_h_8,3, BATH_h_8,4, BATH_h_8,5, BATH_h_8,6], [BATH_h_9,0, BATH_h_9,1, BATH_h_9,2, BATH_h_9,3, BATH_h_9,4, BATH_h_9,5, BATH_h_9,6]], [[BATH_v_0,0, BATH_v_0,1, BATH_v_0,2, BATH_v_0,3, BATH_v_0,4, BATH_v_0,5, BATH_v_0,6, BATH_v_0,7, BATH_v_0,8, BATH_v_0,9], [BATH_v_1,0, BATH_v_1,1, BATH_v_1,2, BATH_v_1,3, BATH_v_1,4, BATH_v_1,5, BATH_v_1,6, BATH_v_1,7, BATH_v_1,8, BATH_v_1,9], [BATH_v_2,0, BATH_v_2,1, BATH_v_2,2, BATH_v_2,3, BATH_v_2,4, BATH_v_2,5, BATH_v_2,6, BATH_v_2,7, BATH_v_2,8, BATH_v_2,9], [BATH_v_3,0, BATH_v_3,1, BATH_v_3,2, BATH_v_3,3, BATH_v_3,4, BATH_v_3,5, BATH_v_3,6, BATH_v_3,7, BATH_v_3,8, BATH_v_3,9], [BATH_v_4,0, BATH_v_4,1, BATH_v_4,2, BATH_v_4,3, BATH_v_4,4, BATH_v_4,5, BATH_v_4,6, BATH_v_4,7, BATH_v_4,8, BATH_v_4,9], [BATH_v_5,0, BATH_v_5,1, BATH_v_5,2, BATH_v_5,3, BATH_v_5,4, BATH_v_5,5, BATH_v_5,6, BATH_v_5,7, BATH_v_5,8, BATH_v_5,9], [BATH_v_6,0, BATH_v_6,1, BATH_v_6,2, BATH_v_6,3, BATH_v_6,4, BATH_v_6,5, BATH_v_6,6, BATH_v_6,7, BATH_v_6,8, BATH_v_6,9]]]\n", "Executed in 0.5563 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "markdown", "source": ["If you've done this correctly, it should be working better, but we now have the problem that words do not all connect to each other.  Hence, we have to add a final constraint that all of the letters are connected.\n", "\n", "First we form an $N\\times N$ adjacency matrix which the value is true if word $i$ intersects with word $j$.  Then we use the 'is_fully_connected' construction that we developed in the notebook on SAT constructions."], "metadata": {"id": "R69urJyN9wlc"}}, {"cell_type": "code", "source": ["def is_fully_connected(s, adjacency):\n", "  # Size of the adjacency matrix\n", "  n_components = len(adjacency)\n", "  # We'll construct a N x N x log[N] array of variables\n", "  # The NxN variables in the first layer represent A, the variables in the second layer represent B and so on\n", "  n_layers = math.ceil(math.log(n_components,2))+1\n", "  connected = [[[ z3.Bool(\"conn_{%d,%d,%d}\"%((i,j,n))) for n in range(0, n_layers)] for j in range(0, n_components) ] for i in range(0, n_components) ]\n", "\n", "  # Constraint 1\n", "  # The value in the top layer of the connected structure is equal to the adjacency matrix\n", "  for i in range(n_components):\n", "    for j in range(n_components):\n", "      s.add(connected[i][j][0]==adjacency[i][j])\n", "\n", "  # Constraint 2\n", "  # Value at position [i,j] in layer n is value at position [i,j] of the binary matrix product of layer n-1 with itself\n", "  for n in range(1,n_layers):\n", "    for i in range(n_components):\n", "      for j in range(n_components):\n", "        matrix_entry_ij = False\n", "        for k in range(n_components):\n", "          matrix_entry_ij = Or(matrix_entry_ij, And(connected[i][k][n-1],connected[k][j][n-1]))\n", "        s.add(connected[i][j][n]==matrix_entry_ij)\n", "\n", "  # Constraint 3 -- any row of column of the matrix should be full of ones at the end (everything is connected)\n", "  for i in range(n_components):\n", "    s.add(connected[i][0][n_layers-1])\n", "\n", "  return s"], "metadata": {"id": "2uPPXENwLugr"}, "execution_count": 28, "outputs": []}, {"cell_type": "code", "source": ["# Helper routine that returns true if the current word is at position (i,j) in the grid\n", "def word_at_position_ij(i,j, placement_vars, word, grid_size):\n", "\n", "    relevant_placements = [] ;\n", "    # Deal with horizontal words first\n", "    for horz_pos in range(np.max([0, j-len(word)+1]), np.min([j+1, grid_size-len(word)+1])):\n", "      # First the horizontal words\n", "      relevant_placements.append(placement_vars[word][0][i][horz_pos])\n", "    # Then the vertical words\n", "    for vert_pos in range(np.max([0, i-len(word)+1]), np.min([i+1, grid_size-len(word)+1])):\n", "      relevant_placements.append(placement_vars[word][1][vert_pos][j])\n", "\n", "    return Or(relevant_placements) ;"], "metadata": {"id": "FhJPmmAOV3AS"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["def add_constraint_set4(s, placement_vars, letter_posns, words, grid_size):\n", "  # First lets create a new variable that represents the adjacency matrix of the words\n", "  adjacency = [[ z3.Bool(\"adj_{%d,%d}\"%((i,j))) for j in range(0, len(words)) ] for i in range(0, len(words)) ]\n", "\n", "  # Run through each word\n", "  for c_w1 in range(len(words)):\n", "    for c_w2 in range(c_w1, len(words)):\n", "      # If word indices are the same (i.e., c_w1=c_w2) then adjacency at c_w1,c_w2 is true\n", "      # TODO -- replace this line\n", "      s.add(adjacency[0][0])\n", "\n", "      word1 = words[c_w1]\n", "      word2 = words[c_w2]\n", "      # TODO determine if word1 and word2 intersect.  You can use the routine \"word_at_position_ij\" above\n", "      # Replace this line\n", "      words_intersect = True\n", "\n", "      # Set value and symmetric value of adjacency matrix\n", "      s.add(adjacency[c_w1][c_w2] == words_intersect)\n", "      s.add(adjacency[c_w2][c_w1] == adjacency[c_w1][c_w2])\n", "\n", "  # Add the constraint that the adjacency matrix must be fully connected\n", "  s = is_fully_connected(s, adjacency)\n", "  return s"], "metadata": {"id": "Xmzv8g_-RIid"}, "execution_count": 32, "outputs": []}, {"cell_type": "code", "source": ["# Let's see how this improves things (took 32 seconds to run for me, but might be longer)\n", "solve_crossword(words, 11, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M7pPhYTfaLEd", "outputId": "dbe3c395-a935-45a3-8c2d-d615e6b4ed39"}, "execution_count": 33, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[[J<PERSON><PERSON>_h_0,0, <PERSON><PERSON><PERSON><PERSON>h_0,1, <PERSON><PERSON><PERSON><PERSON>h_0,2, <PERSON><PERSON><PERSON><PERSON>h_0,3, <PERSON><PERSON><PERSON><PERSON>h_0,4, <PERSON>AN<PERSON><PERSON>h_0,5, JAN<PERSON>_h_0,6, <PERSON><PERSON><PERSON><PERSON>h_0,7], [JANE_h_1,0, <PERSON><PERSON><PERSON><PERSON>h_1,1, <PERSON><PERSON><PERSON><PERSON>h_1,2, JANE<PERSON>h_1,3, J<PERSON><PERSON><PERSON>h_1,4, JANE_h_1,5, JANE_h_1,6, <PERSON><PERSON><PERSON><PERSON>h_1,7], [JANE_h_2,0, JANE_h_2,1, JANE_h_2,2, JANE_h_2,3, JANE_h_2,4, JANE_h_2,5, JANE_h_2,6, <PERSON><PERSON><PERSON>_h_2,7], [JANE_h_3,0, JANE_h_3,1, <PERSON><PERSON><PERSON>_h_3,2, <PERSON><PERSON><PERSON>_h_3,3, JANE_h_3,4, <PERSON><PERSON><PERSON><PERSON>h_3,5, <PERSON><PERSON><PERSON><PERSON>h_3,6, <PERSON><PERSON><PERSON><PERSON>h_3,7], [<PERSON><PERSON><PERSON><PERSON>h_4,0, <PERSON><PERSON><PERSON><PERSON>h_4,1, <PERSON><PERSON><PERSON><PERSON>h_4,2, <PERSON><PERSON><PERSON><PERSON>h_4,3, <PERSON><PERSON><PERSON><PERSON>h_4,4, <PERSON><PERSON>E_h_4,5, JANE_h_4,6, JANE_h_4,7], [JANE_h_5,0, JANE_h_5,1, JANE_h_5,2, JANE_h_5,3, JANE_h_5,4, JANE_h_5,5, JANE_h_5,6, JANE_h_5,7], [JANE_h_6,0, JANE_h_6,1, JANE_h_6,2, JANE_h_6,3, JANE_h_6,4, JANE_h_6,5, JANE_h_6,6, JANE_h_6,7], [JANE_h_7,0, JANE_h_7,1, JANE_h_7,2, JANE_h_7,3, JANE_h_7,4, JANE_h_7,5, JANE_h_7,6, JANE_h_7,7], [JANE_h_8,0, JANE_h_8,1, JANE_h_8,2, JANE_h_8,3, JANE_h_8,4, JANE_h_8,5, JANE_h_8,6, JANE_h_8,7], [JANE_h_9,0, JANE_h_9,1, JANE_h_9,2, JANE_h_9,3, JANE_h_9,4, JANE_h_9,5, JANE_h_9,6, JANE_h_9,7], [JANE_h_10,0, JANE_h_10,1, JANE_h_10,2, JANE_h_10,3, JANE_h_10,4, JANE_h_10,5, JANE_h_10,6, JANE_h_10,7]], [[JANE_v_0,0, JANE_v_0,1, JANE_v_0,2, JANE_v_0,3, JANE_v_0,4, JANE_v_0,5, JANE_v_0,6, JANE_v_0,7, JANE_v_0,8, JANE_v_0,9, JANE_v_0,10], [JANE_v_1,0, JANE_v_1,1, JANE_v_1,2, JANE_v_1,3, JANE_v_1,4, JANE_v_1,5, JANE_v_1,6, JANE_v_1,7, JANE_v_1,8, JANE_v_1,9, JANE_v_1,10], [JANE_v_2,0, JANE_v_2,1, JANE_v_2,2, JANE_v_2,3, JANE_v_2,4, JANE_v_2,5, JANE_v_2,6, JANE_v_2,7, JANE_v_2,8, JANE_v_2,9, JANE_v_2,10], [JANE_v_3,0, JANE_v_3,1, JANE_v_3,2, JANE_v_3,3, JANE_v_3,4, JANE_v_3,5, JANE_v_3,6, JANE_v_3,7, JANE_v_3,8, JANE_v_3,9, JANE_v_3,10], [JANE_v_4,0, JANE_v_4,1, JANE_v_4,2, JANE_v_4,3, JANE_v_4,4, JANE_v_4,5, JANE_v_4,6, JANE_v_4,7, JANE_v_4,8, JANE_v_4,9, JANE_v_4,10], [JANE_v_5,0, JANE_v_5,1, JANE_v_5,2, JANE_v_5,3, JANE_v_5,4, JANE_v_5,5, JANE_v_5,6, JANE_v_5,7, JANE_v_5,8, JANE_v_5,9, JANE_v_5,10], [JANE_v_6,0, JANE_v_6,1, JANE_v_6,2, JANE_v_6,3, JANE_v_6,4, JANE_v_6,5, JANE_v_6,6, JANE_v_6,7, JANE_v_6,8, JANE_v_6,9, JANE_v_6,10], [JANE_v_7,0, JANE_v_7,1, JANE_v_7,2, JANE_v_7,3, JANE_v_7,4, JANE_v_7,5, JANE_v_7,6, JANE_v_7,7, JANE_v_7,8, JANE_v_7,9, JANE_v_7,10]]]\n", "[[[AUSTEN_h_0,0, AUSTEN_h_0,1, AUSTEN_h_0,2, AUSTEN_h_0,3, AUSTEN_h_0,4, AUSTEN_h_0,5], [AUSTEN_h_1,0, AUSTEN_h_1,1, AUSTEN_h_1,2, AUSTEN_h_1,3, AUSTEN_h_1,4, AUSTEN_h_1,5], [AUSTEN_h_2,0, AUSTEN_h_2,1, AUSTEN_h_2,2, AUSTEN_h_2,3, AUSTEN_h_2,4, AUSTEN_h_2,5], [AUSTEN_h_3,0, AUSTEN_h_3,1, AUSTEN_h_3,2, AUSTEN_h_3,3, AUSTEN_h_3,4, AUSTEN_h_3,5], [AUSTEN_h_4,0, AUSTEN_h_4,1, AUSTEN_h_4,2, AUSTEN_h_4,3, AUSTEN_h_4,4, AUSTEN_h_4,5], [AUSTEN_h_5,0, AUSTEN_h_5,1, AUSTEN_h_5,2, AUSTEN_h_5,3, AUSTEN_h_5,4, AUSTEN_h_5,5], [AUSTEN_h_6,0, AUSTEN_h_6,1, AUSTEN_h_6,2, AUSTEN_h_6,3, AUSTEN_h_6,4, AUSTEN_h_6,5], [AUSTEN_h_7,0, AUSTEN_h_7,1, AUSTEN_h_7,2, AUSTEN_h_7,3, AUSTEN_h_7,4, AUSTEN_h_7,5], [AUSTEN_h_8,0, AUSTEN_h_8,1, AUSTEN_h_8,2, AUSTEN_h_8,3, AUSTEN_h_8,4, AUSTEN_h_8,5], [AUSTEN_h_9,0, AUSTEN_h_9,1, AUSTEN_h_9,2, AUSTEN_h_9,3, AUSTEN_h_9,4, AUSTEN_h_9,5], [AUSTEN_h_10,0, AUSTEN_h_10,1, AUSTEN_h_10,2, AUSTEN_h_10,3, AUSTEN_h_10,4, AUSTEN_h_10,5]], [[AUSTEN_v_0,0, AUSTEN_v_0,1, AUSTEN_v_0,2, AUSTEN_v_0,3, AUSTEN_v_0,4, AUSTEN_v_0,5, AUSTEN_v_0,6, AUSTEN_v_0,7, AUSTEN_v_0,8, AUSTEN_v_0,9, AUSTEN_v_0,10], [AUSTEN_v_1,0, AUSTEN_v_1,1, AUSTEN_v_1,2, AUSTEN_v_1,3, AUSTEN_v_1,4, AUSTEN_v_1,5, AUSTEN_v_1,6, AUSTEN_v_1,7, AUSTEN_v_1,8, AUSTEN_v_1,9, AUSTEN_v_1,10], [AUSTEN_v_2,0, AUSTEN_v_2,1, AUSTEN_v_2,2, AUSTEN_v_2,3, AUSTEN_v_2,4, AUSTEN_v_2,5, AUSTEN_v_2,6, AUSTEN_v_2,7, AUSTEN_v_2,8, AUSTEN_v_2,9, AUSTEN_v_2,10], [AUSTEN_v_3,0, AUSTEN_v_3,1, AUSTEN_v_3,2, AUSTEN_v_3,3, AUSTEN_v_3,4, AUSTEN_v_3,5, AUSTEN_v_3,6, AUSTEN_v_3,7, AUSTEN_v_3,8, AUSTEN_v_3,9, AUSTEN_v_3,10], [AUSTEN_v_4,0, AUSTEN_v_4,1, AUSTEN_v_4,2, AUSTEN_v_4,3, AUSTEN_v_4,4, AUSTEN_v_4,5, AUSTEN_v_4,6, AUSTEN_v_4,7, AUSTEN_v_4,8, AUSTEN_v_4,9, AUSTEN_v_4,10], [AUSTEN_v_5,0, AUSTEN_v_5,1, AUSTEN_v_5,2, AUSTEN_v_5,3, AUSTEN_v_5,4, AUSTEN_v_5,5, AUSTEN_v_5,6, AUSTEN_v_5,7, AUSTEN_v_5,8, AUSTEN_v_5,9, AUSTEN_v_5,10]]]\n", "[[[PRIDE_h_0,0, <PERSON><PERSON><PERSON>_h_0,1, PR<PERSON><PERSON>_h_0,2, PRIDE_h_0,3, PRIDE_h_0,4, PRIDE_h_0,5, PRIDE_h_0,6], [PRIDE_h_1,0, PRIDE_h_1,1, PRIDE_h_1,2, PRIDE_h_1,3, PRIDE_h_1,4, PRIDE_h_1,5, PRIDE_h_1,6], [PRIDE_h_2,0, PRIDE_h_2,1, PRIDE_h_2,2, PRIDE_h_2,3, PRIDE_h_2,4, PRIDE_h_2,5, PRIDE_h_2,6], [PRIDE_h_3,0, PRIDE_h_3,1, PRIDE_h_3,2, PRIDE_h_3,3, PRIDE_h_3,4, PRIDE_h_3,5, PRIDE_h_3,6], [PRIDE_h_4,0, PRIDE_h_4,1, PR<PERSON>E_h_4,2, <PERSON><PERSON><PERSON>_h_4,3, PR<PERSON>E_h_4,4, PRIDE_h_4,5, PRIDE_h_4,6], [PRIDE_h_5,0, PRIDE_h_5,1, PRIDE_h_5,2, PRIDE_h_5,3, PRIDE_h_5,4, PRIDE_h_5,5, PRIDE_h_5,6], [PRIDE_h_6,0, PRIDE_h_6,1, PRIDE_h_6,2, PRIDE_h_6,3, PRIDE_h_6,4, PRIDE_h_6,5, PRIDE_h_6,6], [PRIDE_h_7,0, PRIDE_h_7,1, PRIDE_h_7,2, PRIDE_h_7,3, PRIDE_h_7,4, PRIDE_h_7,5, PRIDE_h_7,6], [PRIDE_h_8,0, PRIDE_h_8,1, PRIDE_h_8,2, PRIDE_h_8,3, PRIDE_h_8,4, PRIDE_h_8,5, PRIDE_h_8,6], [PRIDE_h_9,0, PRIDE_h_9,1, PRIDE_h_9,2, PRIDE_h_9,3, PRIDE_h_9,4, PRIDE_h_9,5, PRIDE_h_9,6], [PRIDE_h_10,0, PRIDE_h_10,1, PRIDE_h_10,2, PRIDE_h_10,3, PRIDE_h_10,4, PRIDE_h_10,5, PRIDE_h_10,6]], [[PRIDE_v_0,0, PRIDE_v_0,1, PRIDE_v_0,2, PRIDE_v_0,3, PRIDE_v_0,4, PRIDE_v_0,5, PRIDE_v_0,6, PRIDE_v_0,7, PRIDE_v_0,8, PRIDE_v_0,9, PRIDE_v_0,10], [PRIDE_v_1,0, PRIDE_v_1,1, PRIDE_v_1,2, PRIDE_v_1,3, PRIDE_v_1,4, PRIDE_v_1,5, PRIDE_v_1,6, PRIDE_v_1,7, PRIDE_v_1,8, PRIDE_v_1,9, PRIDE_v_1,10], [PRIDE_v_2,0, PRIDE_v_2,1, PRIDE_v_2,2, PRIDE_v_2,3, PRIDE_v_2,4, PRIDE_v_2,5, PRIDE_v_2,6, PRIDE_v_2,7, PRIDE_v_2,8, PRIDE_v_2,9, PRIDE_v_2,10], [PRIDE_v_3,0, PRIDE_v_3,1, PRIDE_v_3,2, PRIDE_v_3,3, PRIDE_v_3,4, PRIDE_v_3,5, PRIDE_v_3,6, PRIDE_v_3,7, PRIDE_v_3,8, PRIDE_v_3,9, PRIDE_v_3,10], [PRIDE_v_4,0, PRIDE_v_4,1, PRIDE_v_4,2, PRIDE_v_4,3, PRIDE_v_4,4, PRIDE_v_4,5, PRIDE_v_4,6, PRIDE_v_4,7, PRIDE_v_4,8, PRIDE_v_4,9, PRIDE_v_4,10], [PRIDE_v_5,0, PRIDE_v_5,1, PRIDE_v_5,2, PRIDE_v_5,3, PRIDE_v_5,4, PRIDE_v_5,5, PRIDE_v_5,6, PRIDE_v_5,7, PRIDE_v_5,8, PRIDE_v_5,9, PRIDE_v_5,10], [PRIDE_v_6,0, PRIDE_v_6,1, PRIDE_v_6,2, PRIDE_v_6,3, PRIDE_v_6,4, PRIDE_v_6,5, PRIDE_v_6,6, PRIDE_v_6,7, PRIDE_v_6,8, PRIDE_v_6,9, PRIDE_v_6,10]]]\n", "[[[NOVEL_h_0,0, NOVE<PERSON>_h_0,1, NOVEL_h_0,2, NOVEL_h_0,3, NOVEL_h_0,4, NOVEL_h_0,5, NOVEL_h_0,6], [NOVEL_h_1,0, NOVEL_h_1,1, NOVEL_h_1,2, NOVEL_h_1,3, NOVEL_h_1,4, NOVEL_h_1,5, NOVEL_h_1,6], [NOVEL_h_2,0, NOVEL_h_2,1, NOVEL_h_2,2, NOVEL_h_2,3, NOVEL_h_2,4, NOVEL_h_2,5, NOVEL_h_2,6], [NOVEL_h_3,0, NOVEL_h_3,1, NOVEL_h_3,2, NOVEL_h_3,3, NOVEL_h_3,4, NOVEL_h_3,5, NOVEL_h_3,6], [NOVEL_h_4,0, NOVEL_h_4,1, NOVEL_h_4,2, NOVEL_h_4,3, NOVEL_h_4,4, NOVEL_h_4,5, NOVEL_h_4,6], [NOVEL_h_5,0, NOVEL_h_5,1, NOVEL_h_5,2, NOVEL_h_5,3, NOVEL_h_5,4, NOVEL_h_5,5, NOVEL_h_5,6], [NOVEL_h_6,0, NOVEL_h_6,1, NOVEL_h_6,2, NOVEL_h_6,3, NOVEL_h_6,4, NOVEL_h_6,5, NOVEL_h_6,6], [NOVEL_h_7,0, NOVEL_h_7,1, NOVEL_h_7,2, NOVEL_h_7,3, NOVEL_h_7,4, NOVEL_h_7,5, NOVEL_h_7,6], [NOVEL_h_8,0, NOVEL_h_8,1, NOVEL_h_8,2, NOVEL_h_8,3, NOVEL_h_8,4, NOVEL_h_8,5, NOVEL_h_8,6], [NOVEL_h_9,0, NOVEL_h_9,1, NOVEL_h_9,2, NOVEL_h_9,3, NOVEL_h_9,4, NOVEL_h_9,5, NOVEL_h_9,6], [NOVEL_h_10,0, NOVEL_h_10,1, NOVEL_h_10,2, NOVEL_h_10,3, NOVEL_h_10,4, NOVEL_h_10,5, NOVEL_h_10,6]], [[NOVEL_v_0,0, NOVEL_v_0,1, NOVEL_v_0,2, NOVEL_v_0,3, NOVEL_v_0,4, NOVEL_v_0,5, NOVEL_v_0,6, NOVEL_v_0,7, NOVEL_v_0,8, NOVEL_v_0,9, NOVEL_v_0,10], [NOVEL_v_1,0, NOVEL_v_1,1, NOVEL_v_1,2, NOVEL_v_1,3, NOVEL_v_1,4, NOVEL_v_1,5, NOVEL_v_1,6, NOVEL_v_1,7, NOVEL_v_1,8, NOVEL_v_1,9, NOVEL_v_1,10], [NOVEL_v_2,0, NOVEL_v_2,1, NOVEL_v_2,2, NOVEL_v_2,3, NOVEL_v_2,4, NOVEL_v_2,5, NOVEL_v_2,6, NOVEL_v_2,7, NOVEL_v_2,8, NOVEL_v_2,9, NOVEL_v_2,10], [NOVEL_v_3,0, NOVEL_v_3,1, NOVEL_v_3,2, NOVEL_v_3,3, NOVEL_v_3,4, NOVEL_v_3,5, NOVEL_v_3,6, NOVEL_v_3,7, NOVEL_v_3,8, NOVEL_v_3,9, NOVEL_v_3,10], [NOVEL_v_4,0, NOVEL_v_4,1, NOVEL_v_4,2, NOVEL_v_4,3, NOVEL_v_4,4, NOVEL_v_4,5, NOVEL_v_4,6, NOVEL_v_4,7, NOVEL_v_4,8, NOVEL_v_4,9, NOVEL_v_4,10], [NOVEL_v_5,0, NOVEL_v_5,1, NOVEL_v_5,2, NOVEL_v_5,3, NOVEL_v_5,4, NOVEL_v_5,5, NOVEL_v_5,6, NOVEL_v_5,7, NOVEL_v_5,8, NOVEL_v_5,9, NOVEL_v_5,10], [NOVEL_v_6,0, NOVEL_v_6,1, NOVEL_v_6,2, NOVEL_v_6,3, NOVEL_v_6,4, NOVEL_v_6,5, NOVEL_v_6,6, NOVEL_v_6,7, NOVEL_v_6,8, NOVEL_v_6,9, NOVEL_v_6,10]]]\n", "[[[DARCY_h_0,0, <PERSON>ARC<PERSON>_h_0,1, <PERSON>ARC<PERSON>_h_0,2, DARCY_h_0,3, DARCY_h_0,4, DARCY_h_0,5, DARCY_h_0,6], [DARCY_h_1,0, DARCY_h_1,1, DARCY_h_1,2, DARCY_h_1,3, DARCY_h_1,4, DARCY_h_1,5, DARCY_h_1,6], [DARCY_h_2,0, DARCY_h_2,1, DARCY_h_2,2, DARCY_h_2,3, DARCY_h_2,4, DARCY_h_2,5, DARCY_h_2,6], [DARCY_h_3,0, DARCY_h_3,1, DARCY_h_3,2, DARCY_h_3,3, DARCY_h_3,4, DARCY_h_3,5, DARCY_h_3,6], [DARC<PERSON>_h_4,0, <PERSON>ARC<PERSON>_h_4,1, DARCY_h_4,2, DARCY_h_4,3, DARCY_h_4,4, DARCY_h_4,5, DARCY_h_4,6], [DARCY_h_5,0, DARCY_h_5,1, DARCY_h_5,2, DARCY_h_5,3, DARCY_h_5,4, DARCY_h_5,5, DARCY_h_5,6], [DARCY_h_6,0, DARCY_h_6,1, DARCY_h_6,2, DARCY_h_6,3, DARCY_h_6,4, DARCY_h_6,5, DARCY_h_6,6], [DARCY_h_7,0, DARCY_h_7,1, DARCY_h_7,2, DARCY_h_7,3, DARCY_h_7,4, DARCY_h_7,5, DARCY_h_7,6], [DARCY_h_8,0, DARCY_h_8,1, DARCY_h_8,2, DARCY_h_8,3, DARCY_h_8,4, DARCY_h_8,5, DARCY_h_8,6], [DARCY_h_9,0, DARCY_h_9,1, DARCY_h_9,2, DARCY_h_9,3, DARCY_h_9,4, DARCY_h_9,5, DARCY_h_9,6], [DARCY_h_10,0, DARCY_h_10,1, DARCY_h_10,2, DARCY_h_10,3, DARCY_h_10,4, DARCY_h_10,5, DARCY_h_10,6]], [[DARCY_v_0,0, DARCY_v_0,1, DARCY_v_0,2, DARCY_v_0,3, DARCY_v_0,4, DARCY_v_0,5, DARCY_v_0,6, DARCY_v_0,7, DARCY_v_0,8, DARCY_v_0,9, DARCY_v_0,10], [DARCY_v_1,0, DARCY_v_1,1, DARCY_v_1,2, DARCY_v_1,3, DARCY_v_1,4, DARCY_v_1,5, DARCY_v_1,6, DARCY_v_1,7, DARCY_v_1,8, DARCY_v_1,9, DARCY_v_1,10], [DARCY_v_2,0, DARCY_v_2,1, DARCY_v_2,2, DARCY_v_2,3, DARCY_v_2,4, DARCY_v_2,5, DARCY_v_2,6, DARCY_v_2,7, DARCY_v_2,8, DARCY_v_2,9, DARCY_v_2,10], [DARCY_v_3,0, DARCY_v_3,1, DARCY_v_3,2, DARCY_v_3,3, DARCY_v_3,4, DARCY_v_3,5, DARCY_v_3,6, DARCY_v_3,7, DARCY_v_3,8, DARCY_v_3,9, DARCY_v_3,10], [DARCY_v_4,0, DARCY_v_4,1, DARCY_v_4,2, DARCY_v_4,3, DARCY_v_4,4, DARCY_v_4,5, DARCY_v_4,6, DARCY_v_4,7, DARCY_v_4,8, DARCY_v_4,9, DARCY_v_4,10], [DARCY_v_5,0, DARCY_v_5,1, DARCY_v_5,2, DARCY_v_5,3, DARCY_v_5,4, DARCY_v_5,5, DARCY_v_5,6, DARCY_v_5,7, DARCY_v_5,8, DARCY_v_5,9, DARCY_v_5,10], [DARCY_v_6,0, DARCY_v_6,1, DARCY_v_6,2, DARCY_v_6,3, DARCY_v_6,4, DARCY_v_6,5, DARCY_v_6,6, DARCY_v_6,7, DARCY_v_6,8, DARCY_v_6,9, DARCY_v_6,10]]]\n", "[[[SENSE_h_0,0, SENSE_h_0,1, SENSE_h_0,2, SENSE_h_0,3, SENSE_h_0,4, SENSE_h_0,5, SENSE_h_0,6], [SENSE_h_1,0, SENSE_h_1,1, SENSE_h_1,2, SENSE_h_1,3, SENSE_h_1,4, SENSE_h_1,5, SENSE_h_1,6], [SENSE_h_2,0, SENSE_h_2,1, SENSE_h_2,2, SENSE_h_2,3, SENSE_h_2,4, SENSE_h_2,5, SENSE_h_2,6], [SENSE_h_3,0, SENSE_h_3,1, SENSE_h_3,2, SENSE_h_3,3, SENSE_h_3,4, SENSE_h_3,5, SENSE_h_3,6], [SENSE_h_4,0, SENSE_h_4,1, SENSE_h_4,2, SENSE_h_4,3, SENSE_h_4,4, SENSE_h_4,5, SENSE_h_4,6], [SENSE_h_5,0, SENSE_h_5,1, SENSE_h_5,2, SENSE_h_5,3, SENSE_h_5,4, SENSE_h_5,5, SENSE_h_5,6], [SENSE_h_6,0, SENSE_h_6,1, SENSE_h_6,2, SENSE_h_6,3, SENSE_h_6,4, SENSE_h_6,5, SENSE_h_6,6], [SENSE_h_7,0, SENSE_h_7,1, SENSE_h_7,2, SENSE_h_7,3, SENSE_h_7,4, SENSE_h_7,5, SENSE_h_7,6], [SENSE_h_8,0, SENSE_h_8,1, SENSE_h_8,2, SENSE_h_8,3, SENSE_h_8,4, SENSE_h_8,5, SENSE_h_8,6], [SENSE_h_9,0, SENSE_h_9,1, SENSE_h_9,2, SENSE_h_9,3, SENSE_h_9,4, SENSE_h_9,5, SENSE_h_9,6], [SENSE_h_10,0, SENSE_h_10,1, SENSE_h_10,2, SENSE_h_10,3, SENSE_h_10,4, SENSE_h_10,5, SENSE_h_10,6]], [[SENSE_v_0,0, SENSE_v_0,1, SENSE_v_0,2, SENSE_v_0,3, SENSE_v_0,4, SENSE_v_0,5, SENSE_v_0,6, SENSE_v_0,7, SENSE_v_0,8, SENSE_v_0,9, SENSE_v_0,10], [SENSE_v_1,0, SENSE_v_1,1, SENSE_v_1,2, SENSE_v_1,3, SENSE_v_1,4, SENSE_v_1,5, SENSE_v_1,6, SENSE_v_1,7, SENSE_v_1,8, SENSE_v_1,9, SENSE_v_1,10], [SENSE_v_2,0, SENSE_v_2,1, SENSE_v_2,2, SENSE_v_2,3, SENSE_v_2,4, SENSE_v_2,5, SENSE_v_2,6, SENSE_v_2,7, SENSE_v_2,8, SENSE_v_2,9, SENSE_v_2,10], [SENSE_v_3,0, SENSE_v_3,1, SENSE_v_3,2, SENSE_v_3,3, SENSE_v_3,4, SENSE_v_3,5, SENSE_v_3,6, SENSE_v_3,7, SENSE_v_3,8, SENSE_v_3,9, SENSE_v_3,10], [SENSE_v_4,0, SENSE_v_4,1, SENSE_v_4,2, SENSE_v_4,3, SENSE_v_4,4, SENSE_v_4,5, SENSE_v_4,6, SENSE_v_4,7, SENSE_v_4,8, SENSE_v_4,9, SENSE_v_4,10], [SENSE_v_5,0, SENSE_v_5,1, SENSE_v_5,2, SENSE_v_5,3, SENSE_v_5,4, SENSE_v_5,5, SENSE_v_5,6, SENSE_v_5,7, SENSE_v_5,8, SENSE_v_5,9, SENSE_v_5,10], [SENSE_v_6,0, SENSE_v_6,1, SENSE_v_6,2, SENSE_v_6,3, SENSE_v_6,4, SENSE_v_6,5, SENSE_v_6,6, SENSE_v_6,7, SENSE_v_6,8, SENSE_v_6,9, SENSE_v_6,10]]]\n", "[[[EMMA_h_0,0, EMM<PERSON>_h_0,1, EMM<PERSON>_h_0,2, EMMA_h_0,3, EMMA_h_0,4, EMMA_h_0,5, EMMA_h_0,6, EMMA_h_0,7], [EMMA_h_1,0, EMMA_h_1,1, EMMA_h_1,2, EMMA_h_1,3, EMMA_h_1,4, EMMA_h_1,5, EMMA_h_1,6, EMMA_h_1,7], [EMMA_h_2,0, EMMA_h_2,1, EMMA_h_2,2, EMMA_h_2,3, EMMA_h_2,4, EMMA_h_2,5, EMMA_h_2,6, EMMA_h_2,7], [EMMA_h_3,0, EMMA_h_3,1, EMMA_h_3,2, EMMA_h_3,3, EMMA_h_3,4, EMMA_h_3,5, EMMA_h_3,6, <PERSON><PERSON><PERSON>_h_3,7], [EMMA_h_4,0, EMM<PERSON>_h_4,1, EMMA_h_4,2, EMMA_h_4,3, EMMA_h_4,4, <PERSON><PERSON><PERSON>_h_4,5, EMMA_h_4,6, EMMA_h_4,7], [EMMA_h_5,0, EMMA_h_5,1, <PERSON>MMA_h_5,2, <PERSON>MMA_h_5,3, EMMA_h_5,4, <PERSON>MMA_h_5,5, EMMA_h_5,6, EMMA_h_5,7], [EMMA_h_6,0, EMMA_h_6,1, EMMA_h_6,2, EMMA_h_6,3, EMMA_h_6,4, EMMA_h_6,5, EMMA_h_6,6, EMMA_h_6,7], [EMMA_h_7,0, EMMA_h_7,1, EMMA_h_7,2, EMMA_h_7,3, EMMA_h_7,4, EMMA_h_7,5, EMMA_h_7,6, EMMA_h_7,7], [EMMA_h_8,0, EMMA_h_8,1, EMMA_h_8,2, EMMA_h_8,3, EMMA_h_8,4, EMMA_h_8,5, EMMA_h_8,6, EMMA_h_8,7], [EMMA_h_9,0, EMMA_h_9,1, EMMA_h_9,2, EMMA_h_9,3, EMMA_h_9,4, EMMA_h_9,5, EMMA_h_9,6, EMMA_h_9,7], [EMMA_h_10,0, EMMA_h_10,1, EMMA_h_10,2, EMMA_h_10,3, EMMA_h_10,4, EMMA_h_10,5, EMMA_h_10,6, EMMA_h_10,7]], [[EMMA_v_0,0, EMMA_v_0,1, EMMA_v_0,2, EMMA_v_0,3, EMMA_v_0,4, EMMA_v_0,5, EMMA_v_0,6, EMMA_v_0,7, EMMA_v_0,8, EMMA_v_0,9, EMMA_v_0,10], [EMMA_v_1,0, EMMA_v_1,1, EMMA_v_1,2, EMMA_v_1,3, EMMA_v_1,4, EMMA_v_1,5, EMMA_v_1,6, EMMA_v_1,7, EMMA_v_1,8, EMMA_v_1,9, EMMA_v_1,10], [EMMA_v_2,0, EMMA_v_2,1, EMMA_v_2,2, EMMA_v_2,3, EMMA_v_2,4, EMMA_v_2,5, EMMA_v_2,6, EMMA_v_2,7, EMMA_v_2,8, EMMA_v_2,9, EMMA_v_2,10], [EMMA_v_3,0, EMMA_v_3,1, EMMA_v_3,2, EMMA_v_3,3, EMMA_v_3,4, EMMA_v_3,5, EMMA_v_3,6, EMMA_v_3,7, EMMA_v_3,8, EMMA_v_3,9, EMMA_v_3,10], [EMMA_v_4,0, EMMA_v_4,1, EMMA_v_4,2, EMMA_v_4,3, EMMA_v_4,4, EMMA_v_4,5, EMMA_v_4,6, EMMA_v_4,7, EMMA_v_4,8, EMMA_v_4,9, EMMA_v_4,10], [EMMA_v_5,0, EMMA_v_5,1, EMMA_v_5,2, EMMA_v_5,3, EMMA_v_5,4, EMMA_v_5,5, EMMA_v_5,6, EMMA_v_5,7, EMMA_v_5,8, EMMA_v_5,9, EMMA_v_5,10], [EMMA_v_6,0, EMMA_v_6,1, EMMA_v_6,2, EMMA_v_6,3, EMMA_v_6,4, EMMA_v_6,5, EMMA_v_6,6, EMMA_v_6,7, EMMA_v_6,8, EMMA_v_6,9, EMMA_v_6,10], [EMMA_v_7,0, EMMA_v_7,1, EMMA_v_7,2, EMMA_v_7,3, EMMA_v_7,4, EMMA_v_7,5, EMMA_v_7,6, EMMA_v_7,7, EMMA_v_7,8, EMMA_v_7,9, EMMA_v_7,10]]]\n", "[[[ESTATE_h_0,0, ESTATE_h_0,1, <PERSON>STATE_h_0,2, ESTATE_h_0,3, ESTATE_h_0,4, ESTATE_h_0,5], [ESTATE_h_1,0, ESTATE_h_1,1, ESTATE_h_1,2, ESTATE_h_1,3, ESTATE_h_1,4, ESTATE_h_1,5], [ESTATE_h_2,0, ESTATE_h_2,1, ESTATE_h_2,2, ESTATE_h_2,3, ESTATE_h_2,4, ESTATE_h_2,5], [ESTATE_h_3,0, ESTATE_h_3,1, ESTATE_h_3,2, ESTATE_h_3,3, ESTATE_h_3,4, ESTATE_h_3,5], [ESTATE_h_4,0, ESTATE_h_4,1, ESTATE_h_4,2, ESTATE_h_4,3, EST<PERSON>E_h_4,4, ESTATE_h_4,5], [ESTATE_h_5,0, ESTATE_h_5,1, ESTATE_h_5,2, ESTATE_h_5,3, ESTATE_h_5,4, ESTATE_h_5,5], [ESTATE_h_6,0, ESTATE_h_6,1, ESTATE_h_6,2, ESTATE_h_6,3, ESTATE_h_6,4, ESTATE_h_6,5], [ESTATE_h_7,0, ESTATE_h_7,1, ESTATE_h_7,2, ESTATE_h_7,3, ESTATE_h_7,4, ESTATE_h_7,5], [ESTATE_h_8,0, ESTATE_h_8,1, ESTATE_h_8,2, ESTATE_h_8,3, ESTATE_h_8,4, ESTATE_h_8,5], [ESTATE_h_9,0, ESTATE_h_9,1, ESTATE_h_9,2, ESTATE_h_9,3, ESTATE_h_9,4, ESTATE_h_9,5], [ESTATE_h_10,0, ESTATE_h_10,1, ESTATE_h_10,2, ESTATE_h_10,3, ESTATE_h_10,4, ESTATE_h_10,5]], [[ESTATE_v_0,0, ESTATE_v_0,1, ESTATE_v_0,2, ESTATE_v_0,3, ESTATE_v_0,4, ESTATE_v_0,5, ESTATE_v_0,6, ESTATE_v_0,7, ESTATE_v_0,8, ESTATE_v_0,9, ESTATE_v_0,10], [ESTATE_v_1,0, ESTATE_v_1,1, ESTATE_v_1,2, ESTATE_v_1,3, ESTATE_v_1,4, ESTATE_v_1,5, ESTATE_v_1,6, ESTATE_v_1,7, ESTATE_v_1,8, ESTATE_v_1,9, ESTATE_v_1,10], [ESTATE_v_2,0, ESTATE_v_2,1, ESTATE_v_2,2, ESTATE_v_2,3, ESTATE_v_2,4, ESTATE_v_2,5, ESTATE_v_2,6, ESTATE_v_2,7, ESTATE_v_2,8, ESTATE_v_2,9, ESTATE_v_2,10], [ESTATE_v_3,0, ESTATE_v_3,1, ESTATE_v_3,2, ESTATE_v_3,3, ESTATE_v_3,4, ESTATE_v_3,5, ESTATE_v_3,6, ESTATE_v_3,7, ESTATE_v_3,8, ESTATE_v_3,9, ESTATE_v_3,10], [ESTATE_v_4,0, ESTATE_v_4,1, ESTATE_v_4,2, ESTATE_v_4,3, ESTATE_v_4,4, ESTATE_v_4,5, ESTATE_v_4,6, ESTATE_v_4,7, ESTATE_v_4,8, ESTATE_v_4,9, ESTATE_v_4,10], [ESTATE_v_5,0, ESTATE_v_5,1, ESTATE_v_5,2, ESTATE_v_5,3, ESTATE_v_5,4, ESTATE_v_5,5, ESTATE_v_5,6, ESTATE_v_5,7, ESTATE_v_5,8, ESTATE_v_5,9, ESTATE_v_5,10]]]\n", "[[[BENNET_h_0,0, BENNET_h_0,1, BENNET_h_0,2, BENNET_h_0,3, BENNET_h_0,4, BENNET_h_0,5], [BENNET_h_1,0, BENNET_h_1,1, BENNET_h_1,2, BENNET_h_1,3, BENNET_h_1,4, BENNET_h_1,5], [BENNET_h_2,0, BENNET_h_2,1, BENNET_h_2,2, BENNET_h_2,3, BENNET_h_2,4, BENNET_h_2,5], [BENNET_h_3,0, BENNET_h_3,1, BENNET_h_3,2, BENNET_h_3,3, BENNET_h_3,4, BENNET_h_3,5], [BENNET_h_4,0, BENNET_h_4,1, BENNET_h_4,2, BENNET_h_4,3, BENNET_h_4,4, BENNET_h_4,5], [BENNET_h_5,0, BENNET_h_5,1, BENNET_h_5,2, BENNET_h_5,3, BENNET_h_5,4, BENNET_h_5,5], [BENNET_h_6,0, BENNET_h_6,1, BENNET_h_6,2, BENNET_h_6,3, BENNET_h_6,4, BENNET_h_6,5], [BENNET_h_7,0, BENNET_h_7,1, BENNET_h_7,2, BENNET_h_7,3, BENNET_h_7,4, BENNET_h_7,5], [BENNET_h_8,0, BENNET_h_8,1, BENNET_h_8,2, BENNET_h_8,3, BENNET_h_8,4, BENNET_h_8,5], [BENNET_h_9,0, BENNET_h_9,1, BENNET_h_9,2, BENNET_h_9,3, BENNET_h_9,4, BENNET_h_9,5], [BENNET_h_10,0, BENNET_h_10,1, BENNET_h_10,2, BENNET_h_10,3, BENNET_h_10,4, BENNET_h_10,5]], [[BENNET_v_0,0, BENNET_v_0,1, BENNET_v_0,2, BENNET_v_0,3, BENNET_v_0,4, BENNET_v_0,5, BENNET_v_0,6, BENNET_v_0,7, BENNET_v_0,8, BENNET_v_0,9, BENNET_v_0,10], [BENNET_v_1,0, BENNET_v_1,1, BENNET_v_1,2, BENNET_v_1,3, BENNET_v_1,4, BENNET_v_1,5, BENNET_v_1,6, BENNET_v_1,7, BENNET_v_1,8, BENNET_v_1,9, BENNET_v_1,10], [BENNET_v_2,0, BENNET_v_2,1, BENNET_v_2,2, BENNET_v_2,3, BENNET_v_2,4, BENNET_v_2,5, BENNET_v_2,6, BENNET_v_2,7, BENNET_v_2,8, BENNET_v_2,9, BENNET_v_2,10], [BENNET_v_3,0, BENNET_v_3,1, BENNET_v_3,2, BENNET_v_3,3, BENNET_v_3,4, BENNET_v_3,5, BENNET_v_3,6, BENNET_v_3,7, BENNET_v_3,8, BENNET_v_3,9, BENNET_v_3,10], [BENNET_v_4,0, BENNET_v_4,1, BENNET_v_4,2, BENNET_v_4,3, BENNET_v_4,4, BENNET_v_4,5, BENNET_v_4,6, BENNET_v_4,7, BENNET_v_4,8, BENNET_v_4,9, BENNET_v_4,10], [BENNET_v_5,0, BENNET_v_5,1, BENNET_v_5,2, BENNET_v_5,3, BENNET_v_5,4, BENNET_v_5,5, BENNET_v_5,6, BENNET_v_5,7, BENNET_v_5,8, BENNET_v_5,9, BENNET_v_5,10]]]\n", "[[[BATH_h_0,0, BATH_h_0,1, BATH_h_0,2, BATH_h_0,3, BATH_h_0,4, BATH_h_0,5, BATH_h_0,6, BATH_h_0,7], [BATH_h_1,0, BATH_h_1,1, BATH_h_1,2, BATH_h_1,3, BATH_h_1,4, BATH_h_1,5, BATH_h_1,6, BATH_h_1,7], [BATH_h_2,0, BATH_h_2,1, BATH_h_2,2, BATH_h_2,3, BATH_h_2,4, BATH_h_2,5, BATH_h_2,6, BATH_h_2,7], [BATH_h_3,0, BATH_h_3,1, BATH_h_3,2, BATH_h_3,3, BATH_h_3,4, BATH_h_3,5, BATH_h_3,6, BATH_h_3,7], [BATH_h_4,0, BATH_h_4,1, BATH_h_4,2, BATH_h_4,3, BATH_h_4,4, BATH_h_4,5, BATH_h_4,6, BATH_h_4,7], [BATH_h_5,0, BATH_h_5,1, BATH_h_5,2, BATH_h_5,3, BATH_h_5,4, BATH_h_5,5, BATH_h_5,6, BATH_h_5,7], [BATH_h_6,0, BATH_h_6,1, BATH_h_6,2, BATH_h_6,3, BATH_h_6,4, BATH_h_6,5, BATH_h_6,6, BATH_h_6,7], [BATH_h_7,0, BATH_h_7,1, BATH_h_7,2, BATH_h_7,3, BATH_h_7,4, BATH_h_7,5, BATH_h_7,6, BATH_h_7,7], [BATH_h_8,0, BATH_h_8,1, BATH_h_8,2, BATH_h_8,3, BATH_h_8,4, BATH_h_8,5, BATH_h_8,6, BATH_h_8,7], [BATH_h_9,0, BATH_h_9,1, BATH_h_9,2, BATH_h_9,3, BATH_h_9,4, BATH_h_9,5, BATH_h_9,6, BATH_h_9,7], [BATH_h_10,0, BATH_h_10,1, BATH_h_10,2, BATH_h_10,3, BATH_h_10,4, BATH_h_10,5, BATH_h_10,6, BATH_h_10,7]], [[BATH_v_0,0, BATH_v_0,1, BATH_v_0,2, BATH_v_0,3, BATH_v_0,4, BATH_v_0,5, BATH_v_0,6, BATH_v_0,7, BATH_v_0,8, BATH_v_0,9, BATH_v_0,10], [BATH_v_1,0, BATH_v_1,1, BATH_v_1,2, BATH_v_1,3, BATH_v_1,4, BATH_v_1,5, BATH_v_1,6, BATH_v_1,7, BATH_v_1,8, BATH_v_1,9, BATH_v_1,10], [BATH_v_2,0, BATH_v_2,1, BATH_v_2,2, BATH_v_2,3, BATH_v_2,4, BATH_v_2,5, BATH_v_2,6, BATH_v_2,7, BATH_v_2,8, BATH_v_2,9, BATH_v_2,10], [BATH_v_3,0, BATH_v_3,1, BATH_v_3,2, BATH_v_3,3, BATH_v_3,4, BATH_v_3,5, BATH_v_3,6, BATH_v_3,7, BATH_v_3,8, BATH_v_3,9, BATH_v_3,10], [BATH_v_4,0, BATH_v_4,1, BATH_v_4,2, BATH_v_4,3, BATH_v_4,4, BATH_v_4,5, BATH_v_4,6, BATH_v_4,7, BATH_v_4,8, BATH_v_4,9, BATH_v_4,10], [BATH_v_5,0, BATH_v_5,1, BATH_v_5,2, BATH_v_5,3, BATH_v_5,4, BATH_v_5,5, BATH_v_5,6, BATH_v_5,7, BATH_v_5,8, BATH_v_5,9, BATH_v_5,10], [BATH_v_6,0, BATH_v_6,1, BATH_v_6,2, BATH_v_6,3, BATH_v_6,4, BATH_v_6,5, BATH_v_6,6, BATH_v_6,7, BATH_v_6,8, BATH_v_6,9, BATH_v_6,10], [BATH_v_7,0, BATH_v_7,1, BATH_v_7,2, BATH_v_7,3, BATH_v_7,4, BATH_v_7,5, BATH_v_7,6, BATH_v_7,7, BATH_v_7,8, BATH_v_7,9, BATH_v_7,10]]]\n", "Executed in 1.9492 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█│█║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "code", "source": ["# Now let's see what the smallest grid we can fit this in is.  The longest word is 6 letters, so can't be shorter than this\n", "solve_crossword(words, 10, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)\n", "solve_crossword(words, 9, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)\n", "solve_crossword(words, 8, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)\n", "solve_crossword(words, 7, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)\n", "solve_crossword(words, 6, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OFLdFlaP6g7L", "outputId": "15710d99-0086-4016-807d-cf63912265e3"}, "execution_count": 34, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[[JAN<PERSON>_h_0,0, <PERSON><PERSON><PERSON><PERSON>h_0,1, <PERSON><PERSON><PERSON><PERSON>h_0,2, <PERSON><PERSON><PERSON><PERSON>h_0,3, <PERSON><PERSON><PERSON><PERSON>h_0,4, <PERSON><PERSON><PERSON><PERSON>h_0,5, JAN<PERSON>_h_0,6], [JANE_h_1,0, J<PERSON><PERSON><PERSON>h_1,1, <PERSON><PERSON><PERSON><PERSON>h_1,2, <PERSON><PERSON><PERSON><PERSON>h_1,3, JANE_h_1,4, <PERSON><PERSON><PERSON><PERSON>h_1,5, JANE_h_1,6], [JANE_h_2,0, JANE_h_2,1, JANE_h_2,2, JANE_h_2,3, JANE_h_2,4, JANE_h_2,5, JANE_h_2,6], [JANE_h_3,0, JANE_h_3,1, JANE_h_3,2, JANE_h_3,3, JANE_h_3,4, <PERSON><PERSON><PERSON>_h_3,5, <PERSON>AN<PERSON>_h_3,6], [JANE_h_4,0, <PERSON><PERSON><PERSON><PERSON>h_4,1, <PERSON><PERSON><PERSON><PERSON>h_4,2, <PERSON><PERSON><PERSON><PERSON><PERSON>_4,3, <PERSON><PERSON><PERSON><PERSON><PERSON>_4,4, JAN<PERSON><PERSON>h_4,5, <PERSON><PERSON><PERSON><PERSON>h_4,6], [JANE_h_5,0, <PERSON><PERSON><PERSON><PERSON>h_5,1, JANE_h_5,2, JANE_h_5,3, JANE_h_5,4, JAN<PERSON>_h_5,5, JANE_h_5,6], [JANE_h_6,0, JANE_h_6,1, JANE_h_6,2, JANE_h_6,3, JANE_h_6,4, JANE_h_6,5, JANE_h_6,6], [JANE_h_7,0, JANE_h_7,1, JANE_h_7,2, JANE_h_7,3, JANE_h_7,4, JANE_h_7,5, JANE_h_7,6], [JANE_h_8,0, JANE_h_8,1, JANE_h_8,2, JANE_h_8,3, JANE_h_8,4, JANE_h_8,5, JANE_h_8,6], [JANE_h_9,0, JANE_h_9,1, JANE_h_9,2, JANE_h_9,3, JANE_h_9,4, JANE_h_9,5, JANE_h_9,6]], [[JANE_v_0,0, JANE_v_0,1, JANE_v_0,2, JANE_v_0,3, JANE_v_0,4, JANE_v_0,5, JANE_v_0,6, JANE_v_0,7, JANE_v_0,8, JANE_v_0,9], [JANE_v_1,0, JANE_v_1,1, JANE_v_1,2, JANE_v_1,3, JANE_v_1,4, JANE_v_1,5, JANE_v_1,6, JANE_v_1,7, JANE_v_1,8, JANE_v_1,9], [JANE_v_2,0, JANE_v_2,1, JANE_v_2,2, JANE_v_2,3, JANE_v_2,4, JANE_v_2,5, JANE_v_2,6, JANE_v_2,7, JANE_v_2,8, JANE_v_2,9], [JANE_v_3,0, JANE_v_3,1, JANE_v_3,2, JANE_v_3,3, JANE_v_3,4, JANE_v_3,5, JANE_v_3,6, JANE_v_3,7, JANE_v_3,8, JANE_v_3,9], [JANE_v_4,0, JANE_v_4,1, JANE_v_4,2, JANE_v_4,3, JANE_v_4,4, JANE_v_4,5, JANE_v_4,6, JANE_v_4,7, JANE_v_4,8, JANE_v_4,9], [JANE_v_5,0, JANE_v_5,1, JANE_v_5,2, JANE_v_5,3, JANE_v_5,4, JANE_v_5,5, JANE_v_5,6, JANE_v_5,7, JANE_v_5,8, JANE_v_5,9], [JANE_v_6,0, JANE_v_6,1, JANE_v_6,2, JANE_v_6,3, JANE_v_6,4, JANE_v_6,5, JANE_v_6,6, JANE_v_6,7, JANE_v_6,8, JANE_v_6,9]]]\n", "[[[AUSTEN_h_0,0, AUSTEN_h_0,1, AUSTEN_h_0,2, AUSTEN_h_0,3, AUSTEN_h_0,4], [AUSTEN_h_1,0, AUSTEN_h_1,1, AUSTEN_h_1,2, AUSTEN_h_1,3, AUSTEN_h_1,4], [AUSTEN_h_2,0, AUSTEN_h_2,1, AUSTEN_h_2,2, AUSTEN_h_2,3, AUSTEN_h_2,4], [AUSTEN_h_3,0, AUSTEN_h_3,1, AUSTEN_h_3,2, AUSTEN_h_3,3, AUSTEN_h_3,4], [AUSTEN_h_4,0, AUSTEN_h_4,1, AUSTEN_h_4,2, AUSTEN_h_4,3, AUSTEN_h_4,4], [AUSTEN_h_5,0, AUSTEN_h_5,1, AUSTEN_h_5,2, AUSTEN_h_5,3, AUSTEN_h_5,4], [AUSTEN_h_6,0, AUSTEN_h_6,1, AUSTEN_h_6,2, AUSTEN_h_6,3, AUSTEN_h_6,4], [AUSTEN_h_7,0, AUSTEN_h_7,1, AUSTEN_h_7,2, AUSTEN_h_7,3, AUSTEN_h_7,4], [AUSTEN_h_8,0, AUSTEN_h_8,1, AUSTEN_h_8,2, AUSTEN_h_8,3, AUSTEN_h_8,4], [AUSTEN_h_9,0, AUSTEN_h_9,1, AUSTEN_h_9,2, AUSTEN_h_9,3, AUSTEN_h_9,4]], [[AUSTEN_v_0,0, AUSTEN_v_0,1, AUSTEN_v_0,2, AUSTEN_v_0,3, AUSTEN_v_0,4, AUSTEN_v_0,5, AUSTEN_v_0,6, AUSTEN_v_0,7, AUSTEN_v_0,8, AUSTEN_v_0,9], [AUSTEN_v_1,0, AUSTEN_v_1,1, AUSTEN_v_1,2, AUSTEN_v_1,3, AUSTEN_v_1,4, AUSTEN_v_1,5, AUSTEN_v_1,6, AUSTEN_v_1,7, AUSTEN_v_1,8, AUSTEN_v_1,9], [AUSTEN_v_2,0, AUSTEN_v_2,1, AUSTEN_v_2,2, AUSTEN_v_2,3, AUSTEN_v_2,4, AUSTEN_v_2,5, AUSTEN_v_2,6, AUSTEN_v_2,7, AUSTEN_v_2,8, AUSTEN_v_2,9], [AUSTEN_v_3,0, AUSTEN_v_3,1, AUSTEN_v_3,2, AUSTEN_v_3,3, AUSTEN_v_3,4, AUSTEN_v_3,5, AUSTEN_v_3,6, AUSTEN_v_3,7, AUSTEN_v_3,8, AUSTEN_v_3,9], [AUSTEN_v_4,0, AUSTEN_v_4,1, AUSTEN_v_4,2, AUSTEN_v_4,3, AUSTEN_v_4,4, AUSTEN_v_4,5, AUSTEN_v_4,6, AUSTEN_v_4,7, AUSTEN_v_4,8, AUSTEN_v_4,9]]]\n", "[[[PRIDE_h_0,0, <PERSON><PERSON><PERSON>_h_0,1, PR<PERSON>E_h_0,2, PRIDE_h_0,3, PRIDE_h_0,4, PRIDE_h_0,5], [PRIDE_h_1,0, PRIDE_h_1,1, PRIDE_h_1,2, PRIDE_h_1,3, PRIDE_h_1,4, PRIDE_h_1,5], [PRIDE_h_2,0, PRIDE_h_2,1, PRIDE_h_2,2, PRIDE_h_2,3, PRIDE_h_2,4, PRIDE_h_2,5], [PRIDE_h_3,0, PRIDE_h_3,1, PRIDE_h_3,2, PRIDE_h_3,3, PRIDE_h_3,4, PRIDE_h_3,5], [PRIDE_h_4,0, PRIDE_h_4,1, PRIDE_h_4,2, PRIDE_h_4,3, PRIDE_h_4,4, PRIDE_h_4,5], [PRIDE_h_5,0, <PERSON><PERSON><PERSON>_h_5,1, PR<PERSON>E_h_5,2, PRIDE_h_5,3, PRIDE_h_5,4, PRIDE_h_5,5], [PRIDE_h_6,0, PRIDE_h_6,1, PRIDE_h_6,2, PRIDE_h_6,3, PRIDE_h_6,4, PRIDE_h_6,5], [PRIDE_h_7,0, PRIDE_h_7,1, PRIDE_h_7,2, PRIDE_h_7,3, PRIDE_h_7,4, PRIDE_h_7,5], [PRIDE_h_8,0, PRIDE_h_8,1, PRIDE_h_8,2, PRIDE_h_8,3, PRIDE_h_8,4, PRIDE_h_8,5], [PRIDE_h_9,0, PRIDE_h_9,1, PRIDE_h_9,2, PRIDE_h_9,3, PRIDE_h_9,4, PRIDE_h_9,5]], [[PRIDE_v_0,0, PRIDE_v_0,1, PRIDE_v_0,2, PRIDE_v_0,3, PRIDE_v_0,4, PRIDE_v_0,5, PRIDE_v_0,6, PRIDE_v_0,7, PRIDE_v_0,8, PRIDE_v_0,9], [PRIDE_v_1,0, PRIDE_v_1,1, PRIDE_v_1,2, PRIDE_v_1,3, PRIDE_v_1,4, PRIDE_v_1,5, PRIDE_v_1,6, PRIDE_v_1,7, PRIDE_v_1,8, PRIDE_v_1,9], [PRIDE_v_2,0, PRIDE_v_2,1, PRIDE_v_2,2, PRIDE_v_2,3, PRIDE_v_2,4, PRIDE_v_2,5, PRIDE_v_2,6, PRIDE_v_2,7, PRIDE_v_2,8, PRIDE_v_2,9], [PRIDE_v_3,0, PRIDE_v_3,1, PRIDE_v_3,2, PRIDE_v_3,3, PRIDE_v_3,4, PRIDE_v_3,5, PRIDE_v_3,6, PRIDE_v_3,7, PRIDE_v_3,8, PRIDE_v_3,9], [PRIDE_v_4,0, PRIDE_v_4,1, PRIDE_v_4,2, PRIDE_v_4,3, PRIDE_v_4,4, PRIDE_v_4,5, PRIDE_v_4,6, PRIDE_v_4,7, PRIDE_v_4,8, PRIDE_v_4,9], [PRIDE_v_5,0, PRIDE_v_5,1, PRIDE_v_5,2, PRIDE_v_5,3, PRIDE_v_5,4, PRIDE_v_5,5, PRIDE_v_5,6, PRIDE_v_5,7, PRIDE_v_5,8, PRIDE_v_5,9]]]\n", "[[[NOVEL_h_0,0, NOVEL_h_0,1, NOVEL_h_0,2, NOVEL_h_0,3, NOVEL_h_0,4, NOVEL_h_0,5], [NOVEL_h_1,0, NOVEL_h_1,1, NOVEL_h_1,2, NOVEL_h_1,3, NOVEL_h_1,4, NOVEL_h_1,5], [NOVEL_h_2,0, NOVEL_h_2,1, NOVEL_h_2,2, NOVEL_h_2,3, NOVEL_h_2,4, NOVEL_h_2,5], [NOVEL_h_3,0, NOVEL_h_3,1, NOVEL_h_3,2, NOVEL_h_3,3, NOVEL_h_3,4, NOVEL_h_3,5], [NOVEL_h_4,0, NOVEL_h_4,1, NOVEL_h_4,2, NOVEL_h_4,3, NOVEL_h_4,4, NOVEL_h_4,5], [NOVEL_h_5,0, NO<PERSON>L_h_5,1, NOVEL_h_5,2, NOVEL_h_5,3, NOVEL_h_5,4, NOVEL_h_5,5], [NOVEL_h_6,0, NOVEL_h_6,1, NOVEL_h_6,2, NOVEL_h_6,3, NOVEL_h_6,4, NOVEL_h_6,5], [NOVEL_h_7,0, NOVEL_h_7,1, NOVEL_h_7,2, NOVEL_h_7,3, NOVEL_h_7,4, NOVEL_h_7,5], [NOVEL_h_8,0, NOVEL_h_8,1, NOVEL_h_8,2, NOVEL_h_8,3, NOVEL_h_8,4, NOVEL_h_8,5], [NOVEL_h_9,0, NOVEL_h_9,1, NOVEL_h_9,2, NOVEL_h_9,3, NOVEL_h_9,4, NOVEL_h_9,5]], [[NOVEL_v_0,0, NOVEL_v_0,1, NOVEL_v_0,2, NOVEL_v_0,3, NOVEL_v_0,4, NOVEL_v_0,5, NOVEL_v_0,6, NOVEL_v_0,7, NOVEL_v_0,8, NOVEL_v_0,9], [NOVEL_v_1,0, NOVEL_v_1,1, NOVEL_v_1,2, NOVEL_v_1,3, NOVEL_v_1,4, NOVEL_v_1,5, NOVEL_v_1,6, NOVEL_v_1,7, NOVEL_v_1,8, NOVEL_v_1,9], [NOVEL_v_2,0, NOVEL_v_2,1, NOVEL_v_2,2, NOVEL_v_2,3, NOVEL_v_2,4, NOVEL_v_2,5, NOVEL_v_2,6, NOVEL_v_2,7, NOVEL_v_2,8, NOVEL_v_2,9], [NOVEL_v_3,0, NOVEL_v_3,1, NOVEL_v_3,2, NOVEL_v_3,3, NOVEL_v_3,4, NOVEL_v_3,5, NOVEL_v_3,6, NOVEL_v_3,7, NOVEL_v_3,8, NOVEL_v_3,9], [NOVEL_v_4,0, NOVEL_v_4,1, NOVEL_v_4,2, NOVEL_v_4,3, NOVEL_v_4,4, NOVEL_v_4,5, NOVEL_v_4,6, NOVEL_v_4,7, NOVEL_v_4,8, NOVEL_v_4,9], [NOVEL_v_5,0, NOVEL_v_5,1, NOVEL_v_5,2, NOVEL_v_5,3, NOVEL_v_5,4, NOVEL_v_5,5, NOVEL_v_5,6, NOVEL_v_5,7, NOVEL_v_5,8, NOVEL_v_5,9]]]\n", "[[[DARCY_h_0,0, <PERSON>ARC<PERSON>_h_0,1, <PERSON>ARC<PERSON>_h_0,2, DARCY_h_0,3, DARCY_h_0,4, DARCY_h_0,5], [DARCY_h_1,0, DARCY_h_1,1, DARCY_h_1,2, DARCY_h_1,3, DARCY_h_1,4, DARCY_h_1,5], [DARCY_h_2,0, DARCY_h_2,1, DARCY_h_2,2, DARCY_h_2,3, DARCY_h_2,4, DARCY_h_2,5], [DARCY_h_3,0, DARCY_h_3,1, DARCY_h_3,2, DARCY_h_3,3, DARCY_h_3,4, DARCY_h_3,5], [DARCY_h_4,0, DARCY_h_4,1, DARCY_h_4,2, DARCY_h_4,3, <PERSON>AR<PERSON><PERSON>_h_4,4, <PERSON>ARC<PERSON>_h_4,5], [DARCY_h_5,0, DARCY_h_5,1, DARCY_h_5,2, DARCY_h_5,3, DARCY_h_5,4, DARCY_h_5,5], [DARCY_h_6,0, DARCY_h_6,1, DARCY_h_6,2, DARCY_h_6,3, DARCY_h_6,4, DARCY_h_6,5], [DARCY_h_7,0, DARCY_h_7,1, DARCY_h_7,2, DARCY_h_7,3, DARCY_h_7,4, DARCY_h_7,5], [DARCY_h_8,0, DARCY_h_8,1, DARCY_h_8,2, DARCY_h_8,3, DARCY_h_8,4, DARCY_h_8,5], [DARCY_h_9,0, DARCY_h_9,1, DARCY_h_9,2, DARCY_h_9,3, DARCY_h_9,4, DARCY_h_9,5]], [[DARCY_v_0,0, DARCY_v_0,1, DARCY_v_0,2, DARCY_v_0,3, DARCY_v_0,4, DARCY_v_0,5, DARCY_v_0,6, DARCY_v_0,7, DARCY_v_0,8, DARCY_v_0,9], [DARCY_v_1,0, DARCY_v_1,1, DARCY_v_1,2, DARCY_v_1,3, DARCY_v_1,4, DARCY_v_1,5, DARCY_v_1,6, DARCY_v_1,7, DARCY_v_1,8, DARCY_v_1,9], [DARCY_v_2,0, DARCY_v_2,1, DARCY_v_2,2, DARCY_v_2,3, DARCY_v_2,4, DARCY_v_2,5, DARCY_v_2,6, DARCY_v_2,7, DARCY_v_2,8, DARCY_v_2,9], [DARCY_v_3,0, DARCY_v_3,1, DARCY_v_3,2, DARCY_v_3,3, DARCY_v_3,4, DARCY_v_3,5, DARCY_v_3,6, DARCY_v_3,7, DARCY_v_3,8, DARCY_v_3,9], [DARCY_v_4,0, DARCY_v_4,1, DARCY_v_4,2, DARCY_v_4,3, DARCY_v_4,4, DARCY_v_4,5, DARCY_v_4,6, DARCY_v_4,7, DARCY_v_4,8, DARCY_v_4,9], [DARCY_v_5,0, DARCY_v_5,1, DARCY_v_5,2, DARCY_v_5,3, DARCY_v_5,4, DARCY_v_5,5, DARCY_v_5,6, DARCY_v_5,7, DARCY_v_5,8, DARCY_v_5,9]]]\n", "[[[SENSE_h_0,0, SENSE_h_0,1, SENSE_h_0,2, SENSE_h_0,3, SENSE_h_0,4, SENSE_h_0,5], [SENSE_h_1,0, SENSE_h_1,1, SENSE_h_1,2, SENSE_h_1,3, SENSE_h_1,4, SENSE_h_1,5], [SENSE_h_2,0, SENSE_h_2,1, SENSE_h_2,2, SENSE_h_2,3, SENSE_h_2,4, SENSE_h_2,5], [SENSE_h_3,0, SENSE_h_3,1, SENSE_h_3,2, SENSE_h_3,3, SENSE_h_3,4, SENSE_h_3,5], [SENSE_h_4,0, SENSE_h_4,1, SENSE_h_4,2, SENSE_h_4,3, SENSE_h_4,4, SENSE_h_4,5], [SENSE_h_5,0, SENSE_h_5,1, SENSE_h_5,2, SENSE_h_5,3, SENSE_h_5,4, SENSE_h_5,5], [SENSE_h_6,0, SENSE_h_6,1, SENSE_h_6,2, SENSE_h_6,3, SENSE_h_6,4, SENSE_h_6,5], [SENSE_h_7,0, SENSE_h_7,1, SENSE_h_7,2, SENSE_h_7,3, SENSE_h_7,4, SENSE_h_7,5], [SENSE_h_8,0, SENSE_h_8,1, SENSE_h_8,2, SENSE_h_8,3, SENSE_h_8,4, SENSE_h_8,5], [SENSE_h_9,0, SENSE_h_9,1, SENSE_h_9,2, SENSE_h_9,3, SENSE_h_9,4, SENSE_h_9,5]], [[SENSE_v_0,0, SENSE_v_0,1, SENSE_v_0,2, SENSE_v_0,3, SENSE_v_0,4, SENSE_v_0,5, SENSE_v_0,6, SENSE_v_0,7, SENSE_v_0,8, SENSE_v_0,9], [SENSE_v_1,0, SENSE_v_1,1, SENSE_v_1,2, SENSE_v_1,3, SENSE_v_1,4, SENSE_v_1,5, SENSE_v_1,6, SENSE_v_1,7, SENSE_v_1,8, SENSE_v_1,9], [SENSE_v_2,0, SENSE_v_2,1, SENSE_v_2,2, SENSE_v_2,3, SENSE_v_2,4, SENSE_v_2,5, SENSE_v_2,6, SENSE_v_2,7, SENSE_v_2,8, SENSE_v_2,9], [SENSE_v_3,0, SENSE_v_3,1, SENSE_v_3,2, SENSE_v_3,3, SENSE_v_3,4, SENSE_v_3,5, SENSE_v_3,6, SENSE_v_3,7, SENSE_v_3,8, SENSE_v_3,9], [SENSE_v_4,0, SENSE_v_4,1, SENSE_v_4,2, SENSE_v_4,3, SENSE_v_4,4, SENSE_v_4,5, SENSE_v_4,6, SENSE_v_4,7, SENSE_v_4,8, SENSE_v_4,9], [SENSE_v_5,0, SENSE_v_5,1, SENSE_v_5,2, SENSE_v_5,3, SENSE_v_5,4, SENSE_v_5,5, SENSE_v_5,6, SENSE_v_5,7, SENSE_v_5,8, SENSE_v_5,9]]]\n", "[[[EMMA_h_0,0, <PERSON><PERSON><PERSON>_h_0,1, E<PERSON><PERSON>_h_0,2, EMMA_h_0,3, EMMA_h_0,4, EMMA_h_0,5, EMMA_h_0,6], [EMMA_h_1,0, EMMA_h_1,1, EMMA_h_1,2, EMMA_h_1,3, EMMA_h_1,4, EMMA_h_1,5, EMMA_h_1,6], [EMMA_h_2,0, EMMA_h_2,1, EMMA_h_2,2, EMMA_h_2,3, EMMA_h_2,4, EMMA_h_2,5, EMMA_h_2,6], [EMMA_h_3,0, EMMA_h_3,1, EMMA_h_3,2, EMMA_h_3,3, EMMA_h_3,4, EMMA_h_3,5, EMMA_h_3,6], [EMMA_h_4,0, EMMA_h_4,1, EMM<PERSON>_h_4,2, <PERSON><PERSON><PERSON><PERSON>h_4,3, <PERSON><PERSON><PERSON><PERSON>h_4,4, EMMA_h_4,5, EMM<PERSON>_h_4,6], [EMMA_h_5,0, EMMA_h_5,1, EMMA_h_5,2, EMMA_h_5,3, EMMA_h_5,4, <PERSON>MMA_h_5,5, EMMA_h_5,6], [<PERSON>MMA_h_6,0, EMMA_h_6,1, EMMA_h_6,2, <PERSON>MMA_h_6,3, <PERSON>MMA_h_6,4, EMMA_h_6,5, EMMA_h_6,6], [EMMA_h_7,0, EMMA_h_7,1, EMMA_h_7,2, EMMA_h_7,3, EMMA_h_7,4, EMMA_h_7,5, EMMA_h_7,6], [EMMA_h_8,0, EMMA_h_8,1, EMMA_h_8,2, EMMA_h_8,3, EMMA_h_8,4, EMMA_h_8,5, EMMA_h_8,6], [EMMA_h_9,0, EMMA_h_9,1, EMMA_h_9,2, EMMA_h_9,3, EMMA_h_9,4, EMMA_h_9,5, EMMA_h_9,6]], [[EMMA_v_0,0, EMMA_v_0,1, EMMA_v_0,2, EMMA_v_0,3, EMMA_v_0,4, EMMA_v_0,5, EMMA_v_0,6, EMMA_v_0,7, EMMA_v_0,8, EMMA_v_0,9], [EMMA_v_1,0, EMMA_v_1,1, EMMA_v_1,2, EMMA_v_1,3, EMMA_v_1,4, EMMA_v_1,5, EMMA_v_1,6, EMMA_v_1,7, EMMA_v_1,8, EMMA_v_1,9], [EMMA_v_2,0, EMMA_v_2,1, EMMA_v_2,2, EMMA_v_2,3, EMMA_v_2,4, EMMA_v_2,5, EMMA_v_2,6, EMMA_v_2,7, EMMA_v_2,8, EMMA_v_2,9], [EMMA_v_3,0, EMMA_v_3,1, EMMA_v_3,2, EMMA_v_3,3, EMMA_v_3,4, EMMA_v_3,5, EMMA_v_3,6, EMMA_v_3,7, EMMA_v_3,8, EMMA_v_3,9], [EMMA_v_4,0, EMMA_v_4,1, EMMA_v_4,2, EMMA_v_4,3, EMMA_v_4,4, EMMA_v_4,5, EMMA_v_4,6, EMMA_v_4,7, EMMA_v_4,8, EMMA_v_4,9], [EMMA_v_5,0, EMMA_v_5,1, EMMA_v_5,2, EMMA_v_5,3, EMMA_v_5,4, EMMA_v_5,5, EMMA_v_5,6, EMMA_v_5,7, EMMA_v_5,8, EMMA_v_5,9], [EMMA_v_6,0, EMMA_v_6,1, EMMA_v_6,2, EMMA_v_6,3, EMMA_v_6,4, EMMA_v_6,5, EMMA_v_6,6, EMMA_v_6,7, EMMA_v_6,8, EMMA_v_6,9]]]\n", "[[[ESTATE_h_0,0, ESTATE_h_0,1, <PERSON>STATE_h_0,2, ESTATE_h_0,3, ESTATE_h_0,4], [ESTATE_h_1,0, ESTATE_h_1,1, ESTATE_h_1,2, ESTATE_h_1,3, ESTATE_h_1,4], [ESTATE_h_2,0, ESTATE_h_2,1, ESTATE_h_2,2, ESTATE_h_2,3, ESTATE_h_2,4], [ESTATE_h_3,0, ESTATE_h_3,1, ESTATE_h_3,2, ESTATE_h_3,3, ESTATE_h_3,4], [ESTATE_h_4,0, ESTATE_h_4,1, ESTATE_h_4,2, ESTATE_h_4,3, ESTATE_h_4,4], [ESTATE_h_5,0, ESTATE_h_5,1, <PERSON>STATE_h_5,2, <PERSON><PERSON><PERSON><PERSON>_h_5,3, ESTATE_h_5,4], [ESTATE_h_6,0, ESTATE_h_6,1, ESTATE_h_6,2, ESTATE_h_6,3, ESTATE_h_6,4], [ESTATE_h_7,0, ESTATE_h_7,1, ESTATE_h_7,2, ESTATE_h_7,3, ESTATE_h_7,4], [ESTATE_h_8,0, ESTATE_h_8,1, ESTATE_h_8,2, ESTATE_h_8,3, ESTATE_h_8,4], [ESTATE_h_9,0, ESTATE_h_9,1, ESTATE_h_9,2, ESTATE_h_9,3, ESTATE_h_9,4]], [[ESTATE_v_0,0, ESTATE_v_0,1, ESTATE_v_0,2, ESTATE_v_0,3, ESTATE_v_0,4, ESTATE_v_0,5, ESTATE_v_0,6, ESTATE_v_0,7, ESTATE_v_0,8, ESTATE_v_0,9], [ESTATE_v_1,0, ESTATE_v_1,1, ESTATE_v_1,2, ESTATE_v_1,3, ESTATE_v_1,4, ESTATE_v_1,5, ESTATE_v_1,6, ESTATE_v_1,7, ESTATE_v_1,8, ESTATE_v_1,9], [ESTATE_v_2,0, ESTATE_v_2,1, ESTATE_v_2,2, ESTATE_v_2,3, ESTATE_v_2,4, ESTATE_v_2,5, ESTATE_v_2,6, ESTATE_v_2,7, ESTATE_v_2,8, ESTATE_v_2,9], [ESTATE_v_3,0, ESTATE_v_3,1, ESTATE_v_3,2, ESTATE_v_3,3, ESTATE_v_3,4, ESTATE_v_3,5, ESTATE_v_3,6, ESTATE_v_3,7, ESTATE_v_3,8, ESTATE_v_3,9], [ESTATE_v_4,0, ESTATE_v_4,1, ESTATE_v_4,2, ESTATE_v_4,3, ESTATE_v_4,4, ESTATE_v_4,5, ESTATE_v_4,6, ESTATE_v_4,7, ESTATE_v_4,8, ESTATE_v_4,9]]]\n", "[[[BENNET_h_0,0, BENNET_h_0,1, BENNET_h_0,2, BENNET_h_0,3, BENNET_h_0,4], [BENNET_h_1,0, BENNET_h_1,1, BENNET_h_1,2, BENNET_h_1,3, BENNET_h_1,4], [BENNET_h_2,0, BENNET_h_2,1, BENNET_h_2,2, BENNET_h_2,3, BENNET_h_2,4], [BENNET_h_3,0, BENNET_h_3,1, BENNET_h_3,2, BENNET_h_3,3, BENNET_h_3,4], [BENNET_h_4,0, BENNET_h_4,1, BENNET_h_4,2, BENNET_h_4,3, BENNET_h_4,4], [BENNET_h_5,0, BENNET_h_5,1, BENNET_h_5,2, BENNET_h_5,3, BENNET_h_5,4], [BENNET_h_6,0, BENNET_h_6,1, BENNET_h_6,2, BENNET_h_6,3, BENNET_h_6,4], [BENNET_h_7,0, BENNET_h_7,1, BENNET_h_7,2, BENNET_h_7,3, BENNET_h_7,4], [BENNET_h_8,0, BENNET_h_8,1, BENNET_h_8,2, BENNET_h_8,3, BENNET_h_8,4], [BENNET_h_9,0, BENNET_h_9,1, BENNET_h_9,2, BENNET_h_9,3, BENNET_h_9,4]], [[BENNET_v_0,0, BENNET_v_0,1, BENNET_v_0,2, BENNET_v_0,3, BENNET_v_0,4, BENNET_v_0,5, BENNET_v_0,6, BENNET_v_0,7, BENNET_v_0,8, BENNET_v_0,9], [BENNET_v_1,0, BENNET_v_1,1, BENNET_v_1,2, BENNET_v_1,3, BENNET_v_1,4, BENNET_v_1,5, BENNET_v_1,6, BENNET_v_1,7, BENNET_v_1,8, BENNET_v_1,9], [BENNET_v_2,0, BENNET_v_2,1, BENNET_v_2,2, BENNET_v_2,3, BENNET_v_2,4, BENNET_v_2,5, BENNET_v_2,6, BENNET_v_2,7, BENNET_v_2,8, BENNET_v_2,9], [BENNET_v_3,0, BENNET_v_3,1, BENNET_v_3,2, BENNET_v_3,3, BENNET_v_3,4, BENNET_v_3,5, BENNET_v_3,6, BENNET_v_3,7, BENNET_v_3,8, BENNET_v_3,9], [BENNET_v_4,0, BENNET_v_4,1, BENNET_v_4,2, BENNET_v_4,3, BENNET_v_4,4, BENNET_v_4,5, BENNET_v_4,6, BENNET_v_4,7, BENNET_v_4,8, BENNET_v_4,9]]]\n", "[[[BATH_h_0,0, BATH_h_0,1, BATH_h_0,2, BATH_h_0,3, BATH_h_0,4, BATH_h_0,5, BATH_h_0,6], [BATH_h_1,0, BATH_h_1,1, BATH_h_1,2, BATH_h_1,3, BATH_h_1,4, BATH_h_1,5, BATH_h_1,6], [BATH_h_2,0, BATH_h_2,1, BATH_h_2,2, BATH_h_2,3, BATH_h_2,4, BATH_h_2,5, BATH_h_2,6], [BATH_h_3,0, BATH_h_3,1, BATH_h_3,2, BATH_h_3,3, BATH_h_3,4, BATH_h_3,5, BATH_h_3,6], [BATH_h_4,0, BATH_h_4,1, BATH_h_4,2, BATH_h_4,3, BATH_h_4,4, BATH_h_4,5, BATH_h_4,6], [BATH_h_5,0, BATH_h_5,1, BATH_h_5,2, BATH_h_5,3, BATH_h_5,4, BATH_h_5,5, BATH_h_5,6], [BATH_h_6,0, BATH_h_6,1, BATH_h_6,2, BATH_h_6,3, BATH_h_6,4, BATH_h_6,5, BATH_h_6,6], [BATH_h_7,0, BATH_h_7,1, BATH_h_7,2, BATH_h_7,3, BATH_h_7,4, BATH_h_7,5, BATH_h_7,6], [BATH_h_8,0, BATH_h_8,1, BATH_h_8,2, BATH_h_8,3, BATH_h_8,4, BATH_h_8,5, BATH_h_8,6], [BATH_h_9,0, BATH_h_9,1, BATH_h_9,2, BATH_h_9,3, BATH_h_9,4, BATH_h_9,5, BATH_h_9,6]], [[BATH_v_0,0, BATH_v_0,1, BATH_v_0,2, BATH_v_0,3, BATH_v_0,4, BATH_v_0,5, BATH_v_0,6, BATH_v_0,7, BATH_v_0,8, BATH_v_0,9], [BATH_v_1,0, BATH_v_1,1, BATH_v_1,2, BATH_v_1,3, BATH_v_1,4, BATH_v_1,5, BATH_v_1,6, BATH_v_1,7, BATH_v_1,8, BATH_v_1,9], [BATH_v_2,0, BATH_v_2,1, BATH_v_2,2, BATH_v_2,3, BATH_v_2,4, BATH_v_2,5, BATH_v_2,6, BATH_v_2,7, BATH_v_2,8, BATH_v_2,9], [BATH_v_3,0, BATH_v_3,1, BATH_v_3,2, BATH_v_3,3, BATH_v_3,4, BATH_v_3,5, BATH_v_3,6, BATH_v_3,7, BATH_v_3,8, BATH_v_3,9], [BATH_v_4,0, BATH_v_4,1, BATH_v_4,2, BATH_v_4,3, BATH_v_4,4, BATH_v_4,5, BATH_v_4,6, BATH_v_4,7, BATH_v_4,8, BATH_v_4,9], [BATH_v_5,0, BATH_v_5,1, BATH_v_5,2, BATH_v_5,3, BATH_v_5,4, BATH_v_5,5, BATH_v_5,6, BATH_v_5,7, BATH_v_5,8, BATH_v_5,9], [BATH_v_6,0, BATH_v_6,1, BATH_v_6,2, BATH_v_6,3, BATH_v_6,4, BATH_v_6,5, BATH_v_6,6, BATH_v_6,7, BATH_v_6,8, BATH_v_6,9]]]\n", "Executed in 1.0347 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█│█║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╧═╝\n", "[[[JANE_h_0,0, <PERSON><PERSON><PERSON><PERSON>h_0,1, <PERSON><PERSON><PERSON><PERSON>h_0,2, <PERSON><PERSON><PERSON><PERSON>h_0,3, <PERSON><PERSON><PERSON><PERSON>h_0,4, <PERSON>AN<PERSON><PERSON>h_0,5], [JANE_h_1,0, JANE_h_1,1, <PERSON><PERSON><PERSON><PERSON>h_1,2, <PERSON><PERSON><PERSON><PERSON>h_1,3, <PERSON><PERSON><PERSON><PERSON>h_1,4, JANE_h_1,5], [JANE_h_2,0, JANE_h_2,1, JANE_h_2,2, JANE_h_2,3, JANE_h_2,4, JANE_h_2,5], [JANE_h_3,0, JANE_h_3,1, JANE_h_3,2, JANE_h_3,3, JAN<PERSON>_h_3,4, JANE_h_3,5], [JANE_h_4,0, JANE_h_4,1, <PERSON><PERSON><PERSON>_h_4,2, <PERSON>AN<PERSON>_h_4,3, JANE_h_4,4, <PERSON><PERSON><PERSON><PERSON>h_4,5], [<PERSON><PERSON><PERSON>_h_5,0, <PERSON><PERSON><PERSON><PERSON>h_5,1, <PERSON><PERSON><PERSON><PERSON>h_5,2, <PERSON><PERSON><PERSON><PERSON>h_5,3, <PERSON><PERSON><PERSON><PERSON>h_5,4, <PERSON><PERSON><PERSON>_h_5,5], [JANE_h_6,0, JANE_h_6,1, JANE_h_6,2, JANE_h_6,3, <PERSON><PERSON>E_h_6,4, JANE_h_6,5], [JANE_h_7,0, JANE_h_7,1, JANE_h_7,2, J<PERSON>E_h_7,3, JANE_h_7,4, JANE_h_7,5], [JANE_h_8,0, JANE_h_8,1, JANE_h_8,2, JANE_h_8,3, JANE_h_8,4, JANE_h_8,5]], [[JANE_v_0,0, JANE_v_0,1, JANE_v_0,2, JANE_v_0,3, JANE_v_0,4, JANE_v_0,5, JANE_v_0,6, JANE_v_0,7, JANE_v_0,8], [JANE_v_1,0, JANE_v_1,1, JANE_v_1,2, JANE_v_1,3, JANE_v_1,4, JANE_v_1,5, JANE_v_1,6, JANE_v_1,7, JANE_v_1,8], [JANE_v_2,0, JANE_v_2,1, JANE_v_2,2, JANE_v_2,3, JANE_v_2,4, JANE_v_2,5, JANE_v_2,6, JANE_v_2,7, JANE_v_2,8], [JANE_v_3,0, JANE_v_3,1, JANE_v_3,2, JANE_v_3,3, JANE_v_3,4, JANE_v_3,5, JANE_v_3,6, JANE_v_3,7, JANE_v_3,8], [JANE_v_4,0, JANE_v_4,1, JANE_v_4,2, JANE_v_4,3, JANE_v_4,4, JANE_v_4,5, JANE_v_4,6, JANE_v_4,7, JANE_v_4,8], [JANE_v_5,0, JANE_v_5,1, JANE_v_5,2, JANE_v_5,3, JANE_v_5,4, JANE_v_5,5, JANE_v_5,6, JANE_v_5,7, JANE_v_5,8]]]\n", "[[[AUSTEN_h_0,0, AUSTEN_h_0,1, AUSTEN_h_0,2, AUSTEN_h_0,3], [AUSTEN_h_1,0, AUSTEN_h_1,1, AUSTEN_h_1,2, AUSTEN_h_1,3], [AUSTEN_h_2,0, AUSTEN_h_2,1, AUSTEN_h_2,2, AUSTEN_h_2,3], [AUSTEN_h_3,0, AUSTEN_h_3,1, AUSTEN_h_3,2, AUSTEN_h_3,3], [AUSTEN_h_4,0, AUSTEN_h_4,1, AUSTEN_h_4,2, AUSTEN_h_4,3], [AUSTEN_h_5,0, AUSTEN_h_5,1, AUSTEN_h_5,2, AUSTEN_h_5,3], [AUSTEN_h_6,0, AUSTEN_h_6,1, AUSTEN_h_6,2, AUSTEN_h_6,3], [AUSTEN_h_7,0, AUSTEN_h_7,1, AUSTEN_h_7,2, AUSTEN_h_7,3], [AUSTEN_h_8,0, AUSTEN_h_8,1, AUSTEN_h_8,2, AUSTEN_h_8,3]], [[AUSTEN_v_0,0, AUSTEN_v_0,1, AUSTEN_v_0,2, AUSTEN_v_0,3, AUSTEN_v_0,4, AUSTEN_v_0,5, AUSTEN_v_0,6, AUSTEN_v_0,7, AUSTEN_v_0,8], [AUSTEN_v_1,0, AUSTEN_v_1,1, AUSTEN_v_1,2, AUSTEN_v_1,3, AUSTEN_v_1,4, AUSTEN_v_1,5, AUSTEN_v_1,6, AUSTEN_v_1,7, AUSTEN_v_1,8], [AUSTEN_v_2,0, AUSTEN_v_2,1, AUSTEN_v_2,2, AUSTEN_v_2,3, AUSTEN_v_2,4, AUSTEN_v_2,5, AUSTEN_v_2,6, AUSTEN_v_2,7, AUSTEN_v_2,8], [AUSTEN_v_3,0, AUSTEN_v_3,1, AUSTEN_v_3,2, AUSTEN_v_3,3, AUSTEN_v_3,4, AUSTEN_v_3,5, AUSTEN_v_3,6, AUSTEN_v_3,7, AUSTEN_v_3,8]]]\n", "[[[PRIDE_h_0,0, PR<PERSON>E_h_0,1, PR<PERSON>E_h_0,2, PRIDE_h_0,3, PRIDE_h_0,4], [PRIDE_h_1,0, PRIDE_h_1,1, PRIDE_h_1,2, PRIDE_h_1,3, PRIDE_h_1,4], [PRIDE_h_2,0, PRIDE_h_2,1, PRIDE_h_2,2, PRIDE_h_2,3, PRIDE_h_2,4], [PRIDE_h_3,0, PRIDE_h_3,1, PRIDE_h_3,2, PRIDE_h_3,3, PRIDE_h_3,4], [PRIDE_h_4,0, PRIDE_h_4,1, PRIDE_h_4,2, PRIDE_h_4,3, PRIDE_h_4,4], [PRIDE_h_5,0, PRIDE_h_5,1, PRIDE_h_5,2, PRIDE_h_5,3, PRIDE_h_5,4], [PRIDE_h_6,0, PR<PERSON>E_h_6,1, PRIDE_h_6,2, PRIDE_h_6,3, PRIDE_h_6,4], [PRIDE_h_7,0, PRIDE_h_7,1, PRIDE_h_7,2, PRIDE_h_7,3, PRIDE_h_7,4], [PRIDE_h_8,0, PRIDE_h_8,1, PRIDE_h_8,2, PRIDE_h_8,3, PRIDE_h_8,4]], [[PRIDE_v_0,0, PRIDE_v_0,1, PRIDE_v_0,2, PRIDE_v_0,3, PRIDE_v_0,4, PRIDE_v_0,5, PRIDE_v_0,6, PRIDE_v_0,7, PRIDE_v_0,8], [PRIDE_v_1,0, PRIDE_v_1,1, PRIDE_v_1,2, PRIDE_v_1,3, PRIDE_v_1,4, PRIDE_v_1,5, PRIDE_v_1,6, PRIDE_v_1,7, PRIDE_v_1,8], [PRIDE_v_2,0, PRIDE_v_2,1, PRIDE_v_2,2, PRIDE_v_2,3, PRIDE_v_2,4, PRIDE_v_2,5, PRIDE_v_2,6, PRIDE_v_2,7, PRIDE_v_2,8], [PRIDE_v_3,0, PRIDE_v_3,1, PRIDE_v_3,2, PRIDE_v_3,3, PRIDE_v_3,4, PRIDE_v_3,5, PRIDE_v_3,6, PRIDE_v_3,7, PRIDE_v_3,8], [PRIDE_v_4,0, PRIDE_v_4,1, PRIDE_v_4,2, PRIDE_v_4,3, PRIDE_v_4,4, PRIDE_v_4,5, PRIDE_v_4,6, PRIDE_v_4,7, PRIDE_v_4,8]]]\n", "[[[NOVEL_h_0,0, NOVEL_h_0,1, NOVEL_h_0,2, NOVEL_h_0,3, NOVEL_h_0,4], [NOVEL_h_1,0, NOVEL_h_1,1, NOVEL_h_1,2, NOVEL_h_1,3, NOVEL_h_1,4], [NOVEL_h_2,0, NOVEL_h_2,1, NOVEL_h_2,2, NOVEL_h_2,3, NOVEL_h_2,4], [NOVEL_h_3,0, NOVEL_h_3,1, NOVEL_h_3,2, NOVEL_h_3,3, NOVEL_h_3,4], [NOVEL_h_4,0, NOVEL_h_4,1, NOVEL_h_4,2, NOVEL_h_4,3, NOVEL_h_4,4], [NOVEL_h_5,0, NOVEL_h_5,1, NOVEL_h_5,2, NOVEL_h_5,3, NOVEL_h_5,4], [NOVEL_h_6,0, NOVEL_h_6,1, NOVEL_h_6,2, NOVEL_h_6,3, NOVEL_h_6,4], [NOVEL_h_7,0, NOVEL_h_7,1, NOVEL_h_7,2, NOVEL_h_7,3, NOVEL_h_7,4], [NOVEL_h_8,0, NOVEL_h_8,1, NOVEL_h_8,2, NOVEL_h_8,3, NOVEL_h_8,4]], [[NOVEL_v_0,0, NOVEL_v_0,1, NOVEL_v_0,2, NOVEL_v_0,3, NOVEL_v_0,4, NOVEL_v_0,5, NOVEL_v_0,6, NOVEL_v_0,7, NOVEL_v_0,8], [NOVEL_v_1,0, NOVEL_v_1,1, NOVEL_v_1,2, NOVEL_v_1,3, NOVEL_v_1,4, NOVEL_v_1,5, NOVEL_v_1,6, NOVEL_v_1,7, NOVEL_v_1,8], [NOVEL_v_2,0, NOVEL_v_2,1, NOVEL_v_2,2, NOVEL_v_2,3, NOVEL_v_2,4, NOVEL_v_2,5, NOVEL_v_2,6, NOVEL_v_2,7, NOVEL_v_2,8], [NOVEL_v_3,0, NOVEL_v_3,1, NOVEL_v_3,2, NOVEL_v_3,3, NOVEL_v_3,4, NOVEL_v_3,5, NOVEL_v_3,6, NOVEL_v_3,7, NOVEL_v_3,8], [NOVEL_v_4,0, NOVEL_v_4,1, NOVEL_v_4,2, NOVEL_v_4,3, NOVEL_v_4,4, NOVEL_v_4,5, NOVEL_v_4,6, NOVEL_v_4,7, NOVEL_v_4,8]]]\n", "[[[DARCY_h_0,0, <PERSON>ARC<PERSON>_h_0,1, <PERSON>ARC<PERSON>_h_0,2, DARCY_h_0,3, DARCY_h_0,4], [DARCY_h_1,0, DARCY_h_1,1, DARCY_h_1,2, DARCY_h_1,3, DARCY_h_1,4], [DARCY_h_2,0, DARCY_h_2,1, DARCY_h_2,2, DARCY_h_2,3, DARCY_h_2,4], [DARCY_h_3,0, DARCY_h_3,1, DARCY_h_3,2, DARCY_h_3,3, DARCY_h_3,4], [DARCY_h_4,0, DARCY_h_4,1, DARCY_h_4,2, DARCY_h_4,3, DARCY_h_4,4], [DARCY_h_5,0, DARCY_h_5,1, <PERSON>ARC<PERSON>_h_5,2, <PERSON><PERSON><PERSON><PERSON>_h_5,3, <PERSON>ARC<PERSON>_h_5,4], [DARCY_h_6,0, DARCY_h_6,1, DARCY_h_6,2, DARCY_h_6,3, DARCY_h_6,4], [DARCY_h_7,0, DARCY_h_7,1, DARCY_h_7,2, DARCY_h_7,3, DARCY_h_7,4], [DARCY_h_8,0, DARCY_h_8,1, DARCY_h_8,2, DARCY_h_8,3, DARCY_h_8,4]], [[DARCY_v_0,0, DARCY_v_0,1, DARCY_v_0,2, DARCY_v_0,3, DARCY_v_0,4, DARCY_v_0,5, DARCY_v_0,6, DARCY_v_0,7, DARCY_v_0,8], [DARCY_v_1,0, DARCY_v_1,1, DARCY_v_1,2, DARCY_v_1,3, DARCY_v_1,4, DARCY_v_1,5, DARCY_v_1,6, DARCY_v_1,7, DARCY_v_1,8], [DARCY_v_2,0, DARCY_v_2,1, DARCY_v_2,2, DARCY_v_2,3, DARCY_v_2,4, DARCY_v_2,5, DARCY_v_2,6, DARCY_v_2,7, DARCY_v_2,8], [DARCY_v_3,0, DARCY_v_3,1, DARCY_v_3,2, DARCY_v_3,3, DARCY_v_3,4, DARCY_v_3,5, DARCY_v_3,6, DARCY_v_3,7, DARCY_v_3,8], [DARCY_v_4,0, DARCY_v_4,1, DARCY_v_4,2, DARCY_v_4,3, DARCY_v_4,4, DARCY_v_4,5, DARCY_v_4,6, DARCY_v_4,7, DARCY_v_4,8]]]\n", "[[[SENSE_h_0,0, SENSE_h_0,1, SENSE_h_0,2, SENSE_h_0,3, SENSE_h_0,4], [SENSE_h_1,0, SENSE_h_1,1, SENSE_h_1,2, SENSE_h_1,3, SENSE_h_1,4], [SENSE_h_2,0, SENSE_h_2,1, SENSE_h_2,2, SENSE_h_2,3, SENSE_h_2,4], [SENSE_h_3,0, SENSE_h_3,1, SENSE_h_3,2, SENSE_h_3,3, SENSE_h_3,4], [SENSE_h_4,0, SENSE_h_4,1, SENSE_h_4,2, SENSE_h_4,3, SENSE_h_4,4], [SENSE_h_5,0, SENSE_h_5,1, SENSE_h_5,2, SENSE_h_5,3, SENSE_h_5,4], [SENSE_h_6,0, SENSE_h_6,1, SENSE_h_6,2, SENSE_h_6,3, SENSE_h_6,4], [SENSE_h_7,0, SENSE_h_7,1, SENSE_h_7,2, SENSE_h_7,3, SENSE_h_7,4], [SENSE_h_8,0, SENSE_h_8,1, SENSE_h_8,2, SENSE_h_8,3, SENSE_h_8,4]], [[SENSE_v_0,0, SENSE_v_0,1, SENSE_v_0,2, SENSE_v_0,3, SENSE_v_0,4, SENSE_v_0,5, SENSE_v_0,6, SENSE_v_0,7, SENSE_v_0,8], [SENSE_v_1,0, SENSE_v_1,1, SENSE_v_1,2, SENSE_v_1,3, SENSE_v_1,4, SENSE_v_1,5, SENSE_v_1,6, SENSE_v_1,7, SENSE_v_1,8], [SENSE_v_2,0, SENSE_v_2,1, SENSE_v_2,2, SENSE_v_2,3, SENSE_v_2,4, SENSE_v_2,5, SENSE_v_2,6, SENSE_v_2,7, SENSE_v_2,8], [SENSE_v_3,0, SENSE_v_3,1, SENSE_v_3,2, SENSE_v_3,3, SENSE_v_3,4, SENSE_v_3,5, SENSE_v_3,6, SENSE_v_3,7, SENSE_v_3,8], [SENSE_v_4,0, SENSE_v_4,1, SENSE_v_4,2, SENSE_v_4,3, SENSE_v_4,4, SENSE_v_4,5, SENSE_v_4,6, SENSE_v_4,7, SENSE_v_4,8]]]\n", "[[[EMMA_h_0,0, <PERSON>MM<PERSON>_h_0,1, EMM<PERSON>_h_0,2, EMMA_h_0,3, EMMA_h_0,4, EMMA_h_0,5], [EMMA_h_1,0, EMMA_h_1,1, EMMA_h_1,2, EMMA_h_1,3, EMMA_h_1,4, EMMA_h_1,5], [EMMA_h_2,0, EMMA_h_2,1, EMMA_h_2,2, EMMA_h_2,3, EMMA_h_2,4, EMMA_h_2,5], [EMMA_h_3,0, EMMA_h_3,1, EMMA_h_3,2, EMMA_h_3,3, EMMA_h_3,4, EMMA_h_3,5], [EMMA_h_4,0, EMMA_h_4,1, EMMA_h_4,2, EMMA_h_4,3, EMMA_h_4,4, EMMA_h_4,5], [EMMA_h_5,0, <PERSON><PERSON><PERSON><PERSON>h_5,1, EMM<PERSON>_h_5,2, EMMA_h_5,3, EMMA_h_5,4, EMMA_h_5,5], [EMMA_h_6,0, <PERSON>MMA_h_6,1, EMMA_h_6,2, EMMA_h_6,3, <PERSON>MMA_h_6,4, <PERSON>MMA_h_6,5], [EMMA_h_7,0, EMMA_h_7,1, EMMA_h_7,2, EMMA_h_7,3, <PERSON>MMA_h_7,4, <PERSON>MM<PERSON>_h_7,5], [<PERSON>MMA_h_8,0, <PERSON>MMA_h_8,1, EMMA_h_8,2, EMMA_h_8,3, EMMA_h_8,4, EMMA_h_8,5]], [[EMMA_v_0,0, EMMA_v_0,1, EMMA_v_0,2, EMMA_v_0,3, EMMA_v_0,4, EMMA_v_0,5, EMMA_v_0,6, EMMA_v_0,7, EMMA_v_0,8], [EMMA_v_1,0, EMMA_v_1,1, EMMA_v_1,2, EMMA_v_1,3, EMMA_v_1,4, EMMA_v_1,5, EMMA_v_1,6, EMMA_v_1,7, EMMA_v_1,8], [EMMA_v_2,0, EMMA_v_2,1, EMMA_v_2,2, EMMA_v_2,3, EMMA_v_2,4, EMMA_v_2,5, EMMA_v_2,6, EMMA_v_2,7, EMMA_v_2,8], [EMMA_v_3,0, EMMA_v_3,1, EMMA_v_3,2, EMMA_v_3,3, EMMA_v_3,4, EMMA_v_3,5, EMMA_v_3,6, EMMA_v_3,7, EMMA_v_3,8], [EMMA_v_4,0, EMMA_v_4,1, EMMA_v_4,2, EMMA_v_4,3, EMMA_v_4,4, EMMA_v_4,5, EMMA_v_4,6, EMMA_v_4,7, EMMA_v_4,8], [EMMA_v_5,0, EMMA_v_5,1, EMMA_v_5,2, EMMA_v_5,3, EMMA_v_5,4, EMMA_v_5,5, EMMA_v_5,6, EMMA_v_5,7, EMMA_v_5,8]]]\n", "[[[ESTATE_h_0,0, ESTATE_h_0,1, ESTATE_h_0,2, ESTATE_h_0,3], [ESTATE_h_1,0, ESTATE_h_1,1, ESTATE_h_1,2, ESTATE_h_1,3], [ESTATE_h_2,0, ESTATE_h_2,1, ESTATE_h_2,2, ESTATE_h_2,3], [ESTATE_h_3,0, ESTATE_h_3,1, ESTATE_h_3,2, ESTATE_h_3,3], [ESTATE_h_4,0, ESTATE_h_4,1, ESTATE_h_4,2, ESTATE_h_4,3], [ESTATE_h_5,0, ESTATE_h_5,1, ESTATE_h_5,2, ESTATE_h_5,3], [ESTATE_h_6,0, ESTATE_h_6,1, ESTATE_h_6,2, ESTATE_h_6,3], [ESTATE_h_7,0, ESTATE_h_7,1, ESTATE_h_7,2, ESTATE_h_7,3], [ESTATE_h_8,0, ESTATE_h_8,1, ESTATE_h_8,2, ESTATE_h_8,3]], [[ESTATE_v_0,0, ESTATE_v_0,1, ESTATE_v_0,2, ESTATE_v_0,3, ESTATE_v_0,4, ESTATE_v_0,5, ESTATE_v_0,6, ESTATE_v_0,7, ESTATE_v_0,8], [ESTATE_v_1,0, ESTATE_v_1,1, ESTATE_v_1,2, ESTATE_v_1,3, ESTATE_v_1,4, ESTATE_v_1,5, ESTATE_v_1,6, ESTATE_v_1,7, ESTATE_v_1,8], [ESTATE_v_2,0, ESTATE_v_2,1, ESTATE_v_2,2, ESTATE_v_2,3, ESTATE_v_2,4, ESTATE_v_2,5, ESTATE_v_2,6, ESTATE_v_2,7, ESTATE_v_2,8], [ESTATE_v_3,0, ESTATE_v_3,1, ESTATE_v_3,2, ESTATE_v_3,3, ESTATE_v_3,4, ESTATE_v_3,5, ESTATE_v_3,6, ESTATE_v_3,7, ESTATE_v_3,8]]]\n", "[[[BENNET_h_0,0, BENNET_h_0,1, BENNET_h_0,2, BENNET_h_0,3], [BENNET_h_1,0, BENNET_h_1,1, BENNET_h_1,2, BENNET_h_1,3], [BENNET_h_2,0, BENNET_h_2,1, BENNET_h_2,2, BENNET_h_2,3], [BENNET_h_3,0, BENNET_h_3,1, BENNET_h_3,2, BENNET_h_3,3], [BENNET_h_4,0, BENNET_h_4,1, BENNET_h_4,2, BENNET_h_4,3], [BENNET_h_5,0, BENNET_h_5,1, BENNET_h_5,2, BENNET_h_5,3], [BENNET_h_6,0, BENNET_h_6,1, BENNET_h_6,2, BENNET_h_6,3], [BENNET_h_7,0, BENNET_h_7,1, BENNET_h_7,2, BENNET_h_7,3], [BENNET_h_8,0, BENNET_h_8,1, BENNET_h_8,2, BENNET_h_8,3]], [[BENNET_v_0,0, BENNET_v_0,1, BENNET_v_0,2, BENNET_v_0,3, BENNET_v_0,4, BENNET_v_0,5, BENNET_v_0,6, BENNET_v_0,7, BENNET_v_0,8], [BENNET_v_1,0, BENNET_v_1,1, BENNET_v_1,2, BENNET_v_1,3, BENNET_v_1,4, BENNET_v_1,5, BENNET_v_1,6, BENNET_v_1,7, BENNET_v_1,8], [BENNET_v_2,0, BENNET_v_2,1, BENNET_v_2,2, BENNET_v_2,3, BENNET_v_2,4, BENNET_v_2,5, BENNET_v_2,6, BENNET_v_2,7, BENNET_v_2,8], [BENNET_v_3,0, BENNET_v_3,1, BENNET_v_3,2, BENNET_v_3,3, BENNET_v_3,4, BENNET_v_3,5, BENNET_v_3,6, BENNET_v_3,7, BENNET_v_3,8]]]\n", "[[[BATH_h_0,0, BATH_h_0,1, BATH_h_0,2, BATH_h_0,3, BATH_h_0,4, BATH_h_0,5], [BATH_h_1,0, BATH_h_1,1, BATH_h_1,2, BATH_h_1,3, BATH_h_1,4, BATH_h_1,5], [BATH_h_2,0, BATH_h_2,1, BATH_h_2,2, BATH_h_2,3, BATH_h_2,4, BATH_h_2,5], [BATH_h_3,0, BATH_h_3,1, BATH_h_3,2, BATH_h_3,3, BATH_h_3,4, BATH_h_3,5], [BATH_h_4,0, BATH_h_4,1, BATH_h_4,2, BATH_h_4,3, BATH_h_4,4, BATH_h_4,5], [BATH_h_5,0, BATH_h_5,1, BATH_h_5,2, BATH_h_5,3, BATH_h_5,4, BATH_h_5,5], [BATH_h_6,0, BATH_h_6,1, BATH_h_6,2, BATH_h_6,3, BATH_h_6,4, BATH_h_6,5], [BATH_h_7,0, BATH_h_7,1, BATH_h_7,2, BATH_h_7,3, BATH_h_7,4, BATH_h_7,5], [BATH_h_8,0, BATH_h_8,1, BATH_h_8,2, BATH_h_8,3, BATH_h_8,4, BATH_h_8,5]], [[BATH_v_0,0, BATH_v_0,1, BATH_v_0,2, BATH_v_0,3, BATH_v_0,4, BATH_v_0,5, BATH_v_0,6, BATH_v_0,7, BATH_v_0,8], [BATH_v_1,0, BATH_v_1,1, BATH_v_1,2, BATH_v_1,3, BATH_v_1,4, BATH_v_1,5, BATH_v_1,6, BATH_v_1,7, BATH_v_1,8], [BATH_v_2,0, BATH_v_2,1, BATH_v_2,2, BATH_v_2,3, BATH_v_2,4, BATH_v_2,5, BATH_v_2,6, BATH_v_2,7, BATH_v_2,8], [BATH_v_3,0, BATH_v_3,1, BATH_v_3,2, BATH_v_3,3, BATH_v_3,4, BATH_v_3,5, BATH_v_3,6, BATH_v_3,7, BATH_v_3,8], [BATH_v_4,0, BATH_v_4,1, BATH_v_4,2, BATH_v_4,3, BATH_v_4,4, BATH_v_4,5, BATH_v_4,6, BATH_v_4,7, BATH_v_4,8], [BATH_v_5,0, BATH_v_5,1, BATH_v_5,2, BATH_v_5,3, BATH_v_5,4, BATH_v_5,5, BATH_v_5,6, BATH_v_5,7, BATH_v_5,8]]]\n", "Executed in 0.8997 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█│█║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╧═╝\n", "[[[JANE_h_0,0, <PERSON><PERSON><PERSON><PERSON>h_0,1, <PERSON><PERSON><PERSON><PERSON>h_0,2, <PERSON><PERSON><PERSON><PERSON>h_0,3, <PERSON>AN<PERSON><PERSON>h_0,4], [JANE_h_1,0, JANE_h_1,1, JANE_h_1,2, <PERSON><PERSON><PERSON><PERSON>h_1,3, J<PERSON><PERSON><PERSON>h_1,4], [JANE_h_2,0, JANE_h_2,1, JANE_h_2,2, JANE_h_2,3, JANE_h_2,4], [JANE_h_3,0, JANE_h_3,1, JANE_h_3,2, JANE_h_3,3, JANE_h_3,4], [JANE_h_4,0, JANE_h_4,1, JANE_h_4,2, JANE_h_4,3, JANE_h_4,4], [JANE_h_5,0, J<PERSON><PERSON>_h_5,1, JANE_h_5,2, JANE_h_5,3, JAN<PERSON><PERSON>h_5,4], [JANE_h_6,0, <PERSON><PERSON><PERSON><PERSON>h_6,1, <PERSON><PERSON><PERSON><PERSON>h_6,2, JAN<PERSON><PERSON>h_6,3, <PERSON><PERSON><PERSON>_h_6,4], [JANE_h_7,0, J<PERSON><PERSON>_h_7,1, JANE_h_7,2, JANE_h_7,3, JANE_h_7,4]], [[JANE_v_0,0, JANE_v_0,1, JANE_v_0,2, JANE_v_0,3, JANE_v_0,4, JANE_v_0,5, JANE_v_0,6, JANE_v_0,7], [JANE_v_1,0, JANE_v_1,1, JANE_v_1,2, JANE_v_1,3, JANE_v_1,4, JANE_v_1,5, JANE_v_1,6, JANE_v_1,7], [JANE_v_2,0, JANE_v_2,1, JANE_v_2,2, JANE_v_2,3, JANE_v_2,4, JANE_v_2,5, JANE_v_2,6, JANE_v_2,7], [JANE_v_3,0, JANE_v_3,1, JANE_v_3,2, JANE_v_3,3, JANE_v_3,4, JANE_v_3,5, JANE_v_3,6, JANE_v_3,7], [JANE_v_4,0, JANE_v_4,1, JANE_v_4,2, JANE_v_4,3, JANE_v_4,4, JANE_v_4,5, JANE_v_4,6, JANE_v_4,7]]]\n", "[[[AUSTEN_h_0,0, AUSTEN_h_0,1, AUSTEN_h_0,2], [AUSTEN_h_1,0, AUSTEN_h_1,1, AUSTEN_h_1,2], [AUSTEN_h_2,0, AUSTEN_h_2,1, AUSTEN_h_2,2], [AUSTEN_h_3,0, AUSTEN_h_3,1, AUSTEN_h_3,2], [AUSTEN_h_4,0, AUSTEN_h_4,1, AUSTEN_h_4,2], [AUSTEN_h_5,0, AUSTEN_h_5,1, AUSTEN_h_5,2], [AUSTEN_h_6,0, AUSTEN_h_6,1, AUSTEN_h_6,2], [AUSTEN_h_7,0, AUSTEN_h_7,1, AUSTEN_h_7,2]], [[AUSTEN_v_0,0, AUSTEN_v_0,1, AUSTEN_v_0,2, AUSTEN_v_0,3, AUSTEN_v_0,4, AUSTEN_v_0,5, AUSTEN_v_0,6, AUSTEN_v_0,7], [AUSTEN_v_1,0, AUSTEN_v_1,1, AUSTEN_v_1,2, AUSTEN_v_1,3, AUSTEN_v_1,4, AUSTEN_v_1,5, AUSTEN_v_1,6, AUSTEN_v_1,7], [AUSTEN_v_2,0, AUSTEN_v_2,1, AUSTEN_v_2,2, AUSTEN_v_2,3, AUSTEN_v_2,4, AUSTEN_v_2,5, AUSTEN_v_2,6, AUSTEN_v_2,7]]]\n", "[[[PRIDE_h_0,0, PRIDE_h_0,1, PRIDE_h_0,2, PRIDE_h_0,3], [PRIDE_h_1,0, PRIDE_h_1,1, PRIDE_h_1,2, PRIDE_h_1,3], [PRIDE_h_2,0, PRIDE_h_2,1, PRIDE_h_2,2, PRIDE_h_2,3], [PRIDE_h_3,0, PRIDE_h_3,1, PRIDE_h_3,2, PRIDE_h_3,3], [PRIDE_h_4,0, PRIDE_h_4,1, PRIDE_h_4,2, PRIDE_h_4,3], [PRIDE_h_5,0, PRIDE_h_5,1, PRIDE_h_5,2, PRIDE_h_5,3], [PRIDE_h_6,0, PRIDE_h_6,1, PRIDE_h_6,2, PRIDE_h_6,3], [PRIDE_h_7,0, PRIDE_h_7,1, PRIDE_h_7,2, PR<PERSON>E_h_7,3]], [[PRIDE_v_0,0, PRIDE_v_0,1, PRIDE_v_0,2, PRIDE_v_0,3, PRIDE_v_0,4, PRIDE_v_0,5, PRIDE_v_0,6, PRIDE_v_0,7], [PRIDE_v_1,0, PRIDE_v_1,1, PRIDE_v_1,2, PRIDE_v_1,3, PRIDE_v_1,4, PRIDE_v_1,5, PRIDE_v_1,6, PRIDE_v_1,7], [PRIDE_v_2,0, PRIDE_v_2,1, PRIDE_v_2,2, PRIDE_v_2,3, PRIDE_v_2,4, PRIDE_v_2,5, PRIDE_v_2,6, PRIDE_v_2,7], [PRIDE_v_3,0, PRIDE_v_3,1, PRIDE_v_3,2, PRIDE_v_3,3, PRIDE_v_3,4, PRIDE_v_3,5, PRIDE_v_3,6, PRIDE_v_3,7]]]\n", "[[[NOVEL_h_0,0, NOVEL_h_0,1, NOVEL_h_0,2, NOVEL_h_0,3], [NOVEL_h_1,0, NOVEL_h_1,1, NOVEL_h_1,2, NOVEL_h_1,3], [NOVEL_h_2,0, NOVEL_h_2,1, NOVEL_h_2,2, NOVEL_h_2,3], [NOVEL_h_3,0, NOVEL_h_3,1, NOVEL_h_3,2, NOVEL_h_3,3], [NOVEL_h_4,0, NOVEL_h_4,1, NOVEL_h_4,2, NOVEL_h_4,3], [NOVEL_h_5,0, NOVEL_h_5,1, NOVEL_h_5,2, NOVEL_h_5,3], [NOVEL_h_6,0, NOVEL_h_6,1, NOVEL_h_6,2, NOVEL_h_6,3], [NOVEL_h_7,0, NOVEL_h_7,1, NOVEL_h_7,2, NOVEL_h_7,3]], [[NOVEL_v_0,0, NOVEL_v_0,1, NOVEL_v_0,2, NOVEL_v_0,3, NOVEL_v_0,4, NOVEL_v_0,5, NOVEL_v_0,6, NOVEL_v_0,7], [NOVEL_v_1,0, NOVEL_v_1,1, NOVEL_v_1,2, NOVEL_v_1,3, NOVEL_v_1,4, NOVEL_v_1,5, NOVEL_v_1,6, NOVEL_v_1,7], [NOVEL_v_2,0, NOVEL_v_2,1, NOVEL_v_2,2, NOVEL_v_2,3, NOVEL_v_2,4, NOVEL_v_2,5, NOVEL_v_2,6, NOVEL_v_2,7], [NOVEL_v_3,0, NOVEL_v_3,1, NOVEL_v_3,2, NOVEL_v_3,3, NOVEL_v_3,4, NOVEL_v_3,5, NOVEL_v_3,6, NOVEL_v_3,7]]]\n", "[[[DARCY_h_0,0, DARCY_h_0,1, DARCY_h_0,2, DARCY_h_0,3], [DARCY_h_1,0, DARCY_h_1,1, DARCY_h_1,2, DARCY_h_1,3], [DARCY_h_2,0, DARCY_h_2,1, DARCY_h_2,2, DARCY_h_2,3], [DARCY_h_3,0, DARCY_h_3,1, DARCY_h_3,2, DARCY_h_3,3], [DARCY_h_4,0, DARCY_h_4,1, DARCY_h_4,2, DARCY_h_4,3], [DARCY_h_5,0, DARCY_h_5,1, DARCY_h_5,2, DARCY_h_5,3], [DARCY_h_6,0, DARCY_h_6,1, DARCY_h_6,2, DARCY_h_6,3], [DARCY_h_7,0, DARCY_h_7,1, DARCY_h_7,2, DARCY_h_7,3]], [[DARCY_v_0,0, DARCY_v_0,1, DARCY_v_0,2, DARCY_v_0,3, DARCY_v_0,4, DARCY_v_0,5, DARCY_v_0,6, DARCY_v_0,7], [DARCY_v_1,0, DARCY_v_1,1, DARCY_v_1,2, DARCY_v_1,3, DARCY_v_1,4, DARCY_v_1,5, DARCY_v_1,6, DARCY_v_1,7], [DARCY_v_2,0, DARCY_v_2,1, DARCY_v_2,2, DARCY_v_2,3, DARCY_v_2,4, DARCY_v_2,5, DARCY_v_2,6, DARCY_v_2,7], [DARCY_v_3,0, DARCY_v_3,1, DARCY_v_3,2, DARCY_v_3,3, DARCY_v_3,4, DARCY_v_3,5, DARCY_v_3,6, DARCY_v_3,7]]]\n", "[[[SENSE_h_0,0, SENSE_h_0,1, SENSE_h_0,2, SENSE_h_0,3], [SENSE_h_1,0, SENSE_h_1,1, SENSE_h_1,2, SENSE_h_1,3], [SENSE_h_2,0, SENSE_h_2,1, SENSE_h_2,2, SENSE_h_2,3], [SENSE_h_3,0, SENSE_h_3,1, SENSE_h_3,2, SENSE_h_3,3], [SENSE_h_4,0, SENSE_h_4,1, SENSE_h_4,2, SENSE_h_4,3], [SENSE_h_5,0, SENSE_h_5,1, SENSE_h_5,2, SENSE_h_5,3], [SENSE_h_6,0, SENSE_h_6,1, SENSE_h_6,2, SENSE_h_6,3], [SENSE_h_7,0, SENSE_h_7,1, SENSE_h_7,2, SENSE_h_7,3]], [[SENSE_v_0,0, SENSE_v_0,1, SENSE_v_0,2, SENSE_v_0,3, SENSE_v_0,4, SENSE_v_0,5, SENSE_v_0,6, SENSE_v_0,7], [SENSE_v_1,0, SENSE_v_1,1, SENSE_v_1,2, SENSE_v_1,3, SENSE_v_1,4, SENSE_v_1,5, SENSE_v_1,6, SENSE_v_1,7], [SENSE_v_2,0, SENSE_v_2,1, SENSE_v_2,2, SENSE_v_2,3, SENSE_v_2,4, SENSE_v_2,5, SENSE_v_2,6, SENSE_v_2,7], [SENSE_v_3,0, SENSE_v_3,1, SENSE_v_3,2, SENSE_v_3,3, SENSE_v_3,4, SENSE_v_3,5, SENSE_v_3,6, SENSE_v_3,7]]]\n", "[[[EMMA_h_0,0, EMM<PERSON>_h_0,1, EMM<PERSON>_h_0,2, EMMA_h_0,3, EMMA_h_0,4], [EMMA_h_1,0, EMMA_h_1,1, EMMA_h_1,2, EMMA_h_1,3, EMMA_h_1,4], [EMMA_h_2,0, EMMA_h_2,1, EMMA_h_2,2, EMMA_h_2,3, EMMA_h_2,4], [EMMA_h_3,0, EMMA_h_3,1, EMMA_h_3,2, EMMA_h_3,3, EMMA_h_3,4], [EMMA_h_4,0, EMMA_h_4,1, EMMA_h_4,2, EMMA_h_4,3, EMMA_h_4,4], [EMMA_h_5,0, EMMA_h_5,1, EMMA_h_5,2, EMMA_h_5,3, EMMA_h_5,4], [EMMA_h_6,0, EMM<PERSON>_h_6,1, EMMA_h_6,2, EMMA_h_6,3, EMMA_h_6,4], [EMMA_h_7,0, EMMA_h_7,1, EMMA_h_7,2, EMMA_h_7,3, EMMA_h_7,4]], [[EMMA_v_0,0, EMMA_v_0,1, EMMA_v_0,2, EMMA_v_0,3, EMMA_v_0,4, EMMA_v_0,5, EMMA_v_0,6, EMMA_v_0,7], [EMMA_v_1,0, EMMA_v_1,1, EMMA_v_1,2, EMMA_v_1,3, EMMA_v_1,4, EMMA_v_1,5, EMMA_v_1,6, EMMA_v_1,7], [EMMA_v_2,0, EMMA_v_2,1, EMMA_v_2,2, EMMA_v_2,3, EMMA_v_2,4, EMMA_v_2,5, EMMA_v_2,6, EMMA_v_2,7], [EMMA_v_3,0, EMMA_v_3,1, EMMA_v_3,2, EMMA_v_3,3, EMMA_v_3,4, EMMA_v_3,5, EMMA_v_3,6, EMMA_v_3,7], [EMMA_v_4,0, EMMA_v_4,1, EMMA_v_4,2, EMMA_v_4,3, EMMA_v_4,4, EMMA_v_4,5, EMMA_v_4,6, EMMA_v_4,7]]]\n", "[[[ESTATE_h_0,0, ESTATE_h_0,1, ESTATE_h_0,2], [ESTATE_h_1,0, ESTATE_h_1,1, ESTATE_h_1,2], [ESTATE_h_2,0, ESTATE_h_2,1, ESTATE_h_2,2], [ESTATE_h_3,0, ESTATE_h_3,1, ESTATE_h_3,2], [ESTATE_h_4,0, ESTATE_h_4,1, ESTATE_h_4,2], [ESTATE_h_5,0, ESTATE_h_5,1, ESTATE_h_5,2], [ESTATE_h_6,0, ESTATE_h_6,1, ESTATE_h_6,2], [ESTATE_h_7,0, ESTATE_h_7,1, ESTATE_h_7,2]], [[ESTATE_v_0,0, ESTATE_v_0,1, ESTATE_v_0,2, ESTATE_v_0,3, ESTATE_v_0,4, ESTATE_v_0,5, ESTATE_v_0,6, ESTATE_v_0,7], [ESTATE_v_1,0, ESTATE_v_1,1, ESTATE_v_1,2, ESTATE_v_1,3, ESTATE_v_1,4, ESTATE_v_1,5, ESTATE_v_1,6, ESTATE_v_1,7], [ESTATE_v_2,0, ESTATE_v_2,1, ESTATE_v_2,2, ESTATE_v_2,3, ESTATE_v_2,4, ESTATE_v_2,5, ESTATE_v_2,6, ESTATE_v_2,7]]]\n", "[[[BENNET_h_0,0, BENNET_h_0,1, BENNET_h_0,2], [BENNET_h_1,0, BENNET_h_1,1, BENNET_h_1,2], [BENNET_h_2,0, BENNET_h_2,1, BENNET_h_2,2], [BENNET_h_3,0, BENNET_h_3,1, BENNET_h_3,2], [BENNET_h_4,0, BENNET_h_4,1, BENNET_h_4,2], [BENNET_h_5,0, BENNET_h_5,1, BENNET_h_5,2], [BENNET_h_6,0, BENNET_h_6,1, BENNET_h_6,2], [BENNET_h_7,0, BENNET_h_7,1, BENNET_h_7,2]], [[BENNET_v_0,0, BENNET_v_0,1, BENNET_v_0,2, BENNET_v_0,3, BENNET_v_0,4, BENNET_v_0,5, BENNET_v_0,6, BENNET_v_0,7], [BENNET_v_1,0, BENNET_v_1,1, BENNET_v_1,2, BENNET_v_1,3, BENNET_v_1,4, BENNET_v_1,5, BENNET_v_1,6, BENNET_v_1,7], [BENNET_v_2,0, BENNET_v_2,1, BENNET_v_2,2, BENNET_v_2,3, BENNET_v_2,4, BENNET_v_2,5, BENNET_v_2,6, BENNET_v_2,7]]]\n", "[[[BATH_h_0,0, BATH_h_0,1, BATH_h_0,2, BATH_h_0,3, BATH_h_0,4], [BATH_h_1,0, BATH_h_1,1, BATH_h_1,2, BATH_h_1,3, BATH_h_1,4], [BATH_h_2,0, BATH_h_2,1, BATH_h_2,2, BATH_h_2,3, BATH_h_2,4], [BATH_h_3,0, BATH_h_3,1, BATH_h_3,2, BATH_h_3,3, BATH_h_3,4], [BATH_h_4,0, BATH_h_4,1, BATH_h_4,2, BATH_h_4,3, BATH_h_4,4], [BATH_h_5,0, BATH_h_5,1, BATH_h_5,2, BATH_h_5,3, BATH_h_5,4], [BATH_h_6,0, BATH_h_6,1, BATH_h_6,2, BATH_h_6,3, BATH_h_6,4], [BATH_h_7,0, BATH_h_7,1, BATH_h_7,2, BATH_h_7,3, BATH_h_7,4]], [[BATH_v_0,0, BATH_v_0,1, BATH_v_0,2, BATH_v_0,3, BATH_v_0,4, BATH_v_0,5, BATH_v_0,6, BATH_v_0,7], [BATH_v_1,0, BATH_v_1,1, BATH_v_1,2, BATH_v_1,3, BATH_v_1,4, BATH_v_1,5, BATH_v_1,6, BATH_v_1,7], [BATH_v_2,0, BATH_v_2,1, BATH_v_2,2, BATH_v_2,3, BATH_v_2,4, BATH_v_2,5, BATH_v_2,6, BATH_v_2,7], [BATH_v_3,0, BATH_v_3,1, BATH_v_3,2, BATH_v_3,3, BATH_v_3,4, BATH_v_3,5, BATH_v_3,6, BATH_v_3,7], [BATH_v_4,0, BATH_v_4,1, BATH_v_4,2, BATH_v_4,3, BATH_v_4,4, BATH_v_4,5, BATH_v_4,6, BATH_v_4,7]]]\n", "Executed in 0.8943 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█│█║\n", "╚═╧═╧═╧═╧═╧═╧═╧═╝\n", "[[[JANE_h_0,0, <PERSON><PERSON><PERSON><PERSON>h_0,1, <PERSON><PERSON><PERSON><PERSON>h_0,2, JAN<PERSON><PERSON>h_0,3], [JANE_h_1,0, JANE_h_1,1, JANE_h_1,2, JANE_h_1,3], [JANE_h_2,0, JANE_h_2,1, JANE_h_2,2, JANE_h_2,3], [JANE_h_3,0, JANE_h_3,1, JANE_h_3,2, JANE_h_3,3], [JANE_h_4,0, JANE_h_4,1, JANE_h_4,2, JANE_h_4,3], [JANE_h_5,0, JANE_h_5,1, JANE_h_5,2, JANE_h_5,3], [JANE_h_6,0, JANE_h_6,1, JAN<PERSON>_h_6,2, JANE_h_6,3]], [[JANE_v_0,0, JAN<PERSON>_v_0,1, <PERSON><PERSON><PERSON><PERSON>v_0,2, <PERSON><PERSON><PERSON><PERSON>v_0,3, JANE_v_0,4, JAN<PERSON>_v_0,5, JANE_v_0,6], [JANE_v_1,0, JANE_v_1,1, JANE_v_1,2, JANE_v_1,3, JANE_v_1,4, JANE_v_1,5, JANE_v_1,6], [JANE_v_2,0, JANE_v_2,1, JANE_v_2,2, JANE_v_2,3, JANE_v_2,4, JANE_v_2,5, JANE_v_2,6], [JANE_v_3,0, JANE_v_3,1, JANE_v_3,2, JANE_v_3,3, JANE_v_3,4, JANE_v_3,5, JANE_v_3,6]]]\n", "[[[AUSTEN_h_0,0, AUSTEN_h_0,1], [AUSTEN_h_1,0, AUSTEN_h_1,1], [AUSTEN_h_2,0, AUSTEN_h_2,1], [AUSTEN_h_3,0, AUSTEN_h_3,1], [AUSTEN_h_4,0, AUSTEN_h_4,1], [AUSTEN_h_5,0, AUSTEN_h_5,1], [AUSTEN_h_6,0, AUSTEN_h_6,1]], [[AUSTEN_v_0,0, AUSTEN_v_0,1, AUSTEN_v_0,2, AUSTEN_v_0,3, AUSTEN_v_0,4, AUSTEN_v_0,5, AUSTEN_v_0,6], [AUSTEN_v_1,0, AUSTEN_v_1,1, AUSTEN_v_1,2, AUSTEN_v_1,3, AUSTEN_v_1,4, AUSTEN_v_1,5, AUSTEN_v_1,6]]]\n", "[[[PRIDE_h_0,0, PRIDE_h_0,1, PRIDE_h_0,2], [PRIDE_h_1,0, PRIDE_h_1,1, PRIDE_h_1,2], [PRIDE_h_2,0, PRIDE_h_2,1, PRIDE_h_2,2], [PRIDE_h_3,0, PRIDE_h_3,1, PRIDE_h_3,2], [PRIDE_h_4,0, PRIDE_h_4,1, PRIDE_h_4,2], [PRIDE_h_5,0, PRIDE_h_5,1, PRIDE_h_5,2], [PRIDE_h_6,0, PRIDE_h_6,1, PRIDE_h_6,2]], [[PRIDE_v_0,0, PRIDE_v_0,1, PRIDE_v_0,2, PRIDE_v_0,3, PRIDE_v_0,4, PRIDE_v_0,5, PRIDE_v_0,6], [PRIDE_v_1,0, PRIDE_v_1,1, PRIDE_v_1,2, PRIDE_v_1,3, PRIDE_v_1,4, PRIDE_v_1,5, PRIDE_v_1,6], [PRIDE_v_2,0, PRIDE_v_2,1, PRIDE_v_2,2, PRIDE_v_2,3, PRIDE_v_2,4, PRIDE_v_2,5, PRIDE_v_2,6]]]\n", "[[[NOVEL_h_0,0, NOVEL_h_0,1, NOVEL_h_0,2], [NOVEL_h_1,0, NOVEL_h_1,1, NOVEL_h_1,2], [NOVEL_h_2,0, NOVEL_h_2,1, NOVEL_h_2,2], [NOVEL_h_3,0, NOVEL_h_3,1, NOVEL_h_3,2], [NOVEL_h_4,0, NOVEL_h_4,1, NOVEL_h_4,2], [NOVEL_h_5,0, NOVEL_h_5,1, NOVEL_h_5,2], [NOVEL_h_6,0, NOVEL_h_6,1, NOVEL_h_6,2]], [[NOVEL_v_0,0, NOVEL_v_0,1, NOVEL_v_0,2, NOVEL_v_0,3, NOVEL_v_0,4, NOVEL_v_0,5, NOVEL_v_0,6], [NOVEL_v_1,0, NOVEL_v_1,1, NOVEL_v_1,2, NOVEL_v_1,3, NOVEL_v_1,4, NOVEL_v_1,5, NOVEL_v_1,6], [NOVEL_v_2,0, NOVEL_v_2,1, NOVEL_v_2,2, NOVEL_v_2,3, NOVEL_v_2,4, NOVEL_v_2,5, NOVEL_v_2,6]]]\n", "[[[DARCY_h_0,0, DARC<PERSON>_h_0,1, DARCY_h_0,2], [DARCY_h_1,0, DARCY_h_1,1, DARCY_h_1,2], [DARCY_h_2,0, DARCY_h_2,1, DARCY_h_2,2], [DARCY_h_3,0, DARCY_h_3,1, DARCY_h_3,2], [DARCY_h_4,0, DARCY_h_4,1, DARCY_h_4,2], [DARCY_h_5,0, DARCY_h_5,1, DARCY_h_5,2], [DARCY_h_6,0, DARCY_h_6,1, DARCY_h_6,2]], [[DARCY_v_0,0, DARCY_v_0,1, DARCY_v_0,2, DARCY_v_0,3, DARCY_v_0,4, DARCY_v_0,5, DARCY_v_0,6], [DARCY_v_1,0, DARCY_v_1,1, DARCY_v_1,2, DARCY_v_1,3, DARCY_v_1,4, DARCY_v_1,5, DARCY_v_1,6], [DARCY_v_2,0, DARCY_v_2,1, DARCY_v_2,2, DARCY_v_2,3, DARCY_v_2,4, DARCY_v_2,5, DARCY_v_2,6]]]\n", "[[[SENSE_h_0,0, SENSE_h_0,1, SENSE_h_0,2], [SENSE_h_1,0, SENSE_h_1,1, SENSE_h_1,2], [SENSE_h_2,0, SENSE_h_2,1, SENSE_h_2,2], [SENSE_h_3,0, SENSE_h_3,1, SENSE_h_3,2], [SENSE_h_4,0, SENSE_h_4,1, SENSE_h_4,2], [SENSE_h_5,0, SENSE_h_5,1, SENSE_h_5,2], [SENSE_h_6,0, SENSE_h_6,1, SENSE_h_6,2]], [[SENSE_v_0,0, SENSE_v_0,1, SENSE_v_0,2, SENSE_v_0,3, SENSE_v_0,4, SENSE_v_0,5, SENSE_v_0,6], [SENSE_v_1,0, SENSE_v_1,1, SENSE_v_1,2, SENSE_v_1,3, SENSE_v_1,4, SENSE_v_1,5, SENSE_v_1,6], [SENSE_v_2,0, SENSE_v_2,1, SENSE_v_2,2, SENSE_v_2,3, SENSE_v_2,4, SENSE_v_2,5, SENSE_v_2,6]]]\n", "[[[EMMA_h_0,0, EMMA_h_0,1, EMMA_h_0,2, EMMA_h_0,3], [EMMA_h_1,0, EMMA_h_1,1, EMMA_h_1,2, EMMA_h_1,3], [EMMA_h_2,0, EMMA_h_2,1, EMMA_h_2,2, EMMA_h_2,3], [EMMA_h_3,0, EMMA_h_3,1, EMMA_h_3,2, EMMA_h_3,3], [EMMA_h_4,0, EMMA_h_4,1, EMMA_h_4,2, EMMA_h_4,3], [EMMA_h_5,0, EMMA_h_5,1, EMMA_h_5,2, EMMA_h_5,3], [EMMA_h_6,0, EMMA_h_6,1, EMMA_h_6,2, EMMA_h_6,3]], [[EMMA_v_0,0, EMMA_v_0,1, EMMA_v_0,2, EMMA_v_0,3, EMMA_v_0,4, EMMA_v_0,5, EMMA_v_0,6], [EMMA_v_1,0, EMMA_v_1,1, EMMA_v_1,2, EMMA_v_1,3, EMMA_v_1,4, EMMA_v_1,5, EMMA_v_1,6], [EMMA_v_2,0, EMMA_v_2,1, EMMA_v_2,2, EMMA_v_2,3, EMMA_v_2,4, EMMA_v_2,5, EMMA_v_2,6], [EMMA_v_3,0, EMMA_v_3,1, EMMA_v_3,2, EMMA_v_3,3, EMMA_v_3,4, EMMA_v_3,5, EMMA_v_3,6]]]\n", "[[[ESTATE_h_0,0, ESTATE_h_0,1], [ESTATE_h_1,0, ESTATE_h_1,1], [ESTATE_h_2,0, ESTATE_h_2,1], [ESTATE_h_3,0, ESTATE_h_3,1], [ESTATE_h_4,0, ESTATE_h_4,1], [ESTATE_h_5,0, ESTATE_h_5,1], [ESTATE_h_6,0, ESTATE_h_6,1]], [[ESTATE_v_0,0, ESTATE_v_0,1, ESTATE_v_0,2, ESTATE_v_0,3, ESTATE_v_0,4, ESTATE_v_0,5, ESTATE_v_0,6], [ESTATE_v_1,0, ESTATE_v_1,1, ESTATE_v_1,2, ESTATE_v_1,3, ESTATE_v_1,4, ESTATE_v_1,5, ESTATE_v_1,6]]]\n", "[[[BENNET_h_0,0, BENNET_h_0,1], [BENNET_h_1,0, BENNET_h_1,1], [BENNET_h_2,0, BENNET_h_2,1], [BENNET_h_3,0, BENNET_h_3,1], [BENNET_h_4,0, BENNET_h_4,1], [BENNET_h_5,0, BENNET_h_5,1], [BENNET_h_6,0, BENNET_h_6,1]], [[BENNET_v_0,0, BENNET_v_0,1, BENNET_v_0,2, BENNET_v_0,3, BENNET_v_0,4, BENNET_v_0,5, BENNET_v_0,6], [BENNET_v_1,0, BENNET_v_1,1, BENNET_v_1,2, BENNET_v_1,3, BENNET_v_1,4, BENNET_v_1,5, BENNET_v_1,6]]]\n", "[[[BATH_h_0,0, BATH_h_0,1, BATH_h_0,2, BATH_h_0,3], [BATH_h_1,0, BATH_h_1,1, BATH_h_1,2, BATH_h_1,3], [BATH_h_2,0, BATH_h_2,1, BATH_h_2,2, BATH_h_2,3], [BATH_h_3,0, BATH_h_3,1, BATH_h_3,2, BATH_h_3,3], [BATH_h_4,0, BATH_h_4,1, BATH_h_4,2, BATH_h_4,3], [BATH_h_5,0, BATH_h_5,1, BATH_h_5,2, BATH_h_5,3], [BATH_h_6,0, BATH_h_6,1, BATH_h_6,2, BATH_h_6,3]], [[BATH_v_0,0, BATH_v_0,1, BATH_v_0,2, BATH_v_0,3, BATH_v_0,4, BATH_v_0,5, BATH_v_0,6], [BATH_v_1,0, BATH_v_1,1, BATH_v_1,2, BATH_v_1,3, BATH_v_1,4, BATH_v_1,5, BATH_v_1,6], [BATH_v_2,0, BATH_v_2,1, BATH_v_2,2, BATH_v_2,3, BATH_v_2,4, BATH_v_2,5, BATH_v_2,6], [BATH_v_3,0, BATH_v_3,1, BATH_v_3,2, BATH_v_3,3, BATH_v_3,4, BATH_v_3,5, BATH_v_3,6]]]\n", "Executed in 0.6775 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "╚═╧═╧═╧═╧═╧═╧═╝\n", "[[[JANE_h_0,0, <PERSON><PERSON><PERSON><PERSON>h_0,1, <PERSON><PERSON><PERSON><PERSON>h_0,2], [JANE_h_1,0, JANE_h_1,1, JANE_h_1,2], [JANE_h_2,0, JANE_h_2,1, JANE_h_2,2], [JANE_h_3,0, JANE_h_3,1, JANE_h_3,2], [JANE_h_4,0, JANE_h_4,1, JANE_h_4,2], [JANE_h_5,0, JANE_h_5,1, JANE_h_5,2]], [[JANE_v_0,0, JANE_v_0,1, JANE_v_0,2, JANE_v_0,3, JANE_v_0,4, JANE_v_0,5], [JANE_v_1,0, JANE_v_1,1, JANE_v_1,2, JANE_v_1,3, JANE_v_1,4, JANE_v_1,5], [JANE_v_2,0, JAN<PERSON><PERSON>v_2,1, JANE_v_2,2, <PERSON>AN<PERSON>_v_2,3, JANE_v_2,4, JAN<PERSON>_v_2,5]]]\n", "[[[AUSTEN_h_0,0], [AUSTEN_h_1,0], [AUSTEN_h_2,0], [AUSTEN_h_3,0], [AUSTEN_h_4,0], [AUSTEN_h_5,0]], [[AUSTEN_v_0,0, AUSTEN_v_0,1, AUSTEN_v_0,2, AUSTEN_v_0,3, AUSTEN_v_0,4, AUSTEN_v_0,5]]]\n", "[[[PRIDE_h_0,0, PRIDE_h_0,1], [PRIDE_h_1,0, PRIDE_h_1,1], [PRIDE_h_2,0, PRIDE_h_2,1], [PRIDE_h_3,0, PRIDE_h_3,1], [PRIDE_h_4,0, PRIDE_h_4,1], [PRIDE_h_5,0, PRIDE_h_5,1]], [[PRIDE_v_0,0, PRIDE_v_0,1, PRIDE_v_0,2, PRIDE_v_0,3, PRIDE_v_0,4, PRIDE_v_0,5], [PRIDE_v_1,0, PRIDE_v_1,1, PRIDE_v_1,2, PRIDE_v_1,3, PRIDE_v_1,4, PRIDE_v_1,5]]]\n", "[[[NOVEL_h_0,0, NOVEL_h_0,1], [NOVEL_h_1,0, NOVEL_h_1,1], [NOVEL_h_2,0, NOVEL_h_2,1], [NOVEL_h_3,0, NOVEL_h_3,1], [NOVEL_h_4,0, NOVEL_h_4,1], [NOVEL_h_5,0, NOVEL_h_5,1]], [[NOVEL_v_0,0, NOVEL_v_0,1, NOVEL_v_0,2, NOVEL_v_0,3, NOVEL_v_0,4, NOVEL_v_0,5], [NOVEL_v_1,0, NOVEL_v_1,1, NOVEL_v_1,2, NOVEL_v_1,3, NOVEL_v_1,4, NOVEL_v_1,5]]]\n", "[[[DARCY_h_0,0, DARCY_h_0,1], [DARCY_h_1,0, DARCY_h_1,1], [DARCY_h_2,0, DARCY_h_2,1], [DARCY_h_3,0, DARCY_h_3,1], [DARCY_h_4,0, DARCY_h_4,1], [DARCY_h_5,0, DARCY_h_5,1]], [[DARCY_v_0,0, DARCY_v_0,1, DARCY_v_0,2, DARCY_v_0,3, DARCY_v_0,4, DARCY_v_0,5], [DARCY_v_1,0, DARCY_v_1,1, DARCY_v_1,2, DARCY_v_1,3, DARCY_v_1,4, DARCY_v_1,5]]]\n", "[[[SENSE_h_0,0, SENSE_h_0,1], [SENSE_h_1,0, SENSE_h_1,1], [SENSE_h_2,0, SENSE_h_2,1], [SENSE_h_3,0, SENSE_h_3,1], [SENSE_h_4,0, SENSE_h_4,1], [SENSE_h_5,0, SENSE_h_5,1]], [[SENSE_v_0,0, SENSE_v_0,1, SENSE_v_0,2, SENSE_v_0,3, SENSE_v_0,4, SENSE_v_0,5], [SENSE_v_1,0, SENSE_v_1,1, SENSE_v_1,2, SENSE_v_1,3, SENSE_v_1,4, SENSE_v_1,5]]]\n", "[[[EMMA_h_0,0, EMMA_h_0,1, EMMA_h_0,2], [EMMA_h_1,0, EMMA_h_1,1, EMMA_h_1,2], [EMMA_h_2,0, EMMA_h_2,1, EMMA_h_2,2], [EMMA_h_3,0, EMMA_h_3,1, EMMA_h_3,2], [EMMA_h_4,0, EMMA_h_4,1, EMMA_h_4,2], [EMMA_h_5,0, EMMA_h_5,1, EMMA_h_5,2]], [[EMMA_v_0,0, EMMA_v_0,1, EMMA_v_0,2, EMMA_v_0,3, EMMA_v_0,4, EMMA_v_0,5], [EMMA_v_1,0, EMMA_v_1,1, EMMA_v_1,2, EMMA_v_1,3, EMMA_v_1,4, EMMA_v_1,5], [EMMA_v_2,0, EMMA_v_2,1, EMMA_v_2,2, EMMA_v_2,3, EMMA_v_2,4, EMMA_v_2,5]]]\n", "[[[ESTATE_h_0,0], [ESTATE_h_1,0], [ESTATE_h_2,0], [ESTATE_h_3,0], [ESTATE_h_4,0], [ESTATE_h_5,0]], [[ESTATE_v_0,0, ESTATE_v_0,1, ESTATE_v_0,2, ESTATE_v_0,3, ESTATE_v_0,4, ESTATE_v_0,5]]]\n", "[[[BENNET_h_0,0], [BENNET_h_1,0], [BENNET_h_2,0], [BENNET_h_3,0], [BENNET_h_4,0], [BENNET_h_5,0]], [[BENNET_v_0,0, BENNET_v_0,1, BENNET_v_0,2, BENNET_v_0,3, BENNET_v_0,4, BENNET_v_0,5]]]\n", "[[[BATH_h_0,0, BATH_h_0,1, BATH_h_0,2], [BATH_h_1,0, BATH_h_1,1, BATH_h_1,2], [BATH_h_2,0, BATH_h_2,1, BATH_h_2,2], [BATH_h_3,0, BATH_h_3,1, BATH_h_3,2], [BATH_h_4,0, BATH_h_4,1, BATH_h_4,2], [BATH_h_5,0, BATH_h_5,1, BATH_h_5,2]], [[BATH_v_0,0, BATH_v_0,1, BATH_v_0,2, BATH_v_0,3, BATH_v_0,4, BATH_v_0,5], [BATH_v_1,0, BATH_v_1,1, BATH_v_1,2, BATH_v_1,3, BATH_v_1,4, BATH_v_1,5], [BATH_v_2,0, BATH_v_2,1, BATH_v_2,2, BATH_v_2,3, BATH_v_2,4, BATH_v_2,5]]]\n", "Executed in 0.5988 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│█│█║\n", "║█│█│█│█│█│█║\n", "║█│█│█│█│█│█║\n", "║█│█│█│█│█│█║\n", "║█│█│█│█│█│█║\n", "║█│█│█│█│█│█║\n", "╚═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "markdown", "source": ["These were some random words that I chose for an example.  Here are some real words from an NY Times mini puzzle.  You can see that it recovers the minimal possible solution correctly."], "metadata": {"id": "kFQs0iZXmipH"}}, {"cell_type": "code", "source": ["words2 = ['GUM','TAB','ERA','END','IRA','MAP','TIMWALZ','ONE','ELI','COATS','POPHITS', \\\n", "          'GOT', 'PIE','BIZ','SPA','ALLSTAR','UNICORN','MEMOPAD','WAH','TEATIME']\n", "solve_crossword(words2, 7, add_constraint_set1, add_constraint_set2, add_constraint_set3, add_constraint_set4)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sDmahKMoldGi", "outputId": "959eeb9a-3633-4b86-b94b-c80eb0f0f73a"}, "execution_count": 35, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[[GUM_h_0,0, <PERSON><PERSON>_h_0,1, G<PERSON>_h_0,2, G<PERSON>_h_0,3, <PERSON><PERSON>_h_0,4], [GUM_h_1,0, GUM_h_1,1, GUM_h_1,2, G<PERSON>_h_1,3, G<PERSON>_h_1,4], [GUM_h_2,0, GUM_h_2,1, GUM_h_2,2, GUM_h_2,3, GUM_h_2,4], [GUM_h_3,0, GUM_h_3,1, GUM_h_3,2, GUM_h_3,3, GUM_h_3,4], [GUM_h_4,0, GUM_h_4,1, GUM_h_4,2, GUM_h_4,3, GUM_h_4,4], [GUM_h_5,0, GUM_h_5,1, GUM_h_5,2, GUM_h_5,3, GUM_h_5,4], [GUM_h_6,0, GUM_h_6,1, <PERSON><PERSON>_h_6,2, <PERSON><PERSON>_h_6,3, G<PERSON>_h_6,4]], [[GUM_v_0,0, G<PERSON>_v_0,1, G<PERSON>_v_0,2, GUM_v_0,3, GUM_v_0,4, GUM_v_0,5, GUM_v_0,6], [GUM_v_1,0, GUM_v_1,1, GUM_v_1,2, GUM_v_1,3, GUM_v_1,4, GUM_v_1,5, GUM_v_1,6], [GUM_v_2,0, GUM_v_2,1, GUM_v_2,2, GUM_v_2,3, GUM_v_2,4, GUM_v_2,5, GUM_v_2,6], [GUM_v_3,0, GUM_v_3,1, GUM_v_3,2, GUM_v_3,3, GUM_v_3,4, GUM_v_3,5, GUM_v_3,6], [GUM_v_4,0, GUM_v_4,1, GUM_v_4,2, GUM_v_4,3, GUM_v_4,4, GUM_v_4,5, GUM_v_4,6]]]\n", "[[[TAB_h_0,0, TAB_h_0,1, TAB_h_0,2, TAB_h_0,3, TAB_h_0,4], [TAB_h_1,0, TAB_h_1,1, TAB_h_1,2, TAB_h_1,3, TAB_h_1,4], [TAB_h_2,0, TAB_h_2,1, TAB_h_2,2, TAB_h_2,3, TAB_h_2,4], [TAB_h_3,0, TAB_h_3,1, TAB_h_3,2, TAB_h_3,3, TAB_h_3,4], [TAB_h_4,0, TAB_h_4,1, TAB_h_4,2, TAB_h_4,3, TAB_h_4,4], [TAB_h_5,0, TAB_h_5,1, TAB_h_5,2, TAB_h_5,3, TAB_h_5,4], [TAB_h_6,0, TAB_h_6,1, <PERSON><PERSON>_h_6,2, TAB_h_6,3, TAB_h_6,4]], [[TAB_v_0,0, TAB_v_0,1, TAB_v_0,2, TAB_v_0,3, TAB_v_0,4, TAB_v_0,5, TAB_v_0,6], [TAB_v_1,0, TAB_v_1,1, TAB_v_1,2, TAB_v_1,3, TAB_v_1,4, TAB_v_1,5, TAB_v_1,6], [TAB_v_2,0, TAB_v_2,1, TAB_v_2,2, TAB_v_2,3, TAB_v_2,4, TAB_v_2,5, TAB_v_2,6], [TAB_v_3,0, TAB_v_3,1, TAB_v_3,2, TAB_v_3,3, TAB_v_3,4, TAB_v_3,5, TAB_v_3,6], [TAB_v_4,0, TAB_v_4,1, TAB_v_4,2, TAB_v_4,3, TAB_v_4,4, TAB_v_4,5, TAB_v_4,6]]]\n", "[[[ERA_h_0,0, ERA_h_0,1, ERA_h_0,2, ERA_h_0,3, ERA_h_0,4], [ERA_h_1,0, ERA_h_1,1, ERA_h_1,2, ERA_h_1,3, ERA_h_1,4], [ERA_h_2,0, ERA_h_2,1, ERA_h_2,2, ERA_h_2,3, ERA_h_2,4], [ERA_h_3,0, ERA_h_3,1, ERA_h_3,2, ERA_h_3,3, ERA_h_3,4], [ERA_h_4,0, ERA_h_4,1, ERA_h_4,2, ERA_h_4,3, ERA_h_4,4], [ERA_h_5,0, ERA_h_5,1, ERA_h_5,2, ERA_h_5,3, ERA_h_5,4], [ERA_h_6,0, ERA_h_6,1, ERA_h_6,2, ERA_h_6,3, ERA_h_6,4]], [[ERA_v_0,0, ERA_v_0,1, ERA_v_0,2, ERA_v_0,3, ERA_v_0,4, ERA_v_0,5, ERA_v_0,6], [ERA_v_1,0, ERA_v_1,1, ERA_v_1,2, ERA_v_1,3, ERA_v_1,4, ERA_v_1,5, ERA_v_1,6], [ERA_v_2,0, ERA_v_2,1, ERA_v_2,2, ERA_v_2,3, ERA_v_2,4, ERA_v_2,5, ERA_v_2,6], [ERA_v_3,0, ERA_v_3,1, ERA_v_3,2, ERA_v_3,3, ERA_v_3,4, ERA_v_3,5, ERA_v_3,6], [ERA_v_4,0, ERA_v_4,1, ERA_v_4,2, ERA_v_4,3, ERA_v_4,4, ERA_v_4,5, ERA_v_4,6]]]\n", "[[[END_h_0,0, E<PERSON>_h_0,1, END_h_0,2, END_h_0,3, E<PERSON>_h_0,4], [END_h_1,0, END_h_1,1, END_h_1,2, END_h_1,3, END_h_1,4], [END_h_2,0, END_h_2,1, END_h_2,2, END_h_2,3, END_h_2,4], [END_h_3,0, END_h_3,1, END_h_3,2, END_h_3,3, END_h_3,4], [END_h_4,0, END_h_4,1, END_h_4,2, END_h_4,3, END_h_4,4], [END_h_5,0, END_h_5,1, END_h_5,2, END_h_5,3, E<PERSON>_h_5,4], [END_h_6,0, END_h_6,1, E<PERSON>_h_6,2, E<PERSON>_h_6,3, E<PERSON>_h_6,4]], [[END_v_0,0, END_v_0,1, END_v_0,2, END_v_0,3, END_v_0,4, END_v_0,5, <PERSON>ND_v_0,6], [<PERSON>ND_v_1,0, END_v_1,1, END_v_1,2, END_v_1,3, <PERSON>ND_v_1,4, <PERSON>ND_v_1,5, <PERSON>ND_v_1,6], [<PERSON>ND_v_2,0, <PERSON>ND_v_2,1, <PERSON>ND_v_2,2, <PERSON><PERSON>_v_2,3, <PERSON>ND_v_2,4, <PERSON>ND_v_2,5, END_v_2,6], [END_v_3,0, END_v_3,1, END_v_3,2, END_v_3,3, END_v_3,4, END_v_3,5, END_v_3,6], [END_v_4,0, END_v_4,1, END_v_4,2, END_v_4,3, END_v_4,4, END_v_4,5, END_v_4,6]]]\n", "[[[IRA_h_0,0, IRA_h_0,1, IRA_h_0,2, IRA_h_0,3, IRA_h_0,4], [IRA_h_1,0, IRA_h_1,1, IRA_h_1,2, IRA_h_1,3, IRA_h_1,4], [IRA_h_2,0, IRA_h_2,1, IRA_h_2,2, IRA_h_2,3, IRA_h_2,4], [IRA_h_3,0, IRA_h_3,1, IRA_h_3,2, IRA_h_3,3, IRA_h_3,4], [IRA_h_4,0, IRA_h_4,1, IRA_h_4,2, IRA_h_4,3, IRA_h_4,4], [IRA_h_5,0, IRA_h_5,1, IRA_h_5,2, IRA_h_5,3, IRA_h_5,4], [IRA_h_6,0, IRA_h_6,1, IRA_h_6,2, IRA_h_6,3, IRA_h_6,4]], [[IRA_v_0,0, IRA_v_0,1, IRA_v_0,2, IRA_v_0,3, IRA_v_0,4, IRA_v_0,5, IRA_v_0,6], [IRA_v_1,0, IRA_v_1,1, IRA_v_1,2, IRA_v_1,3, IRA_v_1,4, IRA_v_1,5, IRA_v_1,6], [IRA_v_2,0, IRA_v_2,1, IRA_v_2,2, IRA_v_2,3, IRA_v_2,4, IRA_v_2,5, IRA_v_2,6], [IRA_v_3,0, IRA_v_3,1, IRA_v_3,2, IRA_v_3,3, IRA_v_3,4, IRA_v_3,5, IRA_v_3,6], [IRA_v_4,0, IRA_v_4,1, IRA_v_4,2, IRA_v_4,3, IRA_v_4,4, IRA_v_4,5, IRA_v_4,6]]]\n", "[[[MAP_h_0,0, MAP_h_0,1, MAP_h_0,2, MAP_h_0,3, MAP_h_0,4], [MAP_h_1,0, MAP_h_1,1, MAP_h_1,2, MAP_h_1,3, MAP_h_1,4], [MAP_h_2,0, MAP_h_2,1, MAP_h_2,2, MAP_h_2,3, MAP_h_2,4], [MAP_h_3,0, MAP_h_3,1, MAP_h_3,2, MAP_h_3,3, MAP_h_3,4], [MAP_h_4,0, MAP_h_4,1, MAP_h_4,2, MAP_h_4,3, MAP_h_4,4], [MAP_h_5,0, MAP_h_5,1, MAP_h_5,2, MAP_h_5,3, MAP_h_5,4], [MAP_h_6,0, MAP_h_6,1, MAP_h_6,2, MAP_h_6,3, MAP_h_6,4]], [[MAP_v_0,0, MAP_v_0,1, MAP_v_0,2, MAP_v_0,3, MAP_v_0,4, MAP_v_0,5, MAP_v_0,6], [MAP_v_1,0, MAP_v_1,1, MAP_v_1,2, MAP_v_1,3, MAP_v_1,4, MAP_v_1,5, MAP_v_1,6], [MAP_v_2,0, MAP_v_2,1, MAP_v_2,2, MAP_v_2,3, MAP_v_2,4, MAP_v_2,5, MAP_v_2,6], [MAP_v_3,0, MAP_v_3,1, MAP_v_3,2, MAP_v_3,3, MAP_v_3,4, MAP_v_3,5, MAP_v_3,6], [MAP_v_4,0, MAP_v_4,1, MAP_v_4,2, MAP_v_4,3, MAP_v_4,4, MAP_v_4,5, MAP_v_4,6]]]\n", "[[[TIMWALZ_h_0,0], [TIMWALZ_h_1,0], [TIMWALZ_h_2,0], [TIMWALZ_h_3,0], [TIMWALZ_h_4,0], [TIMWALZ_h_5,0], [TIMWALZ_h_6,0]], [[TIMWALZ_v_0,0, TIMWALZ_v_0,1, TIMWALZ_v_0,2, TIMWALZ_v_0,3, TIMWALZ_v_0,4, TIMWALZ_v_0,5, TIMWALZ_v_0,6]]]\n", "[[[ONE_h_0,0, ONE_h_0,1, ONE_h_0,2, ONE_h_0,3, ONE_h_0,4], [ONE_h_1,0, ONE_h_1,1, ONE_h_1,2, ONE_h_1,3, ONE_h_1,4], [ONE_h_2,0, ONE_h_2,1, ONE_h_2,2, ONE_h_2,3, ONE_h_2,4], [ONE_h_3,0, ONE_h_3,1, ONE_h_3,2, ONE_h_3,3, ONE_h_3,4], [ONE_h_4,0, ONE_h_4,1, ONE_h_4,2, ONE_h_4,3, ONE_h_4,4], [ONE_h_5,0, ONE_h_5,1, ONE_h_5,2, ONE_h_5,3, ONE_h_5,4], [ONE_h_6,0, ONE_h_6,1, ONE_h_6,2, ONE_h_6,3, ONE_h_6,4]], [[ONE_v_0,0, ONE_v_0,1, ONE_v_0,2, ONE_v_0,3, ONE_v_0,4, ONE_v_0,5, ONE_v_0,6], [ONE_v_1,0, ONE_v_1,1, ONE_v_1,2, ONE_v_1,3, ONE_v_1,4, ONE_v_1,5, ONE_v_1,6], [ONE_v_2,0, ONE_v_2,1, ONE_v_2,2, ONE_v_2,3, ONE_v_2,4, ONE_v_2,5, ONE_v_2,6], [ONE_v_3,0, ONE_v_3,1, ONE_v_3,2, ONE_v_3,3, ONE_v_3,4, ONE_v_3,5, ONE_v_3,6], [ONE_v_4,0, ONE_v_4,1, ONE_v_4,2, ONE_v_4,3, ONE_v_4,4, ONE_v_4,5, ONE_v_4,6]]]\n", "[[[ELI_h_0,0, EL<PERSON>_h_0,1, EL<PERSON>_h_0,2, ELI_h_0,3, ELI_h_0,4], [ELI_h_1,0, ELI_h_1,1, ELI_h_1,2, ELI_h_1,3, ELI_h_1,4], [ELI_h_2,0, ELI_h_2,1, ELI_h_2,2, ELI_h_2,3, ELI_h_2,4], [ELI_h_3,0, ELI_h_3,1, ELI_h_3,2, ELI_h_3,3, ELI_h_3,4], [ELI_h_4,0, ELI_h_4,1, ELI_h_4,2, ELI_h_4,3, ELI_h_4,4], [ELI_h_5,0, ELI_h_5,1, ELI_h_5,2, ELI_h_5,3, ELI_h_5,4], [ELI_h_6,0, <PERSON><PERSON><PERSON>_h_6,1, ELI_h_6,2, ELI_h_6,3, ELI_h_6,4]], [[ELI_v_0,0, ELI_v_0,1, ELI_v_0,2, ELI_v_0,3, ELI_v_0,4, ELI_v_0,5, ELI_v_0,6], [ELI_v_1,0, ELI_v_1,1, ELI_v_1,2, ELI_v_1,3, ELI_v_1,4, ELI_v_1,5, ELI_v_1,6], [ELI_v_2,0, ELI_v_2,1, ELI_v_2,2, ELI_v_2,3, ELI_v_2,4, ELI_v_2,5, ELI_v_2,6], [ELI_v_3,0, ELI_v_3,1, ELI_v_3,2, ELI_v_3,3, ELI_v_3,4, ELI_v_3,5, ELI_v_3,6], [ELI_v_4,0, ELI_v_4,1, ELI_v_4,2, ELI_v_4,3, ELI_v_4,4, ELI_v_4,5, ELI_v_4,6]]]\n", "[[[COATS_h_0,0, COATS_h_0,1, COATS_h_0,2], [COATS_h_1,0, COATS_h_1,1, COATS_h_1,2], [COATS_h_2,0, COATS_h_2,1, COATS_h_2,2], [COATS_h_3,0, COATS_h_3,1, COATS_h_3,2], [COATS_h_4,0, COATS_h_4,1, COATS_h_4,2], [COATS_h_5,0, COATS_h_5,1, COATS_h_5,2], [COATS_h_6,0, COATS_h_6,1, COATS_h_6,2]], [[COATS_v_0,0, COATS_v_0,1, COATS_v_0,2, COATS_v_0,3, COATS_v_0,4, COATS_v_0,5, COATS_v_0,6], [COATS_v_1,0, COATS_v_1,1, COATS_v_1,2, COATS_v_1,3, COATS_v_1,4, COATS_v_1,5, COATS_v_1,6], [COATS_v_2,0, COATS_v_2,1, COATS_v_2,2, COATS_v_2,3, COATS_v_2,4, COATS_v_2,5, COATS_v_2,6]]]\n", "[[[POPHITS_h_0,0], [POPHITS_h_1,0], [POPHITS_h_2,0], [POPHITS_h_3,0], [POPHITS_h_4,0], [POPHITS_h_5,0], [POPHITS_h_6,0]], [[POPHITS_v_0,0, POPHITS_v_0,1, POPHITS_v_0,2, POPHITS_v_0,3, POPHITS_v_0,4, POPHITS_v_0,5, POPHITS_v_0,6]]]\n", "[[[GOT_h_0,0, GOT_h_0,1, GOT_h_0,2, GOT_h_0,3, GOT_h_0,4], [GOT_h_1,0, GOT_h_1,1, GOT_h_1,2, GOT_h_1,3, GOT_h_1,4], [GOT_h_2,0, GOT_h_2,1, GOT_h_2,2, GOT_h_2,3, GOT_h_2,4], [GOT_h_3,0, GOT_h_3,1, GOT_h_3,2, GOT_h_3,3, GOT_h_3,4], [GOT_h_4,0, GOT_h_4,1, GOT_h_4,2, GOT_h_4,3, GOT_h_4,4], [GOT_h_5,0, GOT_h_5,1, GOT_h_5,2, GOT_h_5,3, GOT_h_5,4], [GOT_h_6,0, GOT_h_6,1, GOT_h_6,2, GOT_h_6,3, GOT_h_6,4]], [[GOT_v_0,0, GOT_v_0,1, GOT_v_0,2, GOT_v_0,3, GOT_v_0,4, GOT_v_0,5, GOT_v_0,6], [GOT_v_1,0, GOT_v_1,1, GOT_v_1,2, GOT_v_1,3, GOT_v_1,4, GOT_v_1,5, GOT_v_1,6], [GOT_v_2,0, GOT_v_2,1, GOT_v_2,2, GOT_v_2,3, GOT_v_2,4, GOT_v_2,5, GOT_v_2,6], [GOT_v_3,0, GOT_v_3,1, GOT_v_3,2, GOT_v_3,3, GOT_v_3,4, GOT_v_3,5, GOT_v_3,6], [GOT_v_4,0, GOT_v_4,1, GOT_v_4,2, GOT_v_4,3, GOT_v_4,4, GOT_v_4,5, GOT_v_4,6]]]\n", "[[[<PERSON>IE_h_0,0, <PERSON><PERSON>_h_0,1, <PERSON><PERSON>_h_0,2, <PERSON><PERSON>_h_0,3, <PERSON><PERSON>_h_0,4], [<PERSON>IE_h_1,0, <PERSON>IE_h_1,1, <PERSON>IE_h_1,2, <PERSON>IE_h_1,3, <PERSON><PERSON>_h_1,4], [<PERSON>IE_h_2,0, <PERSON>IE_h_2,1, <PERSON>IE_h_2,2, <PERSON>IE_h_2,3, <PERSON>IE_h_2,4], [PIE_h_3,0, <PERSON><PERSON>_h_3,1, <PERSON>IE_h_3,2, <PERSON>IE_h_3,3, <PERSON>IE_h_3,4], [PIE_h_4,0, PIE_h_4,1, <PERSON>IE_h_4,2, PIE_h_4,3, PIE_h_4,4], [PIE_h_5,0, PIE_h_5,1, PIE_h_5,2, <PERSON>IE_h_5,3, <PERSON>IE_h_5,4], [<PERSON>IE_h_6,0, <PERSON>IE_h_6,1, <PERSON><PERSON>_h_6,2, <PERSON><PERSON>_h_6,3, <PERSON><PERSON>_h_6,4]], [[<PERSON>IE_v_0,0, <PERSON><PERSON>_v_0,1, <PERSON><PERSON>_v_0,2, PIE_v_0,3, <PERSON>IE_v_0,4, <PERSON>IE_v_0,5, PIE_v_0,6], [<PERSON>IE_v_1,0, <PERSON><PERSON>_v_1,1, PIE_v_1,2, PIE_v_1,3, PIE_v_1,4, PIE_v_1,5, PIE_v_1,6], [PIE_v_2,0, PIE_v_2,1, PIE_v_2,2, <PERSON><PERSON>_v_2,3, PIE_v_2,4, PIE_v_2,5, PIE_v_2,6], [PIE_v_3,0, PIE_v_3,1, PIE_v_3,2, PIE_v_3,3, PIE_v_3,4, PIE_v_3,5, PIE_v_3,6], [PIE_v_4,0, PIE_v_4,1, PIE_v_4,2, PIE_v_4,3, PIE_v_4,4, PIE_v_4,5, PIE_v_4,6]]]\n", "[[[BIZ_h_0,0, B<PERSON><PERSON>_h_0,1, <PERSON><PERSON>Z_h_0,2, BIZ_h_0,3, BIZ_h_0,4], [BIZ_h_1,0, BIZ_h_1,1, BIZ_h_1,2, BIZ_h_1,3, BIZ_h_1,4], [BIZ_h_2,0, BIZ_h_2,1, BIZ_h_2,2, BIZ_h_2,3, BIZ_h_2,4], [BIZ_h_3,0, BIZ_h_3,1, BIZ_h_3,2, BIZ_h_3,3, BIZ_h_3,4], [BIZ_h_4,0, BIZ_h_4,1, BIZ_h_4,2, BIZ_h_4,3, BIZ_h_4,4], [BIZ_h_5,0, BIZ_h_5,1, BIZ_h_5,2, BIZ_h_5,3, BIZ_h_5,4], [BIZ_h_6,0, <PERSON>IZ_h_6,1, <PERSON>IZ_h_6,2, BIZ_h_6,3, BIZ_h_6,4]], [[BIZ_v_0,0, BIZ_v_0,1, BIZ_v_0,2, BIZ_v_0,3, BIZ_v_0,4, BIZ_v_0,5, BIZ_v_0,6], [BIZ_v_1,0, BIZ_v_1,1, BIZ_v_1,2, BIZ_v_1,3, BIZ_v_1,4, BIZ_v_1,5, BIZ_v_1,6], [BIZ_v_2,0, BIZ_v_2,1, BIZ_v_2,2, BIZ_v_2,3, BIZ_v_2,4, BIZ_v_2,5, BIZ_v_2,6], [BIZ_v_3,0, BIZ_v_3,1, BIZ_v_3,2, BIZ_v_3,3, BIZ_v_3,4, BIZ_v_3,5, BIZ_v_3,6], [BIZ_v_4,0, BIZ_v_4,1, BIZ_v_4,2, BIZ_v_4,3, BIZ_v_4,4, BIZ_v_4,5, BIZ_v_4,6]]]\n", "[[[SPA_h_0,0, SPA_h_0,1, SPA_h_0,2, SPA_h_0,3, SPA_h_0,4], [SPA_h_1,0, SPA_h_1,1, SPA_h_1,2, SPA_h_1,3, SPA_h_1,4], [SPA_h_2,0, SPA_h_2,1, SPA_h_2,2, SPA_h_2,3, SPA_h_2,4], [SPA_h_3,0, SPA_h_3,1, SPA_h_3,2, SPA_h_3,3, SPA_h_3,4], [SPA_h_4,0, SPA_h_4,1, SPA_h_4,2, SPA_h_4,3, SPA_h_4,4], [SPA_h_5,0, SPA_h_5,1, SPA_h_5,2, SPA_h_5,3, SPA_h_5,4], [SPA_h_6,0, SPA_h_6,1, SPA_h_6,2, SPA_h_6,3, SPA_h_6,4]], [[SPA_v_0,0, SPA_v_0,1, SPA_v_0,2, SPA_v_0,3, SPA_v_0,4, SPA_v_0,5, SPA_v_0,6], [SPA_v_1,0, SPA_v_1,1, SPA_v_1,2, SPA_v_1,3, SPA_v_1,4, SPA_v_1,5, SPA_v_1,6], [SPA_v_2,0, SPA_v_2,1, SPA_v_2,2, SPA_v_2,3, SPA_v_2,4, SPA_v_2,5, SPA_v_2,6], [SPA_v_3,0, SPA_v_3,1, SPA_v_3,2, SPA_v_3,3, SPA_v_3,4, SPA_v_3,5, SPA_v_3,6], [SPA_v_4,0, SPA_v_4,1, SPA_v_4,2, SPA_v_4,3, SPA_v_4,4, SPA_v_4,5, SPA_v_4,6]]]\n", "[[[ALLSTAR_h_0,0], [ALLSTAR_h_1,0], [ALLSTAR_h_2,0], [ALLSTAR_h_3,0], [ALLSTAR_h_4,0], [ALLSTAR_h_5,0], [ALLSTAR_h_6,0]], [[ALLSTAR_v_0,0, ALLSTAR_v_0,1, ALLSTAR_v_0,2, ALLSTAR_v_0,3, ALLSTAR_v_0,4, ALLSTAR_v_0,5, ALLSTAR_v_0,6]]]\n", "[[[UNICORN_h_0,0], [UNICORN_h_1,0], [UNICORN_h_2,0], [UNICORN_h_3,0], [UNICORN_h_4,0], [UNICORN_h_5,0], [UNICORN_h_6,0]], [[UNICORN_v_0,0, UNICORN_v_0,1, UNICORN_v_0,2, UNICORN_v_0,3, UNICORN_v_0,4, UNICORN_v_0,5, UNICORN_v_0,6]]]\n", "[[[MEMOPAD_h_0,0], [MEMOPAD_h_1,0], [MEMOPAD_h_2,0], [MEMOPAD_h_3,0], [MEMOPAD_h_4,0], [MEMOPAD_h_5,0], [MEMOPAD_h_6,0]], [[MEMOPAD_v_0,0, MEMOPAD_v_0,1, MEMOPAD_v_0,2, MEMOPAD_v_0,3, MEMOPAD_v_0,4, MEMOPAD_v_0,5, MEMOPAD_v_0,6]]]\n", "[[[WAH_h_0,0, WAH_h_0,1, WAH_h_0,2, WAH_h_0,3, WAH_h_0,4], [WAH_h_1,0, WAH_h_1,1, WAH_h_1,2, WAH_h_1,3, WAH_h_1,4], [WAH_h_2,0, WAH_h_2,1, WAH_h_2,2, WAH_h_2,3, WAH_h_2,4], [WAH_h_3,0, WAH_h_3,1, WAH_h_3,2, WAH_h_3,3, WAH_h_3,4], [WAH_h_4,0, WAH_h_4,1, WAH_h_4,2, WAH_h_4,3, WAH_h_4,4], [WAH_h_5,0, WAH_h_5,1, WAH_h_5,2, WAH_h_5,3, WAH_h_5,4], [WAH_h_6,0, WAH_h_6,1, WAH_h_6,2, WAH_h_6,3, WAH_h_6,4]], [[WAH_v_0,0, WAH_v_0,1, WAH_v_0,2, WAH_v_0,3, WAH_v_0,4, WAH_v_0,5, WAH_v_0,6], [WAH_v_1,0, WAH_v_1,1, WAH_v_1,2, WAH_v_1,3, WAH_v_1,4, WAH_v_1,5, WAH_v_1,6], [WAH_v_2,0, WAH_v_2,1, WAH_v_2,2, WAH_v_2,3, WAH_v_2,4, WAH_v_2,5, WAH_v_2,6], [WAH_v_3,0, WAH_v_3,1, WAH_v_3,2, WAH_v_3,3, WAH_v_3,4, WAH_v_3,5, WAH_v_3,6], [WAH_v_4,0, WAH_v_4,1, WAH_v_4,2, WAH_v_4,3, WAH_v_4,4, WAH_v_4,5, WAH_v_4,6]]]\n", "[[[TEATIME_h_0,0], [TEATIME_h_1,0], [TEATIME_h_2,0], [TEATIME_h_3,0], [TEATIME_h_4,0], [TEATIME_h_5,0], [TEATIME_h_6,0]], [[TEATIME_v_0,0, TEATIME_v_0,1, TEATIME_v_0,2, TEATIME_v_0,3, TEATIME_v_0,4, TEATIME_v_0,5, TEATIME_v_0,6]]]\n", "Executed in 6.0718 seconds\n", "sat\n", "╔═╤═╤═╤═╤═╤═╤═╗\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "║█│█│█│█│█│█│█║\n", "╚═╧═╧═╧═╧═╧═╧═╝\n"]}]}, {"cell_type": "markdown", "source": ["Further work for keeners:\n", "\n", "1. Technically, we should also put in a constraint to stop two words starting in the same place.  For example, the current solver would allow the words 'EAT' and 'EATEN' to start in the same place and overlap.  Add this constraint.\n", "\n", "2. As proposed, this is fairly impractical to help make crosswords.  It would be more useful to have a superset of answers and allow the SAT solver to choose from these to make a sensible crossword.  You would then ask the SAT solver to build an crossword of size $N$ that included at least $W$ words.  Add this feature.\n"], "metadata": {"id": "c2ZiS3OgzHXW"}}]}