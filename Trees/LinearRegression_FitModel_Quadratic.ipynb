{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyOBSkQDzgifR3FuyHZMuMGd", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/Trees/LinearRegression_FitModel_Quadratic.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["<img src=\"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNzY0LjQ1IiBoZWlnaHQ9IjYzLjc1OSIgdmVyc2lvbj0iMS4xIiB2aWV3Qm94PSIwIDAgNzY0LjQ1IDYzLjc1OSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KIDxnIHRyYW5zZm9ybT0ibWF0cml4KC43MzU0OCAwIDAgLjczNTQ4IDAgMy4zODgpIiBzdHJva2Utd2lkdGg9IjEuMzU5NyI+CiAgPHJlY3QgeD0iNWUtNyIgeT0iNWUtNyIgd2lkdGg9Ijc3LjM4NyIgaGVpZ2h0PSI3Ny4zODciIHJ5PSIxMS4zNSIgZmlsbD0iIzQ0NjlkOCIgb3BhY2l0eT0iLjk4IiBzdHJva2Utd2lkdGg9IjAiLz4KICA8dGV4dCB4PSIxMC4zMzA1NjciIHk9IjY1Ljc2MTcxOSIgZmlsbD0iI2ZmZmZmZiIgZm9udC1mYW1pbHk9IkNvdXJpZXIiIGZvbnQtc2l6ZT0iOTMuNDgzcHgiIHN0cm9rZS13aWR0aD0iMS4zNTk3IiBzdHlsZT0ibGluZS1oZWlnaHQ6MS4yNSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHRzcGFuIHg9IjEwLjMzMDU2NyIgeT0iNjUuNzYxNzE5IiBmaWxsPSIjZmZmZmZmIiBmb250LWZhbWlseT0iQ291cmllciIgc3Ryb2tlLXdpZHRoPSIxLjM1OTciPkkgPC90c3Bhbj48L3RleHQ+CiAgPHJlY3QgeD0iOTYiIHk9IjVlLTciIHdpZHRoPSI3Ny4zODciIGhlaWdodD0iNzcuMzg3IiByeT0iMTEuMzUiIGZpbGw9IiM0NDY5ZDgiIG9wYWNpdHk9Ii45OCIgc3Ryb2tlLXdpZHRoPSIwIi8+CiAgPHRleHQgeD0iMTA2LjMzMDU3IiB5PSI2NS43NjE3MTkiIGZpbGw9IiNmZmZmZmYiIGZvbnQtZmFtaWx5PSJDb3VyaWVyIiBmb250LXNpemU9IjkzLjQ4M3B4IiBzdHJva2Utd2lkdGg9IjEuMzU5NyIgc3R5bGU9ImxpbmUtaGVpZ2h0OjEuMjUiIHhtbDpzcGFjZT0icHJlc2VydmUiPjx0c3BhbiB4PSIxMDYuMzMwNTciIHk9IjY1Ljc2MTcxOSIgZmlsbD0iI2ZmZmZmZiIgZm9udC1mYW1pbHk9IkNvdXJpZXIiIHN0cm9rZS13aWR0aD0iMS4zNTk3Ij5DIDwvdHNwYW4+PC90ZXh0PgogIDxyZWN0IHg9IjE4MCIgeT0iNWUtNyIgd2lkdGg9Ijc3LjM4NyIgaGVpZ2h0PSI3Ny4zODciIHJ5PSIxMS4zNSIgZmlsbD0iIzQ0NjlkOCIgb3BhY2l0eT0iLjk4IiBzdHJva2Utd2lkdGg9IjAiLz4KICA8dGV4dCB4PSIxOTAuMzMwNTciIHk9IjY1Ljc2MTcxOSIgZmlsbD0iI2ZmZmZmZiIgZm9udC1mYW1pbHk9IkNvdXJpZXIiIGZvbnQtc2l6ZT0iOTMuNDgzcHgiIHN0cm9rZS13aWR0aD0iMS4zNTk3IiBzdHlsZT0ibGluZS1oZWlnaHQ6MS4yNSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHRzcGFuIHg9IjE5MC4zMzA1NyIgeT0iNjUuNzYxNzE5IiBmaWxsPSIjZmZmZmZmIiBmb250LWZhbWlseT0iQ291cmllciIgc3Ryb2tlLXdpZHRoPSIxLjM1OTciPkwgPC90c3Bhbj48L3RleHQ+CiAgPHJlY3QgeD0iMjY0IiB5PSI1ZS03IiB3aWR0aD0iNzcuMzg3IiBoZWlnaHQ9Ijc3LjM4NyIgcnk9IjExLjM1IiBmaWxsPSIjNDQ2OWQ4IiBvcGFjaXR5PSIuOTgiIHN0cm9rZS13aWR0aD0iMCIvPgogIDx0ZXh0IHg9IjI3NC4zMzA1NyIgeT0iNjUuNzYxNzE5IiBmaWxsPSIjZmZmZmZmIiBmb250LWZhbWlseT0iQ291cmllciIgZm9udC1zaXplPSI5My40ODNweCIgc3Ryb2tlLXdpZHRoPSIxLjM1OTciIHN0eWxlPSJsaW5lLWhlaWdodDoxLjI1IiB4bWw6c3BhY2U9InByZXNlcnZlIj48dHNwYW4geD0iMjc0LjMzMDU3IiB5PSI2NS43NjE3MTkiIGZpbGw9IiNmZmZmZmYiIGZvbnQtZmFtaWx5PSJDb3VyaWVyIiBzdHJva2Utd2lkdGg9IjEuMzU5NyI+SSA8L3RzcGFuPjwvdGV4dD4KICA8cmVjdCB4PSIzNDgiIHk9IjVlLTciIHdpZHRoPSI3Ny4zODciIGhlaWdodD0iNzcuMzg3IiByeT0iMTEuMzUiIGZpbGw9IiM0NDY5ZDgiIG9wYWNpdHk9Ii45OCIgc3Ryb2tlLXdpZHRoPSIwIi8+CiAgPHRleHQgeD0iMzU4LjMzMDU3IiB5PSI2NS43NjE3MTkiIGZpbGw9IiNmZmZmZmYiIGZvbnQtZmFtaWx5PSJDb3VyaWVyIiBmb250LXNpemU9IjkzLjQ4M3B4IiBzdHJva2Utd2lkdGg9IjEuMzU5NyIgc3R5bGU9ImxpbmUtaGVpZ2h0OjEuMjUiIHhtbDpzcGFjZT0icHJlc2VydmUiPjx0c3BhbiB4PSIzNTguMzMwNTciIHk9IjY1Ljc2MTcxOSIgZmlsbD0iI2ZmZmZmZiIgZm9udC1mYW1pbHk9IkNvdXJpZXIiIHN0cm9rZS13aWR0aD0iMS4zNTk3Ij5NIDwvdHNwYW4+PC90ZXh0PgogIDxyZWN0IHg9IjQzMiIgeT0iNWUtNyIgd2lkdGg9Ijc3LjM4NyIgaGVpZ2h0PSI3Ny4zODciIHJ5PSIxMS4zNSIgZmlsbD0iIzQ0NjlkOCIgb3BhY2l0eT0iLjk4IiBzdHJva2Utd2lkdGg9IjAiLz4KICA8dGV4dCB4PSI0NDIuMzMwNTciIHk9IjY1Ljc2MTcxOSIgZmlsbD0iI2ZmZmZmZiIgZm9udC1mYW1pbHk9IkNvdXJpZXIiIGZvbnQtc2l6ZT0iOTMuNDgzcHgiIHN0cm9rZS13aWR0aD0iMS4zNTk3IiBzdHlsZT0ibGluZS1oZWlnaHQ6MS4yNSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHRzcGFuIHg9IjQ0Mi4zMzA1NyIgeT0iNjUuNzYxNzE5IiBmaWxsPSIjZmZmZmZmIiBmb250LWZhbWlseT0iQ291cmllciIgc3Ryb2tlLXdpZHRoPSIxLjM1OTciPkIgPC90c3Bhbj48L3RleHQ+CiAgPGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMiAxLjUzNzYpIj4KICAgPHJlY3QgeD0iNjI0IiB5PSItMS41Mzc2IiB3aWR0aD0iNzcuMzg3IiBoZWlnaHQ9Ijc3LjM4NyIgcnk9IjExLjM1IiBmaWxsPSIjNDQ2OWQ4IiBvcGFjaXR5PSIuOTgiIHN0cm9rZS13aWR0aD0iMCIvPgogICA8dGV4dCB4PSI2MzQuMzMwNTciIHk9IjY0LjIyNDE2NyIgZmlsbD0iI2ZmZmZmZiIgZm9udC1mYW1pbHk9IkNvdXJpZXIiIGZvbnQtc2l6ZT0iOTMuNDgzcHgiIHN0cm9rZS13aWR0aD0iMS4zNTk3IiBzdHlsZT0ibGluZS1oZWlnaHQ6MS4yNSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHRzcGFuIHg9IjYzNC4zMzA1NyIgeT0iNjQuMjI0MTY3IiBmaWxsPSIjZmZmZmZmIiBmb250LWZhbWlseT0iQ291cmllciIgc3Ryb2tlLXdpZHRoPSIxLjM1OTciPlQgPC90c3Bhbj48L3RleHQ+CiAgIDxyZWN0IHg9IjcwOCIgeT0iLTEuNTM3NiIgd2lkdGg9Ijc3LjM4NyIgaGVpZ2h0PSI3Ny4zODciIHJ5PSIxMS4zNSIgZmlsbD0iIzQ0NjlkOCIgb3BhY2l0eT0iLjk4IiBzdHJva2Utd2lkdGg9IjAiLz4KICAgPHRleHQgeD0iNzE4LjMzMDU3IiB5PSI2NC4yMjQxNjciIGZpbGw9IiNmZmZmZmYiIGZvbnQtZmFtaWx5PSJDb3VyaWVyIiBmb250LXNpemU9IjkzLjQ4M3B4IiBzdHJva2Utd2lkdGg9IjEuMzU5NyIgc3R5bGU9ImxpbmUtaGVpZ2h0OjEuMjUiIHhtbDpzcGFjZT0icHJlc2VydmUiPjx0c3BhbiB4PSI3MTguMzMwNTciIHk9IjY0LjIyNDE2NyIgZmlsbD0iI2ZmZmZmZiIgZm9udC1mYW1pbHk9IkNvdXJpZXIiIHN0cm9rZS13aWR0aD0iMS4zNTk3Ij5SIDwvdHNwYW4+PC90ZXh0PgogICA8cmVjdCB4PSI3OTIiIHk9Ii0xLjUzNzYiIHdpZHRoPSI3Ny4zODciIGhlaWdodD0iNzcuMzg3IiByeT0iMTEuMzUiIGZpbGw9IiM0NDY5ZDgiIG9wYWNpdHk9Ii45OCIgc3Ryb2tlLXdpZHRoPSIwIi8+CiAgIDx0ZXh0IHg9IjgwMi4zMzA1NyIgeT0iNjQuMjI0MTY3IiBmaWxsPSIjZmZmZmZmIiBmb250LWZhbWlseT0iQ291cmllciIgZm9udC1zaXplPSI5My40ODNweCIgc3Ryb2tlLXdpZHRoPSIxLjM1OTciIHN0eWxlPSJsaW5lLWhlaWdodDoxLjI1IiB4bWw6c3BhY2U9InByZXNlcnZlIj48dHNwYW4geD0iODAyLjMzMDU3IiB5PSI2NC4yMjQxNjciIGZpbGw9IiNmZmZmZmYiIGZvbnQtZmFtaWx5PSJDb3VyaWVyIiBzdHJva2Utd2lkdGg9IjEuMzU5NyI+RTwvdHNwYW4+PC90ZXh0PgogICA8cmVjdCB4PSI4NzYiIHk9Ii0xLjUzNzYiIHdpZHRoPSI3Ny4zODciIGhlaWdodD0iNzcuMzg3IiByeT0iMTEuMzUiIGZpbGw9IiM0NDY5ZDgiIG9wYWNpdHk9Ii45OCIgc3Ryb2tlLXdpZHRoPSIwIi8+CiAgIDx0ZXh0IHg9Ijg4Ni4zMzA1NyIgeT0iNjQuMjI0MTY3IiBmaWxsPSIjZmZmZmZmIiBmb250LWZhbWlseT0iQ291cmllciIgZm9udC1zaXplPSI5My40ODNweCIgc3Ryb2tlLXdpZHRoPSIxLjM1OTciIHN0eWxlPSJsaW5lLWhlaWdodDoxLjI1IiB4bWw6c3BhY2U9InByZXNlcnZlIj48dHNwYW4geD0iODg2LjMzMDU3IiB5PSI2NC4yMjQxNjciIGZpbGw9IiNmZmZmZmYiIGZvbnQtZmFtaWx5PSJDb3VyaWVyIiBzdHJva2Utd2lkdGg9IjEuMzU5NyI+RSA8L3RzcGFuPjwvdGV4dD4KICAgPHJlY3QgeD0iOTYwIiB5PSItMS41Mzc2IiB3aWR0aD0iNzcuMzg3IiBoZWlnaHQ9Ijc3LjM4NyIgcnk9IjExLjM1IiBmaWxsPSIjNDQ2OWQ4IiBvcGFjaXR5PSIuOTgiIHN0cm9rZS13aWR0aD0iMCIvPgogICA8dGV4dCB4PSI5NzAuMzMwNTciIHk9IjY0LjIyNDE2NyIgZmlsbD0iI2ZmZmZmZiIgZm9udC1mYW1pbHk9IkNvdXJpZXIiIGZvbnQtc2l6ZT0iOTMuNDgzcHgiIHN0cm9rZS13aWR0aD0iMS4zNTk3IiBzdHlsZT0ibGluZS1oZWlnaHQ6MS4yNSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHRzcGFuIHg9Ijk3MC4zMzA1NyIgeT0iNjQuMjI0MTY3IiBmaWxsPSIjZmZmZmZmIiBmb250LWZhbWlseT0iQ291cmllciIgc3Ryb2tlLXdpZHRoPSIxLjM1OTciPlMgPC90c3Bhbj48L3RleHQ+CiAgPC9nPgogIDxnIHRyYW5zZm9ybT0ibWF0cml4KDEuMDQ5OSAwIDAgMS4wNDk5IC0yOC4wOTIgLS4yNzI5MykiIGZpbGw9IiM0NDY5ZDgiIHN0cm9rZT0iI2ZkZmZmZiI+CiAgIDxyZWN0IHg9IjUyOCIgeT0iLTEuNTM3NiIgd2lkdGg9Ijc3LjM4NyIgaGVpZ2h0PSI3Ny4zODciIHJ5PSIxMS4zNSIgb3BhY2l0eT0iLjk4IiBzdHJva2Utd2lkdGg9IjUuMTgiLz4KICAgPGcgdHJhbnNmb3JtPSJtYXRyaXgoLjc0NTkyIDAgMCAuNzQzNjcgNTMwLjg0IDEuNjc0NCkiIHN0cm9rZS13aWR0aD0iNS4yMTYyIiBmZWF0dXJlS2V5PSJpbmxpbmVTeW1ib2xGZWF0dXJlLTAiPgogICAgPGcgZmlsbD0iIzQ0NjlkOCIgc3Ryb2tlPSIjZmRmZmZmIiBzdHJva2Utd2lkdGg9IjUuMjE2MiI+CiAgICAgPGcgZmlsbD0iIzQ0NjlkOCIgc3Ryb2tlPSIjZmRmZmZmIiBzdHJva2Utd2lkdGg9IjUuMjE2MiI+CiAgICAgIDxwYXRoIGQ9Im00Ny42NTkgODEuNDI3YzAuMzU4LTcuOTgxIDEuMzMzLTE1LjkxNyAxLjE1Mi0yMy45MTctMC4wMS0wLjQyNS0wLjU0NC0wLjg0My0wLjk0LTAuNTQtMi4zNTYgMS44MDEtNC44MTEgMy4yMTktNy42NjQgNC4xMDQtMy42NDkgMS4xMzItNy43MDMtMi4zMjgtNS44MTQtNS45ODEgMC43NTgtMS40NjYgMi4xNDYtMi43MDggMy40NDctMy42NzIgMC40NjctMC4zNDYgMC4zNTgtMS4xNzYtMC4zMTUtMS4xNjUtMy4xNTQgMC4wNTQtMTAuODM1IDEuMTQ5LTEwLjA0Mi00LjM4NiAwLjQ4MS0zLjM2NSA2LjI5LTUuNDU4IDguOTE3LTYuODQgMC4zMzMtMC4xNzUgMC40MzUtMC43MyAwLjEyNy0wLjk4MS02LjY2My01LjQzMS0zLjA2OS0xNC42NDcgNS43MzEtMTIuNzg4IDAuMjcyIDAuMDU4IDAuNTYzLTAuMDMzIDAuNzA2LTAuMjg3IDIuMjM1LTMuOTk1IDQuMjc2LTguMDYzIDcuMTA2LTExLjY4OC0wLjM1Ni0wLjE0Ny0wLjcxMi0wLjI5NC0xLjA2Ny0wLjQ0MiAwLjI5NCAzLjExNiAyLjAzNiA1LjI2OSA0LjMzNyA3LjI3MiAyLjQ1OSAyLjE0MiA3LjYzNCA0LjI3IDguMDg1IDcuODQ1IDAuNDgxIDMuODIxLTYuNTQ5IDQuMzU2LTYuMDU0IDcuNTg4IDAuMzMgMi4xNDcgMS4zNTQgMy40MjMgMy4wMjEgNC43NCAxLjA1MiAwLjgzMSAxLjk2OCAxLjQwNSAzLjAxNyAyLjMyOSAxLjgxOCAyLjAzNiAxLjU5NiA0LjIyMy0wLjY2NyA2LjU2MS0xLjQ4NiAwLjI1Mi0yLjkyNyAwLjEzOC00LjMyLTAuMzQxLTAuNTU2LTAuMTQ0LTAuOTQ1IDAuNDM1LTAuNzA2IDAuOTE4IDEuNDEyIDIuODQyIDMuMjMgNS40NDkgMy41MjkgOC43MDcgMC44MjEgOC45NjktNy4yMzcgMS43NDgtOC4xMyAwLjg3NS0wLjgxMy0wLjc5My0xLjYtMS41NjEtMi40ODYtMi4yNy0wLjYyMy0wLjQ5OC0xLjUxNCAwLjM4LTAuODg1IDAuODg0IDMuMzk5IDIuNzE3IDYuNTA3IDcuNzgyIDExLjEzMiA0LjQyIDQuMzIzLTMuMTQyLTAuNTI0LTEwLjExNC0yLjA4LTEzLjI0Ni0wLjIzNSAwLjMwNi0wLjQ3MSAwLjYxMi0wLjcwNiAwLjkxOCAzLjkgMS4wMSA4LjIzMSAwLjQ0NyA3Ljk0MS00LjQ1Mi0wLjExNy0xLjk3My0xLjI1OS0zLjY0NC0yLjgtNC43NzgtMS40NjgtMS4wODEtNi43MjktNC4yMzQtMy42OC02LjQxIDEuMjYxLTAuODk5IDIuNDUzLTEuODI2IDMuNTQ4LTIuOTI5IDIuMjk0LTIuMzExIDEuNzI2LTQuOTQtMC4zMjYtNy4xMDUtMy41MzUtMy43MzItOS45Ny01LjY4Mi0xMC41MjEtMTEuNTI1LTAuMDQ0LTAuNDctMC42OTItMC45MjEtMS4wNjctMC40NDItMS4yNjcgMS42MjItNi4yNjUgMTEuNzI0LTcuODQxIDExLjM5MS0yLjIzNC0wLjQ3Mi00LjQ4NSAwLjA2LTYuNDE4IDEuMTg2LTQuMTA1IDIuMzkxLTMuOTE5IDcuOTAzLTEuNzM4IDExLjQ0OCAwLjEyMiAwLjE5OSAxLjUxNyAyLjA4NCAxLjc4MiAxLjk0NC0xLjY4MiAwLjg4NS0zLjM1MSAxLjczNy00Ljk1MSAyLjc2OC0xLjY2NCAxLjA3Mi00LjE3NyAzLjI2Mi0zLjkwNCA1LjU0IDAuNjcxIDUuNjE5IDcuMTQ0IDQuOTAyIDExLjQwOSA0LjgyOS0wLjEwNS0wLjM4OC0wLjIxLTAuNzc2LTAuMzE1LTEuMTY1LTMuNTYgMi42MzYtOC41OCAxMS4zODEtMC41NjIgMTIuMTc0IDIuMzQgMC4yMzEgNC4yNDctMC4yNTkgNi40MjMtMS4xNDIgMC44ODMtMC4zNTggMS42OTgtMC44NDUgMi41MjUtMS4zMTEgMC43NzUtMC40MzcgMS45NzYtMi4xMjIgMi4wMDgtMC42OTIgMC4xNjYgNy4zNTctMC44NjUgMTQuNzE0LTEuMTk0IDIyLjA1Ni0wLjAzNiAwLjgwNCAxLjIxNCAwLjgwMSAxLjI1LTJlLTN6IiBmaWxsPSIjNDQ2OWQ4IiBzdHJva2U9IiNmZGZmZmYiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iNS4yMTYyIi8+CiAgICAgPC9nPgogICAgIDxnIGZpbGw9IiM0NDY5ZDgiIHN0cm9rZT0iI2ZkZmZmZiIgc3Ryb2tlLXdpZHRoPSI1LjIxNjIiPgogICAgICA8cGF0aCBkPSJtMjIuMzAxIDgzLjE1NmMtMC40NDEtNi4wMzItMS4wNzItMTIuNjE4IDAuMjY2LTE4LjU2NCAwLjEzOC0wLjYxMy0wLjU3OC0xLjA0Mi0xLjA0NS0wLjYwOC0xLjc0MyAxLjYyNS0zLjQ0MyAyLjgzMS01LjczMiAzLjYwNC02LjM0LTMuMzkzLTcuOTEzLTYuMzczLTQuNzE3LTguOTM5IDAuOTg4LTAuODU2IDIuMDM0LTEuNjMzIDMuMTM5LTIuMzI5IDAuMjg3LTAuMTkxIDAuMzk3LTAuNTQ0IDAuMjI1LTAuODU1LTAuNjU4LTEuMTc4LTEuMzkyLTIuMTYzLTIuMjUxLTMuMTkxLTQuMzk3LTUuMjY0LTAuMzgyLTkuNDE0IDQuNzU5LTEwLjg3NSAwLjI3MS0wLjA3NyAwLjQ1NS0wLjMyMiAwLjQ1OS0wLjYwMyAwLjAzNi0yLjg2NCAwLjMxMy01LjY0MiAxLjA5NC04LjQwNyAxLjg2NS02LjYwNiAxMC4yNTUtOS4xODEgMTMuMTQzLTEuNDg3IDAuMjggMC43NDggMS40ODkgMC40MjQgMS4yMDUtMC4zMzItMi41MTctNi43MDYtOS41NzQtNy42NDktMTMuOTE4LTIuMDAzLTIuMzA1IDIuOTk2LTIuNjEgNy40NjYtMi43NTkgMTEuMDg0LTAuMDM1IDAuODUtMy44MzkgMi4yNjktNC40OTYgMi42OTQtMS4wMzQgMC42NjktMi4yMTkgMi4wOTgtMi40NSAzLjMxMi0wLjgwOCA0LjIzMyAxLjEwMyA2LjA1NiAzLjUxMiA5LjMyMyAwLjQwNSAwLjU0OC01LjMyNyA1LjI1Mi01LjMxNyA3LjI3OSAwLjAxNiAzLjQ2OCAyLjQ1NSA1LjY0IDUuNjA1IDYuNjQ1IDMuNDA0IDEuMDg2IDcuMTI3LTEuOTMyIDkuMzg2LTQuMDM3LTAuMzQ5LTAuMjAzLTAuNjk3LTAuNDA1LTEuMDQ1LTAuNjA4LTEuMzY4IDYuMDc5LTAuNzYyIDEyLjczNC0wLjMxMSAxOC44OTYgMC4wNTYgMC44IDEuMzA2IDAuODA2IDEuMjQ4IDFlLTN6IiBmaWxsPSIjNDQ2OWQ4IiBzdHJva2U9IiNmZGZmZmYiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iNS4yMTYyIi8+CiAgICAgPC9nPgogICAgIDxnIGZpbGw9IiM0NDY5ZDgiIHN0cm9rZT0iI2ZkZmZmZiIgc3Ryb2tlLXdpZHRoPSI1LjIxNjIiPgogICAgICA8cGF0aCBkPSJtMjEuNDI0IDY0Ljc0MWMxLjk4MyAyLjcwNyA0Ljk4MSA0LjE5OSA4LjM0OSAzLjYzNyAzLjU5NC0wLjYgNS4xOTEtNC4xMyA1LjI5MS03LjQxMSAwLjAyNC0wLjgwNy0xLjIyNi0wLjgwNC0xLjI1IDAtMC4yMDIgNi42Ny03LjUyMyA4LjMxMy0xMS4zMSAzLjE0My0wLjQ3Mi0wLjY0My0xLjU1Ny0wLjAyLTEuMDggMC42MzF6IiBmaWxsPSIjNDQ2OWQ4IiBzdHJva2U9IiNmZGZmZmYiIHN0cm9rZS13aWR0aD0iNS4yMTYyIi8+CiAgICAgPC9nPgogICAgIDxnIGZpbGw9IiM0NDY5ZDgiIHN0cm9rZT0iI2ZkZmZmZiIgc3Ryb2tlLXdpZHRoPSI1LjIxNjIiPgogICAgICA8cGF0aCBkPSJtNzQuNjYxIDgwLjg3OGMyLjg2OS01LjQwNiAzLjI1MS0xMi4xOTEgMi42NzktMTguMTgyLTAuMDM2LTAuMzgxLTAuMzc1LTAuNzQyLTAuNzkxLTAuNjAzLTEuNDgyIDAuNDk2LTkuNjc3IDEuODQtNS42MzQtNC41NTcgMC4yNTEtMC4zOTctMC4wNzUtMC45NTItMC41NC0wLjk0LTQuOTEzIDAuMTIzLTkuMjMzLTAuOTM3LTkuNTctNi42ODMtMC4wNDctMC44MDEtMS4yOTctMC44MDYtMS4yNSAwIDAuMjAxIDMuNDI2IDEuMzc1IDUuODI4IDQuNjIyIDcuMjE0IDEuNTE0IDAuNjQ2IDMuMjc4IDAuNyA0Ljg5NCAwLjc1MS0wLjY1OC0wLjAyMS0wLjMzOCAzLjA3NC0wLjIxNiAzLjQ4OSAwLjYyNSAyLjEzIDQuMTAxIDIuNzczIDUuODk2IDIuNDY2IDIuNjA2LTAuNDQ2IDEuNTUxIDMuMjg4IDEuNDc3IDUuMTc3LTAuMTUgMy44MzMtMC44MzIgNy44Mi0yLjY0NiAxMS4yMzYtMC4zNzggMC43MTMgMC43MDEgMS4zNDUgMS4wNzkgMC42MzJ6IiBmaWxsPSIjNDQ2OWQ4IiBzdHJva2U9IiNmZGZmZmYiIHN0cm9rZS13aWR0aD0iNS4yMTYyIi8+CiAgICAgPC9nPgogICAgIDxnIGZpbGw9IiM0NDY5ZDgiIHN0cm9rZT0iI2ZkZmZmZiIgc3Ryb2tlLXdpZHRoPSI1LjIxNjIiPgogICAgICA8cGF0aCBkPSJtNzYuODgxIDYzLjI5OWMzLjM0MS0wLjYxOCA3LjQyNS0xLjM3MiA3LjQyMy01LjY3IDAtMS40NzMtMC4xNDEtMy40NjItMS40MDMtNC40ODYgMC41MjQgMC40MjUgMi43MDMtMS4yODcgMy4zODEtMS44ODUgNS4wOTctNC40OTkgMS42MDctMTIuNTg1LTQuMzAxLTEzLjg1LTAuMjIyLTAuMDQ3IDIuMjE2LTQuNSAyLjUxNS01LjE1NyAwLjgzMi0xLjgzNCAwLjYxNC0zLjYzNC04ZS0zIC01LjQ3Mi0xLjEzMy0zLjM0Ny02LjMyNy05LjA2LTEwLjE1My05LjI4My0xLjQxMS0wLjA4Mi0yLjQ0OS0wLjA3Ny0zLjUxNSAwLjg4MS0xLjIxMiAxLjA5IDAuODQyIDMuOTgtMS45NjMgMi40ODQtNC44Mi0yLjU3My01LjEyNSAyLjI1LTcuODU2IDQuODUyLTAuNTg0IDAuNTU3IDAuMzAxIDEuNDM5IDAuODg1IDAuODg0IDEuMTk5LTEuMTQzIDAuOTYxLTAuNzM2IDEuNTc0LTIuMDI2IDIuMjAyLTQuNjQxIDQuNzY4LTIuNTg5IDcuMTc4LTEuMzg4IDAuMzM0IDAuMTY3IDAuODM5IDAuMDQ3IDAuOTE4LTAuMzc0IDAuMjA4LTEuMDk4IDAuMjA1LTEuMDI1IDAuMTg2LTIuMTY5IDIuNzg3LTEuODQgNS4wODQtMS41OTYgNi44OTEgMC43MzEgMC43NDUgMC43MTUgMS40NDkgMS40NjkgMi4xMTMgMi4yNjEgNC44NzQgNS41MDcgMi4wOTcgOC44MzMtMC41MzUgMTMuOTY4LTAuMjI4IDAuNDQ1IDAuMDYgMC44OTcgMC41NCAwLjk0IDguMzY4IDAuNzQ5IDguNjg0IDExLjk4MyAwLjY5OCAxMy43NTctMC40MzIgMC4wOTYtMC42NCAwLjc1LTAuMjc2IDEuMDQ0IDQuOTkgNC4wNDYtMC4zODYgNy45NjktNC42MjIgOC43NTMtMC43OTQgMC4xNDctMC40NTggMS4zNTEgMC4zMyAxLjIwNXoiIGZpbGw9IiM0NDY5ZDgiIHN0cm9rZT0iI2ZkZmZmZiIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSI1LjIxNjIiLz4KICAgICA8L2c+CiAgICA8L2c+CiAgIDwvZz4KICA8L2c+CiA8L2c+Cjwvc3ZnPgo=\">"], "metadata": {"id": "9Qgup23vwdll"}}, {"cell_type": "markdown", "source": ["# Fitting 1D quadratic model\n", "\n", "This notebook fits the quadratic model using coordinate descent.\n", "\n", "The code is complete (i.e., there is no work for you to to), but take a look at it and run the code to see the model training.\n", "\n", "You can save a local copy of this notebook in your Google account and work through it in Colab (recommended) or you can download the notebook and run it locally using Jupy<PERSON> notebook or similar.  \n", "\n", "Contact <NAME_EMAIL> if you find any mistakes or have any suggestions."], "metadata": {"id": "uORlKyPv02ge"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bbF6SE_F0tU8"}, "outputs": [], "source": ["# Math library\n", "import numpy as np\n", "# Plotting library\n", "import matplotlib.pyplot as plt\n", "from matplotlib import cm\n", "from matplotlib.colors import ListedColormap\n", "# Time library\n", "import time\n", "# Used to update figures\n", "from IPython import display"]}, {"cell_type": "code", "source": ["# Create the same input / output data as used in the unit\n", "x = np.array([0.03, 0.19, 0.34, 0.46, 0.78, 0.81, 1.08, 1.18, 1.39, 1.60, 1.65, 1.90])\n", "y = np.array([0.67, 0.85, 1.05, 1.0, 1.40, 1.5, 1.3, 1.54, 1.55, 1.68, 1.73, 1.6 ])"], "metadata": {"id": "9fGAobBnyI7Z"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# Quadratic model\n", "\n", "Now let's consider fitting a more complex model.  The quadratic model is defined as:\n", "\n", "$$ \\textrm{f}[x] = \\phi_0+\\phi_1 x+ \\phi_2 x^2$$\n", "\n", "and has three parameters $\\phi_0, \\phi_1, \\phi_2$"], "metadata": {"id": "vkmA_5scA5mi"}}, {"cell_type": "code", "source": ["# Model definition\n", "def f_quad(x, phi0, phi1, phi2):\n", "    return phi0 + phi1 * x + phi2 * x * x\n", "\n", "# Function to calculate the loss\n", "def compute_loss_quad(x,y,f,phi0,phi1,phi2):\n", "\n", "  signed_distance = f(x,phi0,phi1,phi2)-y\n", "  loss = np.sum(signed_distance * signed_distance)\n", "\n", "  return loss"], "metadata": {"id": "O_3gRgR1AhfT"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Draw this model for some values\n", "phi0 = 1.35\n", "phi1 = 0.5\n", "phi2 = -0.2\n", "\n", "fig,ax = plt.subplots()\n", "x_plot = np.linspace(0,2,100)\n", "ax.plot(x,y,'bo')\n", "ax.plot(x_plot,f_quad(x_plot, phi0, phi1, phi2), 'r-')\n", "ax.set_xlim(0,2)\n", "ax.set_ylim(0,2)\n", "ax.set_title('Loss: {:.2f}'.format(compute_loss_quad(x,y,f_quad,phi0,phi1,phi2)))\n", "ax.set_aspect('equal', adjustable='box')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "PPA7RplHCfGC", "outputId": "2bb7dcc4-21b3-4f37-d75a-d8cfeca72a3a"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["# Fit the model\n", "\n", "We'll fit the model using a version of coordinate descent. We first choose a step size $\\alpha$ and then we alternate between updating the intercept parameter $\\phi_0$ and the slope parameters $\\phi_1$ and $\\phi_2$.  \n", "\n", "1.  Compare the loss for models with $[\\phi_0, \\phi_1, \\phi_2]$,  $[\\phi_0+\\alpha, \\phi_1,\\phi_2]$,  and $[\\phi_0-\\alpha, \\phi_1,\\phi_2]$. Update the parameters according to the set that have the minimum loss.\n", "\n", "2. Compare the loss for models with $[\\phi_0, \\phi_1, \\phi_2]$, $[\\phi_0,\\phi_1+\\alpha, \\phi_2]$, and $[\\phi_0, \\phi_1-\\alpha, \\phi_2]$.\n", "\n", "2. Compare the loss for models with $[\\phi_0, \\phi_1, \\phi_2]$, $[\\phi_0,\\phi_1, \\phi_2+\\alpha]$, and $[\\phi_0, \\phi_1, \\phi_2-\\alpha]$.\n", "\n", "We'll alternate these two steps until we cannot improve any further."], "metadata": {"id": "4BrOiVY0zTY4"}}, {"cell_type": "code", "source": ["# Utility function for plotting the three models at each stage\n", "def plot_quad(fig, ax, x,y, f_quad, phi0_1, phi1_1, phi2_1, phi0_2, phi1_2, phi2_2, phi0_3, phi1_3, phi2_3, loss1, loss2, loss3):\n", "    x_plot = np.linspace(0,2,100)\n", "    ax.clear()\n", "    ax.plot(x,y,'bo')\n", "    ax.plot(x_plot,f_quad(x_plot, phi0_1, phi1_1, phi2_1), 'r-')\n", "    ax.plot(x_plot,f_quad(x_plot, phi0_2, phi1_2, phi2_2), 'g-')\n", "    ax.plot(x_plot,f_quad(x_plot, phi0_3, phi1_3, phi2_3), 'b-')\n", "    ax.set_title('Losses: {:.2f} (red), {:.2f} (green), {:.2f} (blue)'.format(loss1, loss2, loss3))\n", "    ax.set_xlim(0,2)\n", "    ax.set_ylim(0,2)\n", "    ax.set_aspect('equal', adjustable='box')\n", "\n", "    # Show the figure and wait 0.1 sec\n", "    display.display(fig)\n", "    time.sleep(0.05)\n", "    display.clear_output(wait=True)"], "metadata": {"id": "_3d7J8CkCLQQ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Fit the quad model\n", "def fit_model_quad(x,y,f_quad,compute_loss_quad,phi0_init, phi1_init, phi2_init, alpha, n_iter):\n", "\n", "  # Create figure to display results\n", "  fig,ax = plt.subplots()\n", "\n", "  # These two variables to store the evolution of the parameters\n", "  phi0_progress = np.zeros(n_iter)\n", "  phi1_progress = np.zeros(n_iter)\n", "  phi2_progress = np.zeros(n_iter)\n", "\n", "  # Initialize the history with the provided values\n", "  phi0_progress[0] = phi0_init\n", "  phi1_progress[0] = phi1_init\n", "  phi2_progress[0] = phi2_init\n", "\n", "  # Main iteration loop\n", "  for c_iter in range(1, n_iter):\n", "    # Choose parameters for first model [phi0, phi1, phi2]\n", "    phi0_1 = phi0_progress[c_iter-1]\n", "    phi1_1 = phi1_progress[c_iter-1]\n", "    phi2_1 = phi2_progress[c_iter-1]\n", "\n", "    # Change the intercept phi0\n", "    match c_iter%3:\n", "      case 0:\n", "        # Choose parameters for second model [phi_0+alpha, phi1, phi2]\n", "        phi0_2 = phi0_progress[c_iter-1]+alpha\n", "        phi1_2 = phi1_progress[c_iter-1]\n", "        phi2_2 = phi2_progress[c_iter-1]\n", "\n", "        # Choose parameters for third model [phi_0+alpha, phi1, phi2]\n", "        phi0_3 = phi0_progress[c_iter-1]-alpha\n", "        phi1_3 = phi1_progress[c_iter-1]\n", "        phi2_3 = phi2_progress[c_iter-1]\n", "\n", "      # Change the slope phi1\n", "      case 1:\n", "        # Choose parameters for second model [phi_0, phi1+alpha]\n", "        phi0_2 = phi0_progress[c_iter-1]\n", "        phi1_2 = phi1_progress[c_iter-1]+alpha\n", "        phi2_2 = phi2_progress[c_iter-1]\n", "\n", "        # Choose parameters for third model [phi_0, phi1-alpha]\n", "        phi0_3 = phi0_progress[c_iter-1]\n", "        phi1_3 = phi1_progress[c_iter-1]-alpha\n", "        phi2_3 = phi2_progress[c_iter-1]\n", "\n", "      # Change the quadratic term phi2\n", "      case 2:\n", "        # Choose parameters for second model [phi_0, phi1+alpha]\n", "        phi0_2 = phi0_progress[c_iter-1]\n", "        phi1_2 = phi1_progress[c_iter-1]\n", "        phi2_2 = phi2_progress[c_iter-1]+alpha\n", "\n", "        # Choose parameters for third model [phi_0, phi1-alpha]\n", "        phi0_3 = phi0_progress[c_iter-1]\n", "        phi1_3 = phi1_progress[c_iter-1]\n", "        phi2_3 = phi2_progress[c_iter-1]-alpha\n", "\n", "    # Compute the loss for all three models\n", "    loss1 = compute_loss_quad(x,y,f_quad, phi0_1, phi1_1, phi2_1)\n", "    loss2 = compute_loss_quad(x,y,f_quad, phi0_2, phi1_2, phi2_2)\n", "    loss3 = compute_loss_quad(x,y,f_quad, phi0_3, phi1_3, phi2_3)\n", "\n", "    # Set the parameters to the whichever model has the lowest loss\n", "    match np.argmin(np.array([loss1, loss2, loss3]))+1:\n", "      case 1:\n", "        phi0_progress[c_iter] = phi0_1\n", "        phi1_progress[c_iter] = phi1_1\n", "        phi2_progress[c_iter] = phi2_1\n", "      case 2:\n", "        phi0_progress[c_iter] = phi0_2\n", "        phi1_progress[c_iter] = phi1_2\n", "        phi2_progress[c_iter] = phi2_2\n", "      case 3:\n", "        phi0_progress[c_iter] = phi0_3\n", "        phi1_progress[c_iter] = phi1_3\n", "        phi2_progress[c_iter] = phi2_3\n", "\n", "\n", "    # Plot the progress\n", "    plot_quad(fig, ax, x,y, f_quad,  phi0_1, phi1_1, phi2_1, phi0_2, phi1_2, phi2_2, phi0_3, phi1_3, phi2_3, loss1, loss2, loss3)\n", "\n", "  return phi0_progress, phi1_progress, phi2_progress"], "metadata": {"id": "uOvVKcj3ElEr"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Run the fitting algorithm\n", "phi0_progress, phi1_progress, phi2_progress = fit_model_quad(x,y,f_quad,compute_loss_quad,phi0_init=1.35, phi1_init=-0.55, phi2_init = 0, alpha=0.015, n_iter=275)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "7v7RpAM64XuD", "outputId": "132f0a01-a29c-4dbb-ad28-3b3b4a0fe738"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["You can see that this takes a lot longer than for the simple linear regression model, but the final loss of 0.15 is less than the best we could achieve with that model (which was around 0.20).  Adding the quadratic term which allows the function to \"bend\" improves the fit."], "metadata": {"id": "0FPMbBMlobYH"}}]}