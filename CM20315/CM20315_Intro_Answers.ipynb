{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "collapsed_sections": [], "authorship_tag": "ABX9TyMg86EZ/EnmCZeOUvVnj73U", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/CM20315_Intro_Answers.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["\n", "# **CM20315 Background Mathematics**\n", "\n", "The purpose of this Python notebook is to make sure you can use CoLab and to familiarize yourself with some of the background mathematical concepts that you are going to need to understand deep learning. <br><br> It's not meant to be difficult and it may be that you know some or all of this information already.<br><br> Maths is *NOT* a spectator sport.  You won't learn it by just listening to lectures or reading books.  It really helps to interact with it and explore yourself. <br><br> Work through the cells below, running each cell in turn.  In various places you will see the words **\"TO DO\"**. Follow the instructions at these places.  If you can't figure out what is going on or how to complete the section, feel free to ask your fellow students or the TAs in the practical session or via Moodle.  This does not count toward your final marks, but complete=ing it will help you do well in your coursework and exam and reduce your stress levels later on.\n"], "metadata": {"id": "s5zzKSOusPOB"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aUAjBbqzivMY"}, "outputs": [], "source": ["# Imports math library\n", "import numpy as np\n", "# Imports plotting library\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "source": ["**Linear functions**<br> In this course, we will be using the term *linear equation* to mean a weighted sum of inputs plus an offset. If there is just one input $x$, then this is a straight line:\n", "\n", "\\begin{equation}y=\\beta+\\omega x,\\end{equation} <br>\n", "\n", "where $\\beta$ is the y-intercept of the linear and $\\omega$ is the slope of the line. When there are two inputs $x_{1}$ and $x_{2}$, then this becomes:\n", "\n", "\\begin{equation}y=\\beta+\\omega_1 x_1 + \\omega_2 x_2.\\end{equation} <br><br>\n", "\n", "Any other functions are by definition **non-linear**.\n", "\n", "\n"], "metadata": {"id": "WV2Dl6owme2d"}}, {"cell_type": "code", "source": ["# Define a linear function with just one input, x\n", "def linear_function_1D(x,beta,omega):\n", "  # TODO -- replace the code lin below with formula for 1D linear equation\n", "  y = x\n", "  # ANSWER\n", "  y = beta + omega * x\n", "  # END_ANSWER\n", "  return y"], "metadata": {"id": "WeFK4AvTotd8"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Plot the 1D linear function \n", "\n", "# Define an array of x values from 0 to 10 with increments of 0.1\n", "# https://numpy.org/doc/stable/reference/generated/numpy.arange.html\n", "x = np.arange(0.0,10.0, 0.01) \n", "# Compute y using the function you filled in above\n", "beta = 0.0; omega = 1.0\n", "# ANSWER\n", "beta = 10.0; omega = -2.0\n", "# END_ANSWER\n", "y = linear_function_1D(x,beta,omega)\n", "\n", "# Plot this function\n", "fig, ax = plt.subplots()\n", "ax.plot(x,y,'r-')\n", "ax.set_ylim([0,10]);ax.set_xlim([0,10])\n", "ax.set_xlabel('x'); ax.set_ylabel('y')\n", "plt.show\n", "\n", "# TODO -- experiment with changing the values of beta and omega\n", "# to understand what they do.  Try to make a line\n", "# that crosses the y-axis at y=10 and the x-axis at x=5"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 301}, "id": "eimhJ8_jpmEp", "outputId": "5678a055-d865-4a3f-f55c-c2c7cae1f713"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<function matplotlib.pyplot.show(*args, **kw)>"]}, "metadata": {}, "execution_count": 51}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["Now let's investigate a 2D linear function"], "metadata": {"id": "AedfvD9dxShZ"}}, {"cell_type": "code", "source": ["# Code to draw 2D function -- read it so you know what is going on, but you don't have to change it\n", "def draw_2D_function(x1_mesh, x2_mesh, y):\n", "    fig, ax = plt.subplots()\n", "    fig.set_size_inches(7,7)\n", "    pos = ax.contourf(x1_mesh, x2_mesh, y, levels=256 ,cmap = 'hot', vmin=-10,vmax=10.0)\n", "    fig.colorbar(pos, ax=ax)\n", "    ax.set_xlabel('x1');ax.set_ylabel('x2')\n", "    levels = np.arange(-10,10,1.0)\n", "    ax.contour(x1_mesh, x2_mesh, y, levels, cmap='winter')\n", "    plt.show()"], "metadata": {"id": "57Gvkk-Ir_7b"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Define a linear function with two inputs, x1 and x2\n", "def linear_function_2D(x1,x2,beta,omega1,omega2):\n", "  # TODO -- replace the code line below with formula for 2D linear equation\n", "  y = x1\n", "  # ANSWER\n", "  y = beta + omega1 * x1 + omega2 * x2\n", "  # END_ANSWER\n", "  return y"], "metadata": {"id": "YxeNhrXMzkZR"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Plot the 2D function\n", "\n", "# Make 2D array of x and y points\n", "x1 = np.arange(0.0, 10.0, 0.1)\n", "x2 = np.arange(0.0, 10.0, 0.1)\n", "x1,x2 = np.meshgrid(x1,x2)  # https://www.geeksforgeeks.org/numpy-meshgrid-function/\n", "\n", "# Compute the 2D function for given values of omega1, omega2\n", "beta = 0.0; omega1 = 1.0; omega2 = -0.5\n", "y  = linear_function_2D(x1,x2,beta, omega1, omega2)\n", "\n", "# Draw the function.  \n", "# Color represents y value (brighter = higher value)\n", "# Black = -10 or less, White = +10 or more\n", "# 0 = mid orange\n", "# Lines are contours where value is equal\n", "draw_2D_function(x1,x2,y)\n", "\n", "# TODO\n", "# Predict what this plot will look like if you set omega_1 to zero\n", "# Change the code and see if you are right.\n", "# ANSWER: It will no longer change in x1-direction #END_ANSWER\n", "\n", "# TODO\n", "# Predict what this plot will look like if you set omega_2 to zero\n", "# Change the code and see if you are right.\n", "# ANSWER: It will no longer change in the x2-direction # END_ANSWER\n", "\n", "#TODO \n", "# Predict what this plot will look like if you set beta to -5\n", "# Change the code and see if you are correct\n", "# ANSWER: The whole plot will become darker # END_ANSWER"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 442}, "id": "rn_UBRDBysmR", "outputId": "c085ac8a-a246-428c-ab81-2b09fe7a3eed"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 504x504 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["Often we will want to compute many linear functions at the same time.  For example, we might have three inputs, $x_1$, $x_2$, and $x_3$ and want to compute two linear functions giving $y_1$ and $y_2$. Of course, we could do this by just running each equation separately,<br><br>\n", "\n", "\\begin{eqnarray}y_1 &=& \\beta_1 + \\omega_{11} x_1 + \\omega_{12} x_2 + \\omega_{13} x_3\\\\\n", "y_2 &=& \\beta_2 + \\omega_{21} x_1 + \\omega_{22} x_2 + \\omega_{23} x_3.\n", "\\end{eqnarray}<br>\n", "\n", "However, we can write it more compactly with vectors and matrices:\n", "\n", "\\begin{equation}\n", "\\begin{bmatrix} y_1\\\\ y_2 \\end{bmatrix} = \\begin{bmatrix}\\beta_{1}\\\\\\beta_{2}\\end{bmatrix}+ \\begin{bmatrix}\\omega_{11}&\\omega_{12}&\\omega_{13}\\\\\\omega_{21}&\\omega_{22}&\\omega_{23}\\end{bmatrix}\\begin{bmatrix}x_{1}\\\\x_{2}\\\\x_{3}\\end{bmatrix},\n", "\\end{equation}<br>\n", "or \n", "\n", "\\begin{equation}\n", "\\mathbf{y} = \\boldsymbol\\beta +\\boldsymbol\\Omega\\mathbf{x}.\n", "\\end{equation}\n", "\n", "for short.  Here, lowercase bold symbols are used for vectors.  Upper case bold symbols are used for matrices.\n", "\n"], "metadata": {"id": "i8tLwpls476R"}}, {"cell_type": "code", "source": ["# Define a linear function with three inputs, x1, x2, and x_3\n", "def linear_function_3D(x1,x2,x3,beta,omega1,omega2,omega3):\n", "  # TODO -- replace the code below with formula for a single 3D linear equation\n", "  y = x1\n", "  # ANSWER\n", "  y = beta + omega1 * x1 + omega2 * x2 + omega3 * x3\n", "  # END_ANSWER\n", "  return y"], "metadata": {"id": "MjHXMavh9IUz"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["Let's compute two linear equations, using both the individual equations and the vector / matrix form and check they give the same answer"], "metadata": {"id": "fGzVJQ6N-mHJ"}}, {"cell_type": "code", "source": ["# Define the parameters\n", "beta1 = 0.5; beta2 = 0.2\n", "omega11 =  -1.0 ; omega12 = 0.4; omega13 = -0.3\n", "omega21 =  0.1  ; omega22 = 0.1; omega23 = 1.2\n", "\n", "# Define the inputs\n", "x1 = 4 ; x2 =-1; x3 = 2\n", "\n", "# Compute using the individual equations\n", "y1 = linear_function_3D(x1,x2,x3,beta1,omega11,omega12,omega13)\n", "y2 = linear_function_3D(x1,x2,x3,beta2,omega21,omega22,omega23)\n", "print(\"Individual equations\")\n", "print('y1 = %3.3f\\ny2 = %3.3f'%((y1,y2)))\n", "\n", "# Define vectors and matrices\n", "beta_vec = np.array([[beta1],[beta2]])\n", "omega_mat = np.array([[omega11,omega12,omega13],[omega21,omega22,omega23]])\n", "x_vec = np.array([[x1], [x2], [x3]])\n", "\n", "# Compute with vector/matrix form\n", "y_vec = beta_vec+np.matmul(omega_mat, x_vec)\n", "print(\"Matrix/vector form\")\n", "print('y1= %3.3f\\ny2 = %3.3f'%((y_vec[0],y_vec[1])))\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Swd_bFIE9p2n", "outputId": "44f83ce2-a2d0-4d88-9b23-b554965c8697"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Individual equations\n", "y1 = -4.500\n", "y2 = 2.900\n", "Matrix/vector form\n", "y1= -4.500\n", "y2 = 2.900\n"]}]}, {"cell_type": "markdown", "source": ["(Optional, but highly recommended) Think that you have this down?  Here's some stuff to think about:<br>\n", "\n", "1.  A single linear equation with three inputs (i.e. **linear_function_3D()**) associates a value y with each point in a 3D space ($x_1$,$x_2$,$x_3$).  Is it possible to visualize this?   What value is at position (0,0,0)?\n", "\n", "2.  Write code to compute three linear equations with two inputs ($x_1$, $x_2$) using both the individual equations and the matrix form (you can make up any values for the inputs $\\beta_{i}$ and the slopes $\\omega_{ij}$."], "metadata": {"id": "3LGRoTMLU8ZU"}}, {"cell_type": "markdown", "source": ["# ANSWER\n", "Question 1:  No,  there is no easy way to visualize this -- it's a three dimensional volume with a different value at each position.  The best we would be able to do is to print out a set of slices showing the heatmap for ($x_1$,$x_2$) as in the 2D example above for a series of different values of $x_{3}$ but it's still hard to get the overall idea.   In four dimensions or higher, it becomes really impossible to visualize.  Spoiler... we will have linear equations with thousands of dimensions!  Position (0,0,0) has the value $\\beta$.  It's still the y-offset.\n", "# END_ANSWER"], "metadata": {"id": "7qdt7v8oXLSM"}}, {"cell_type": "code", "source": ["# ANSWER\n", "#Question 2:\n", "\n", "# Define the parameters\n", "beta1 = 0.5; beta2 = 0.2 ; beta3 = -0.1\n", "omega11 =  -1.0 ; omega12 = 0.4; \n", "omega21 =  0.1  ; omega22 = 0.1; \n", "omega31 = 0.4 ; omega32 = -0.7\n", "\n", "# Define the inputs\n", "x1 = 4 ; x2 =-1;\n", "\n", "# Compute using the individual equations\n", "y1 = linear_function_2D(x1,x2,beta1,omega11,omega12)\n", "y2 = linear_function_2D(x1,x2,beta2,omega21,omega22)\n", "y3 = linear_function_2D(x1,x2,beta3,omega31,omega32)\n", "print(\"Individual equations\")\n", "print('y1 = %3.3f\\ny2 = %3.3f'%((y1,y2)))\n", "\n", "# Define vectors and matrices\n", "beta_vec = np.array([[beta1],[beta2],[beta3]])\n", "omega_mat = np.array([[omega11,omega12],[omega21,omega22],[omega31,omega32]])\n", "x_vec = np.array([[x1], [x2]])\n", "\n", "# Compute with vector/matrix form\n", "y_vec = beta_vec+np.matmul(omega_mat, x_vec)\n", "print(\"Matrix/vector form\")\n", "print('y1= %3.3f\\ny2 = %3.3f'%((y_vec[0],y_vec[1])))\n", "# END_ANSWER"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1NlOoJEizWEN", "outputId": "b2d111fb-c44e-488e-b7a1-b4f98508b20f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Individual equations\n", "y1 = -3.900\n", "y2 = 0.500\n", "Matrix/vector form\n", "y1= -3.900\n", "y2 = 0.500\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "7Y5zdKtKZAB2"}, "execution_count": null, "outputs": []}]}