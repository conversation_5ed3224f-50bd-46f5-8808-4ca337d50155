{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyP2uQebA3i1KuIqkrgmngWg", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/practicals/CM20315_BackgroundMaths.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["\n", "# **CM20315 Background Mathematics**\n", "\n", "The purpose of this Python notebook is to make sure you can use CoLab and to familiarize yourself with some of the background mathematical concepts that you are going to need to understand deep learning. <br><br> It's not meant to be difficult and it may be that you know some or all of this information already.<br><br> Maths is *NOT* a spectator sport.  You won't learn it by just listening to lectures or reading books.  It really helps to interact with it and explore yourself. <br><br> Work through the cells below.  In various places you will see the words **\"TO DO\"**. Follow the instructions at these places.  If you can't figure out what is going on or how to complete the section, feel free to ask your fellow students or the TAs in the practical session or via Moodle.  This does not count toward your final marks, but completeing it will help you do well in your coursework and exam and reduce your stress levels later on.\n"], "metadata": {"id": "s5zzKSOusPOB"}}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "aUAjBbqzivMY"}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "source": ["x = np.arange\n", "\n", "x = np.arange(0.01,10, 0.01)\n", "y = np.log(x)\n", "\n", "\n", "fig, ax = plt.subplots()\n", "fig.set_size_inches(5.0,5.0, forward=True)\n", "ax.plot(x,y,'r-')\n", "ax.set_ylim([-5,3])\n", "ax.set_xlim([0,10])\n", "plt.savefig('Log.svg', format='svg')\n", "plt.show"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 342}, "id": "9WbDlaVksItZ", "outputId": "66350910-8b3d-431b-cc3d-3e81f17b4a27"}, "execution_count": 2, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<function matplotlib.pyplot.show(*args, **kw)>"]}, "metadata": {}, "execution_count": 2}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 360x360 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}]}