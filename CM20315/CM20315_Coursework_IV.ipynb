{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyNb1nfymw3lpvyBHaCFRvMI", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/udlbook/udlbook/blob/main/CM20315_Coursework_IV.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Coursework IV\n", "\n", "This coursework explores the geometry of high dimensional spaces.  It doesn't behave how you would expect and all your intuitions are wrong!  You will write code and it will give you three numerical answers that you need to type into <PERSON><PERSON><PERSON>."], "metadata": {"id": "EjLK-kA1KnYX"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4ESMmnkYEVAb"}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import scipy.special as sci"]}, {"cell_type": "markdown", "source": ["# Part (a)\n", "\n", "In part (a) of the practical, we investigate how close random points are in 2D, 100D, and 1000D.   In each case, we generate 1000 points and calculate the Euclidean distance between each pair.  You should find that in 1000D, the furthest two points are only slightly further apart than the nearest points.  Weird!"], "metadata": {"id": "MonbPEitLNgN"}}, {"cell_type": "code", "source": ["# Fix the random seed so we all have the same random numbers\n", "np.random.seed(0)\n", "n_data = 1000\n", "# Create 1000 data examples (columns) each with 2 dimensions (rows)\n", "n_dim = 2\n", "x_2D = np.random.normal(size=(n_dim,n_data))\n", "# Create 1000 data examples (columns) each with 100 dimensions (rows)\n", "n_dim = 100\n", "x_100D = np.random.normal(size=(n_dim,n_data))\n", "# Create 1000 data examples (columns) each with 1000 dimensions (rows)\n", "n_dim = 1000\n", "x_1000D = np.random.normal(size=(n_dim,n_data))\n", "\n", "# These values should be the same, otherwise your answer will be wrong\n", "# Get in touch if they are not!\n", "print('Sum of your data is %3.3f, Should be %3.3f'%(np.sum(x_1000D),1036.321))"], "metadata": {"id": "vZSHVmcWEk14"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def distance_ratio(x):\n", "  # TODO -- replace the two lines below to calculate the largest and smallest Euclidean distance between \n", "  # the data points in the columns of x.  DO NOT include the distance between the data point\n", "  # and itself (which is obviously zero)\n", "  smallest_dist = 1.0\n", "  largest_dist = 1.0\n", " \n", "  # Calculate the ratio and return\n", "  dist_ratio = largest_dist / smallest_dist\n", "  return dist_ratio"], "metadata": {"id": "PhVmnUs8ErD9"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print('Ratio of largest to smallest distance 2D: %3.3f'%(distance_ratio(x_2D)))\n", "print('Ratio of largest to smallest distance 100D: %3.3f'%(distance_ratio(x_100D)))\n", "print('Ratio of largest to smallest distance 1000D: %3.3f'%(distance_ratio(x_1000D)))\n", "print('**Note down the last of these three numbers, you will need to submit it for your coursework**')"], "metadata": {"id": "0NdPxfn5GQuJ"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# Part (b)\n", "\n", "In part (b) of the practical we calculate the volume of a hypersphere of radius 0.5 (i.e., of diameter 1) as a function of the radius.  You will find that the volume decreases to almost nothing in high dimensions.  All of the volume is in the corners of the unit hypercube (which always has volume 1). Double weird.\n", "\n", "Note that you can check your answer by doing the calculation for 2D using the standard formula for the area of a circle and making sure it matches."], "metadata": {"id": "b2FYKV1SL4Z7"}}, {"cell_type": "code", "source": ["def volume_of_hypersphere(diameter, dimensions):\n", "  # Formula iven in Problem 8.7 of the notes in <PERSON><PERSON><PERSON> (probably a different problem number on book site)\n", "  # You will need sci.special.gamma()\n", "  # Check out:    https://docs.scipy.org/doc/scipy/reference/generated/scipy.special.gamma.html\n", "  # Also use this value for pi\n", "  pi = np.pi\n", "  # TODO replace this code with formula for the volume of a hypersphere\n", "  volume = 1.0\n", "\n", "  return volume\n", "  "], "metadata": {"id": "CZoNhD8XJaHR"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["diameter = 1.0\n", "for c_dim in range(1,11):\n", "  print(\"Volume of unit diameter hypersphere in %d dimensions is %3.3f\"%(c_dim, volume_of_hypersphere(diameter, c_dim)))\n", "print('**Note down the last of these ten numbers, you will need to submit it for your coursework**')"], "metadata": {"id": "fNTBlg_GPEUh"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# Part (c)\n", "\n", "In part (c) of the coursework, you will calculate what proportion of the volume of a hypersphere is in the outer 1% of the radius/diameter.  Calculate the volume of a hypersphere and then the volume of a hypersphere with 0.99 of the radius and then figure out the proportion (a number between 0 and 1).  You'll see that by the time we get to 300 dimensions most of the volume is in the outer 1 percent.  Extremely weird!"], "metadata": {"id": "GdyMeOBmoXyF"}}, {"cell_type": "code", "source": ["def get_prop_of_volume_in_outer_1_percent(dimension):\n", "  # TODO -- replace this line\n", "  proportion = 1.0\n", "\n", "  return proportion"], "metadata": {"id": "8_CxZ2AIpQ8w"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# While we're here, let's look at how much of the volume is in the outer 1% of the radius\n", "for c_dim in [1,2,10,20,50,100,150,200,250,300]:\n", "  print('Proportion of volume in outer 1 percent of radius in %d dimensions =%3.3f'%(c_dim, get_prop_of_volume_in_outer_1_percent(c_dim)))\n", "print('**Note down the last of these ten numbers, you will need to submit it for your coursework**')"], "metadata": {"id": "LtMDIn2qPVfJ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "Qbxb-eHHQCS7"}, "execution_count": null, "outputs": []}]}