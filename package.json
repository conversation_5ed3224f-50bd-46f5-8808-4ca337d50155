{"name": "udlbook-website", "version": "0.1.0", "private": true, "homepage": "https://udlbook.github.io/udlbook", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "predeploy": "npm run build", "deploy": "gh-pages -d dist", "clean": "rm -rf node_modules dist", "format": "prettier --write ."}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.2.1", "react-router-dom": "^6.23.1", "react-scroll": "^1.8.4", "styled-components": "^6.1.11"}, "devDependencies": {"@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "gh-pages": "^6.1.1", "prettier": "^3.3.1", "prettier-plugin-organize-imports": "^3.2.4", "vite": "^5.2.12"}}