{"version": 3, "file": "9022.16842ed509ced9c32e9c.js?v=16842ed509ced9c32e9c", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,cAAc,MAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,sCAAsC,YAAY;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;;AAEA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AACA;;AAEA,qBAAqB,yCAAyC;AAC9D;AACA;AACA;AACA,0BAA0B,mBAAmB;AAC7C;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,2BAA2B;AAC3B,oBAAoB,oBAAoB;AACxC;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/go.js"], "sourcesContent": ["var keywords = {\n  \"break\":true, \"case\":true, \"chan\":true, \"const\":true, \"continue\":true,\n  \"default\":true, \"defer\":true, \"else\":true, \"fallthrough\":true, \"for\":true,\n  \"func\":true, \"go\":true, \"goto\":true, \"if\":true, \"import\":true,\n  \"interface\":true, \"map\":true, \"package\":true, \"range\":true, \"return\":true,\n  \"select\":true, \"struct\":true, \"switch\":true, \"type\":true, \"var\":true,\n  \"bool\":true, \"byte\":true, \"complex64\":true, \"complex128\":true,\n  \"float32\":true, \"float64\":true, \"int8\":true, \"int16\":true, \"int32\":true,\n  \"int64\":true, \"string\":true, \"uint8\":true, \"uint16\":true, \"uint32\":true,\n  \"uint64\":true, \"int\":true, \"uint\":true, \"uintptr\":true, \"error\": true,\n  \"rune\":true, \"any\":true, \"comparable\":true\n};\n\nvar atoms = {\n  \"true\":true, \"false\":true, \"iota\":true, \"nil\":true, \"append\":true,\n  \"cap\":true, \"close\":true, \"complex\":true, \"copy\":true, \"delete\":true, \"imag\":true,\n  \"len\":true, \"make\":true, \"new\":true, \"panic\":true, \"print\":true,\n  \"println\":true, \"real\":true, \"recover\":true\n};\n\nvar isOperatorChar = /[+\\-*&^%:=<>!|\\/]/;\n\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"' || ch == \"'\" || ch == \"`\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[\\d\\.]/.test(ch)) {\n    if (ch == \".\") {\n      stream.match(/^[0-9]+([eE][\\-+]?[0-9]+)?/);\n    } else if (ch == \"0\") {\n      stream.match(/^[xX][0-9a-fA-F]+/) || stream.match(/^0[0-7]+/);\n    } else {\n      stream.match(/^[0-9]*\\.?[0-9]*([eE][\\-+]?[0-9]+)?/);\n    }\n    return \"number\";\n  }\n  if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n  var cur = stream.current();\n  if (keywords.propertyIsEnumerable(cur)) {\n    if (cur == \"case\" || cur == \"default\") curPunc = \"case\";\n    return \"keyword\";\n  }\n  if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {end = true; break;}\n      escaped = !escaped && quote != \"`\" && next == \"\\\\\";\n    }\n    if (end || !(escaped || quote == \"`\"))\n      state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type) {\n  return state.context = new Context(state.indented, col, type, null, state.context);\n}\nfunction popContext(state) {\n  if (!state.context.prev) return;\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n// Interface\n\nexport const go = {\n  name: \"go\",\n  startState: function(indentUnit) {\n    return {\n      tokenize: null,\n      context: new Context(-indentUnit, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n      if (ctx.type == \"case\") ctx.type = \"}\";\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"case\") ctx.type = \"case\";\n    else if (curPunc == \"}\" && ctx.type == \"}\") popContext(state);\n    else if (curPunc == ctx.type) popContext(state);\n    state.startOfLine = false;\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase && state.tokenize != null) return null;\n    var ctx = state.context, firstChar = textAfter && textAfter.charAt(0);\n    if (ctx.type == \"case\" && /^(?:case|default)\\b/.test(textAfter)) return ctx.indented;\n    var closing = firstChar == ctx.type;\n    if (ctx.align) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indented + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    indentOnInput: /^\\s([{}]|case |default\\s*:)$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n\n"], "names": [], "sourceRoot": ""}