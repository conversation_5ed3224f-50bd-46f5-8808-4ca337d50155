{"version": 3, "file": "85.f5f11db2bc819f9ae970.js?v=f5f11db2bc819f9ae970", "mappings": ";;;;;;;;;;AAAA,sDAAsD,EAAE;AACxD;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEO;AACP;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/solr.js"], "sourcesContent": ["var isStringChar = /[^\\s\\|\\!\\+\\-\\*\\?\\~\\^\\&\\:\\(\\)\\[\\]\\{\\}\\\"\\\\]/;\nvar isOperatorChar = /[\\|\\!\\+\\-\\*\\?\\~\\^\\&]/;\nvar isOperatorString = /^(OR|AND|NOT|TO)$/;\n\nfunction isNumber(word) {\n  return parseFloat(word).toString() === word;\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) break;\n      escaped = !escaped && next == \"\\\\\";\n    }\n\n    if (!escaped) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction tokenOperator(operator) {\n  return function(stream, state) {\n    if (operator == \"|\")\n      stream.eat(/\\|/);\n    else if (operator == \"&\")\n      stream.eat(/\\&/);\n\n    state.tokenize = tokenBase;\n    return \"operator\";\n  };\n}\n\nfunction tokenWord(ch) {\n  return function(stream, state) {\n    var word = ch;\n    while ((ch = stream.peek()) && ch.match(isStringChar) != null) {\n      word += stream.next();\n    }\n\n    state.tokenize = tokenBase;\n    if (isOperatorString.test(word))\n      return \"operator\";\n    else if (isNumber(word))\n      return \"number\";\n    else if (stream.peek() == \":\")\n      return \"propertyName\";\n    else\n      return \"string\";\n  };\n}\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"')\n    state.tokenize = tokenString(ch);\n  else if (isOperatorChar.test(ch))\n    state.tokenize = tokenOperator(ch);\n  else if (isStringChar.test(ch))\n    state.tokenize = tokenWord(ch);\n\n  return (state.tokenize != tokenBase) ? state.tokenize(stream, state) : null;\n}\n\nexport const solr = {\n  name: \"solr\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase\n    };\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  }\n};\n"], "names": [], "sourceRoot": ""}