{"version": 3, "file": "6942.073187fa00ada10fcd06.js?v=073187fa00ada10fcd06", "mappings": ";;;;;;;;;;AAAA;AACA,cAAc;AACd,kBAAkB,kBAAkB;AACpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;;AAEA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sBAAsB;AACtB;AACA;AACA;AACA,0BAA0B,yCAAyC;AACnE;AACA;AACA,0BAA0B;AAC1B;AACA,wBAAwB;AACxB;AACA;AACA;AACA,kDAAkD;AAClD,mDAAmD;AACnD;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,2BAA2B;AAC3B,oBAAoB;AACpB;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/ttcn-cfg.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i)\n    obj[words[i]] = true;\n  return obj;\n}\n\nconst parserConfig = {\n  name: \"ttcn-cfg\",\n  keywords: words(\"Yes No LogFile FileMask ConsoleMask AppendFile\" +\n                  \" TimeStampFormat LogEventTypes SourceInfoFormat\" +\n                  \" LogEntityName LogSourceInfo DiskFullAction\" +\n                  \" LogFileNumber LogFileSize MatchingHints Detailed\" +\n                  \" Compact SubCategories Stack Single None Seconds\" +\n                  \" DateTime Time Stop Error Retry Delete TCPPort KillTimer\" +\n                  \" NumHCs UnixSocketsEnabled LocalAddress\"),\n  fileNCtrlMaskOptions: words(\"TTCN_EXECUTOR TTCN_ERROR TTCN_WARNING\" +\n                              \" TTCN_PORTEVENT TTCN_TIMEROP TTCN_VERDICTOP\" +\n                              \" TTCN_DEFAULTOP TTCN_TESTCASE TTCN_ACTION\" +\n                              \" TTCN_USER TTCN_FUNCTION TTCN_STATISTICS\" +\n                              \" TTCN_PARALLEL TTCN_MATCHING TTCN_DEBUG\" +\n                              \" EXECUTOR ERROR WARNING PORTEVENT TIMEROP\" +\n                              \" VERDICTOP DEFAULTOP TESTCASE ACTION USER\" +\n                              \" FUNCTION STATISTICS PARALLEL MATCHING DEBUG\" +\n                              \" LOG_ALL LOG_NOTHING ACTION_UNQUALIFIED\" +\n                              \" DEBUG_ENCDEC DEBUG_TESTPORT\" +\n                              \" DEBUG_UNQUALIFIED DEFAULTOP_ACTIVATE\" +\n                              \" DEFAULTOP_DEACTIVATE DEFAULTOP_EXIT\" +\n                              \" DEFAULTOP_UNQUALIFIED ERROR_UNQUALIFIED\" +\n                              \" EXECUTOR_COMPONENT EXECUTOR_CONFIGDATA\" +\n                              \" EXECUTOR_EXTCOMMAND EXECUTOR_LOGOPTIONS\" +\n                              \" EXECUTOR_RUNTIME EXECUTOR_UNQUALIFIED\" +\n                              \" FUNCTION_RND FUNCTION_UNQUALIFIED\" +\n                              \" MATCHING_DONE MATCHING_MCSUCCESS\" +\n                              \" MATCHING_MCUNSUCC MATCHING_MMSUCCESS\" +\n                              \" MATCHING_MMUNSUCC MATCHING_PCSUCCESS\" +\n                              \" MATCHING_PCUNSUCC MATCHING_PMSUCCESS\" +\n                              \" MATCHING_PMUNSUCC MATCHING_PROBLEM\" +\n                              \" MATCHING_TIMEOUT MATCHING_UNQUALIFIED\" +\n                              \" PARALLEL_PORTCONN PARALLEL_PORTMAP\" +\n                              \" PARALLEL_PTC PARALLEL_UNQUALIFIED\" +\n                              \" PORTEVENT_DUALRECV PORTEVENT_DUALSEND\" +\n                              \" PORTEVENT_MCRECV PORTEVENT_MCSEND\" +\n                              \" PORTEVENT_MMRECV PORTEVENT_MMSEND\" +\n                              \" PORTEVENT_MQUEUE PORTEVENT_PCIN\" +\n                              \" PORTEVENT_PCOUT PORTEVENT_PMIN\" +\n                              \" PORTEVENT_PMOUT PORTEVENT_PQUEUE\" +\n                              \" PORTEVENT_STATE PORTEVENT_UNQUALIFIED\" +\n                              \" STATISTICS_UNQUALIFIED STATISTICS_VERDICT\" +\n                              \" TESTCASE_FINISH TESTCASE_START\" +\n                              \" TESTCASE_UNQUALIFIED TIMEROP_GUARD\" +\n                              \" TIMEROP_READ TIMEROP_START TIMEROP_STOP\" +\n                              \" TIMEROP_TIMEOUT TIMEROP_UNQUALIFIED\" +\n                              \" USER_UNQUALIFIED VERDICTOP_FINAL\" +\n                              \" VERDICTOP_GETVERDICT VERDICTOP_SETVERDICT\" +\n                              \" VERDICTOP_UNQUALIFIED WARNING_UNQUALIFIED\"),\n  externalCommands: words(\"BeginControlPart EndControlPart BeginTestCase\" +\n                          \" EndTestCase\"),\n  multiLineStrings: true\n}\n\nvar keywords = parserConfig.keywords,\n    fileNCtrlMaskOptions = parserConfig.fileNCtrlMaskOptions,\n    externalCommands = parserConfig.externalCommands,\n    multiLineStrings = parserConfig.multiLineStrings,\n    indentStatements = parserConfig.indentStatements !== false;\nvar isOperatorChar = /[\\|]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[:=]/.test(ch)) {\n    curPunc = ch;\n    return \"punctuation\";\n  }\n  if (ch == \"#\"){\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  if (ch == \"[\"){\n    stream.eatWhile(/[\\w_\\]]/);\n    return \"number\";\n  }\n\n  stream.eatWhile(/[\\w\\$_]/);\n  var cur = stream.current();\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  if (fileNCtrlMaskOptions.propertyIsEnumerable(cur))\n    return \"atom\";\n  if (externalCommands.propertyIsEnumerable(cur)) return \"deleted\";\n\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped){\n        var afterNext = stream.peek();\n        //look if the character if the quote is like the B in '10100010'B\n        if (afterNext){\n          afterNext = afterNext.toLowerCase();\n          if(afterNext == \"b\" || afterNext == \"h\" || afterNext == \"o\")\n            stream.next();\n        }\n        end = true; break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !(escaped || multiLineStrings))\n      state.tokenize = null;\n    return \"string\";\n  };\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type) {\n  var indent = state.indented;\n  if (state.context && state.context.type == \"statement\")\n    indent = state.context.indented;\n  return state.context = new Context(indent, col, type, null, state.context);\n}\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n//Interface\nexport const ttcnCfg = {\n  name: \"ttcn\",\n  startState: function() {\n    return {\n      tokenize: null,\n      context: new Context(0, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\" || curPunc == \",\")\n        && ctx.type == \"statement\"){\n      popContext(state);\n    }\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (indentStatements && (((ctx.type == \"}\" || ctx.type == \"top\")\n                                   && curPunc != ';') || (ctx.type == \"statement\"\n                                                          && curPunc == \"newstatement\")))\n      pushContext(state, stream.column(), \"statement\");\n    state.startOfLine = false;\n    return style;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": [], "sourceRoot": ""}