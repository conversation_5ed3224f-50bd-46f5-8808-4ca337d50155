{"version": 3, "file": "5115.722cf90a473016a17ba7.js?v=722cf90a473016a17ba7", "mappings": ";;;;;;AAAa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,eAAe;AACf;;;;;;;ACJa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,2BAA2B,mBAAO,CAAC,KAA4B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,UAAU;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;ACpEa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B,GAAG,4BAA4B,GAAG,qBAAqB;AAClF,mBAAmB,mBAAO,CAAC,IAAuB;AAClD,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,wBAAwB,mBAAO,CAAC,KAA4B;AAC5D,2BAA2B,mBAAO,CAAC,KAA+B;AAClE,gBAAgB,mBAAO,CAAC,KAAW;AACnC;AACA;AACA,kCAAkC;AAClC,mCAAmC;AACnC,gCAAgC;AAChC,+BAA+B;AAC/B,kCAAkC;AAClC,gCAAgC;AAChC,wCAAwC;AACxC,yCAAyC;AACzC,qCAAqC;AACrC,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,0DAA0D;AACjG;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA,8EAA8E,4CAA4C;AAC1H,iFAAiF,6CAA6C;AAC9H;AACA,2DAA2D,uBAAuB,oBAAoB,mBAAmB,sBAAsB,oBAAoB;AACnK;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,0DAA0D,4BAA4B,KAAK;AAC5F;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,UAAU;AAC1F;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+EAA+E,UAAU;AACzF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,UAAU;AACpF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,CAAC;AACD,2BAA2B;AAC3B;;;;;;;ACzQa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,GAAG,kBAAkB,GAAG,kBAAkB;AAC7D,2BAA2B,mBAAO,CAAC,KAA+B;AAClE,wBAAwB,mBAAO,CAAC,KAA4B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,sCAAsC,kBAAkB,KAAK;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA,4EAA4E,UAAU;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE,UAAU;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;AC7Na;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,mBAAO,CAAC,KAA+B;AAC1D,cAAc,mBAAO,CAAC,KAAmC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wFAAwF,oBAAoB;AAC5G;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,qBAAqB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,iBAAiB;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,CAAC,4BAA4B;AAC7B,kBAAe;AACf;;;;;;;AC3Na;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,mBAAO,CAAC,KAA+B;AAC1D,oCAAoC,mBAAO,CAAC,KAAe;AAC3D,qCAAqC,mBAAO,CAAC,KAAgB;AAC7D,oCAAoC,mBAAO,CAAC,KAAe;AAC3D,oBAAoB,mBAAO,CAAC,KAAwB;AACpD;AACA;AACA;AACA;AACA;AACA,6BAA6B,WAAW;AACxC,6BAA6B,iBAAiB;AAC9C,6BAA6B,gBAAgB;AAC7C,6BAA6B,iBAAiB;AAC9C,6BAA6B,mCAAmC;AAChE,6BAA6B,uBAAuB;AACpD,6BAA6B,8BAA8B;AAC3D,6BAA6B,8BAA8B;AAC3D,6BAA6B,gBAAgB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA,oCAAoC,eAAe;AACnD;AACA;AACA;AACA,8BAA8B;AAC9B,gCAAgC;AAChC;AACA,mDAAmD,iEAAiE;AACpH;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,oFAAoF;AACnI;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,qFAAqF;AACpI;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,+DAA+D;AACzI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA,kBAAkB,iCAAiC;AACnD,kBAAkB,gCAAgC;AAClD,4EAA4E;AAC5E;AACA;AACA;AACA,0CAA0C,OAAO;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,2BAA2B,IAAI;AAClE;AACA,+BAA+B,wBAAwB;AACvD;AACA;AACA;AACA;AACA,qHAAqH;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA,iCAAiC;AACjC,oCAAoC;AACpC,mFAAmF;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E;AAC5E;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yHAAyH;AACzH;AACA;AACA;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,yCAAyC;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yFAAyF,sBAAsB;AAC/G,uDAAuD,WAAW;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D,sDAAsD;AACnH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD,sBAAsB;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qGAAqG;AACrG;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA,gHAAgH;AAChH;AACA;AACA;AACA,0GAA0G;AAC1G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,iBAAiB;AACjG;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,oBAAoB,IAAI;AACjD;AACA;AACA;AACA;AACA,kCAAkC;AAClC,gCAAgC;AAChC;AACA;AACA;AACA,0EAA0E,UAAU;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,8BAA8B;AAC/B,kBAAe;AACf;;;;;;;AChgBa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oCAAoC,mBAAO,CAAC,KAAe;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA,wEAAwE,gBAAgB;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAe;AACf;;;;;;;ACnIa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,aAAa,GAAG,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,cAAc;AACd;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,aAAa;AACb;;;;;;;AChEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,sBAAsB,GAAG,kBAAkB,GAAG,gBAAgB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,wBAAwB,GAAG,iBAAiB,GAAG,yBAAyB,GAAG,mBAAmB;AAC7M,kBAAkB,mBAAO,CAAC,KAAa;AACvC,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE,UAAU;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE,UAAU;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,sBAAsB;AACtB;;;;;;;AC1Pa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,GAAG,eAAe,GAAG,cAAc,GAAG,oBAAoB,GAAG,eAAe,GAAG,aAAa;AAC/G,qCAAqC,mBAAO,CAAC,KAAgB;AAC7D;AACA;AACA,8BAA8B;AAC9B,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,CAAC;AACD,aAAa;AACb;AACA;AACA,8BAA8B;AAC9B,mCAAmC;AACnC,sCAAsC;AACtC,8BAA8B;AAC9B,gCAAgC;AAChC,oCAAoC;AACpC,gCAAgC;AAChC,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,kCAAkC,KAAK;AAC5F,6EAA6E,2BAA2B;AACxG;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D,oBAAoB;AACjF;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE,UAAU;AACjF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,wCAAwC,mBAAmB,KAAK;AACjE;;;;;;;ACzTa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,OAAO;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,uBAAuB;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,KAAK,GAAG,uBAAuB,KAAK,QAAQ,OAAO;AACnF;AACA,CAAC;AACD,kBAAe;AACf;;;;;;;AC9Ca;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qCAAqC,mBAAO,CAAC,KAAgB;AAC7D,iCAAiC,mBAAO,CAAC,KAAY;AACrD,oCAAoC,mBAAO,CAAC,KAAe;AAC3D,mBAAmB,mBAAO,CAAC,KAA+B;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,UAAU;AACpF;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oGAAoG,UAAU;AAC9G;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,gBAAgB;AACxF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAe;AACf;;;;;;;ACjXa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,mBAAmB,mBAAO,CAAC,KAAyB;AACpD,uBAAuB,mBAAO,CAAC,KAAuB;AACtD,mBAAmB,mBAAO,CAAC,KAAmB;AAC9C,eAAe;AACf;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;;;;;;AChBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,mBAAmB,yBAAyB,mBAAmB;AAC1G;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,iBAAiB;AACjB;;;;;;;ACnBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,iBAAiB,GAAG,cAAc,GAAG,WAAW,GAAG,gBAAgB,GAAG,eAAe;AACvG,mBAAmB,mBAAO,CAAC,KAAc;AACzC,qBAAqB,mBAAO,CAAC,KAAgB;AAC7C,eAAe;AACf;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,cAAc;AACd;AACA,kEAAkE;AAClE;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;;;;;;;ACrca;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,2BAA2B,mBAAO,CAAC,KAAsB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA,0DAA0D,UAAU;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,mBAAmB;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;AChHa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA,UAAU;AACV,sCAAsC,gCAAgC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;;;;;;;ACvCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,GAAG,wBAAwB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,uBAAuB;AACpE,6CAA6C,oBAAoB;AACjE;AACA;AACA,qDAAqD,uBAAuB;AAC5E;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/components/version.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/HandlerList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/Configuration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/MapHandler.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/NodeUtil.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/ParseUtil.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/Stack.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/Symbol.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/SymbolMap.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/Tags.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/TexError.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/TexParser.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/mathjax.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/AsyncLoad.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/Entities.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/FunctionList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/PrioritizedList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/Retries.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.VERSION = void 0;\nexports.VERSION = '3.2.2';\n//# sourceMappingURL=version.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.HandlerList = void 0;\nvar PrioritizedList_js_1 = require(\"../util/PrioritizedList.js\");\nvar HandlerList = (function (_super) {\n    __extends(HandlerList, _super);\n    function HandlerList() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    HandlerList.prototype.register = function (handler) {\n        return this.add(handler, handler.priority);\n    };\n    HandlerList.prototype.unregister = function (handler) {\n        this.remove(handler);\n    };\n    HandlerList.prototype.handlesDocument = function (document) {\n        var e_1, _a;\n        try {\n            for (var _b = __values(this), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var item = _c.value;\n                var handler = item.item;\n                if (handler.handlesDocument(document)) {\n                    return handler;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        throw new Error(\"Can't find handler for document\");\n    };\n    HandlerList.prototype.document = function (document, options) {\n        if (options === void 0) { options = null; }\n        return this.handlesDocument(document).create(document, options);\n    };\n    return HandlerList;\n}(PrioritizedList_js_1.PrioritizedList));\nexports.HandlerList = HandlerList;\n//# sourceMappingURL=HandlerList.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ParserConfiguration = exports.ConfigurationHandler = exports.Configuration = void 0;\nvar Options_js_1 = require(\"../../util/Options.js\");\nvar MapHandler_js_1 = require(\"./MapHandler.js\");\nvar FunctionList_js_1 = require(\"../../util/FunctionList.js\");\nvar PrioritizedList_js_1 = require(\"../../util/PrioritizedList.js\");\nvar Tags_js_1 = require(\"./Tags.js\");\nvar Configuration = (function () {\n    function Configuration(name, handler, fallback, items, tags, options, nodes, preprocessors, postprocessors, initMethod, configMethod, priority, parser) {\n        if (handler === void 0) { handler = {}; }\n        if (fallback === void 0) { fallback = {}; }\n        if (items === void 0) { items = {}; }\n        if (tags === void 0) { tags = {}; }\n        if (options === void 0) { options = {}; }\n        if (nodes === void 0) { nodes = {}; }\n        if (preprocessors === void 0) { preprocessors = []; }\n        if (postprocessors === void 0) { postprocessors = []; }\n        if (initMethod === void 0) { initMethod = null; }\n        if (configMethod === void 0) { configMethod = null; }\n        this.name = name;\n        this.handler = handler;\n        this.fallback = fallback;\n        this.items = items;\n        this.tags = tags;\n        this.options = options;\n        this.nodes = nodes;\n        this.preprocessors = preprocessors;\n        this.postprocessors = postprocessors;\n        this.initMethod = initMethod;\n        this.configMethod = configMethod;\n        this.priority = priority;\n        this.parser = parser;\n        this.handler = Object.assign({ character: [], delimiter: [], macro: [], environment: [] }, handler);\n    }\n    Configuration.makeProcessor = function (func, priority) {\n        return Array.isArray(func) ? func : [func, priority];\n    };\n    Configuration._create = function (name, config) {\n        var _this = this;\n        if (config === void 0) { config = {}; }\n        var priority = config.priority || PrioritizedList_js_1.PrioritizedList.DEFAULTPRIORITY;\n        var init = config.init ? this.makeProcessor(config.init, priority) : null;\n        var conf = config.config ? this.makeProcessor(config.config, priority) : null;\n        var preprocessors = (config.preprocessors || []).map(function (pre) { return _this.makeProcessor(pre, priority); });\n        var postprocessors = (config.postprocessors || []).map(function (post) { return _this.makeProcessor(post, priority); });\n        var parser = config.parser || 'tex';\n        return new Configuration(name, config.handler || {}, config.fallback || {}, config.items || {}, config.tags || {}, config.options || {}, config.nodes || {}, preprocessors, postprocessors, init, conf, priority, parser);\n    };\n    Configuration.create = function (name, config) {\n        if (config === void 0) { config = {}; }\n        var configuration = Configuration._create(name, config);\n        ConfigurationHandler.set(name, configuration);\n        return configuration;\n    };\n    Configuration.local = function (config) {\n        if (config === void 0) { config = {}; }\n        return Configuration._create('', config);\n    };\n    Object.defineProperty(Configuration.prototype, \"init\", {\n        get: function () {\n            return this.initMethod ? this.initMethod[0] : null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Configuration.prototype, \"config\", {\n        get: function () {\n            return this.configMethod ? this.configMethod[0] : null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Configuration;\n}());\nexports.Configuration = Configuration;\nvar ConfigurationHandler;\n(function (ConfigurationHandler) {\n    var maps = new Map();\n    ConfigurationHandler.set = function (name, map) {\n        maps.set(name, map);\n    };\n    ConfigurationHandler.get = function (name) {\n        return maps.get(name);\n    };\n    ConfigurationHandler.keys = function () {\n        return maps.keys();\n    };\n})(ConfigurationHandler = exports.ConfigurationHandler || (exports.ConfigurationHandler = {}));\nvar ParserConfiguration = (function () {\n    function ParserConfiguration(packages, parsers) {\n        var e_1, _a, e_2, _b;\n        if (parsers === void 0) { parsers = ['tex']; }\n        this.initMethod = new FunctionList_js_1.FunctionList();\n        this.configMethod = new FunctionList_js_1.FunctionList();\n        this.configurations = new PrioritizedList_js_1.PrioritizedList();\n        this.parsers = [];\n        this.handlers = new MapHandler_js_1.SubHandlers();\n        this.items = {};\n        this.tags = {};\n        this.options = {};\n        this.nodes = {};\n        this.parsers = parsers;\n        try {\n            for (var _c = __values(packages.slice().reverse()), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var pkg = _d.value;\n                this.addPackage(pkg);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        try {\n            for (var _e = __values(this.configurations), _f = _e.next(); !_f.done; _f = _e.next()) {\n                var _g = _f.value, config = _g.item, priority = _g.priority;\n                this.append(config, priority);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    }\n    ParserConfiguration.prototype.init = function () {\n        this.initMethod.execute(this);\n    };\n    ParserConfiguration.prototype.config = function (jax) {\n        var e_3, _a;\n        this.configMethod.execute(this, jax);\n        try {\n            for (var _b = __values(this.configurations), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var config = _c.value;\n                this.addFilters(jax, config.item);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    ParserConfiguration.prototype.addPackage = function (pkg) {\n        var name = typeof pkg === 'string' ? pkg : pkg[0];\n        var conf = this.getPackage(name);\n        conf && this.configurations.add(conf, typeof pkg === 'string' ? conf.priority : pkg[1]);\n    };\n    ParserConfiguration.prototype.add = function (name, jax, options) {\n        var e_4, _a;\n        if (options === void 0) { options = {}; }\n        var config = this.getPackage(name);\n        this.append(config);\n        this.configurations.add(config, config.priority);\n        this.init();\n        var parser = jax.parseOptions;\n        parser.nodeFactory.setCreators(config.nodes);\n        try {\n            for (var _b = __values(Object.keys(config.items)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var kind = _c.value;\n                parser.itemFactory.setNodeClass(kind, config.items[kind]);\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        Tags_js_1.TagsFactory.addTags(config.tags);\n        (0, Options_js_1.defaultOptions)(parser.options, config.options);\n        (0, Options_js_1.userOptions)(parser.options, options);\n        this.addFilters(jax, config);\n        if (config.config) {\n            config.config(this, jax);\n        }\n    };\n    ParserConfiguration.prototype.getPackage = function (name) {\n        var config = ConfigurationHandler.get(name);\n        if (config && this.parsers.indexOf(config.parser) < 0) {\n            throw Error(\"Package \".concat(name, \" doesn't target the proper parser\"));\n        }\n        return config;\n    };\n    ParserConfiguration.prototype.append = function (config, priority) {\n        priority = priority || config.priority;\n        if (config.initMethod) {\n            this.initMethod.add(config.initMethod[0], config.initMethod[1]);\n        }\n        if (config.configMethod) {\n            this.configMethod.add(config.configMethod[0], config.configMethod[1]);\n        }\n        this.handlers.add(config.handler, config.fallback, priority);\n        Object.assign(this.items, config.items);\n        Object.assign(this.tags, config.tags);\n        (0, Options_js_1.defaultOptions)(this.options, config.options);\n        Object.assign(this.nodes, config.nodes);\n    };\n    ParserConfiguration.prototype.addFilters = function (jax, config) {\n        var e_5, _a, e_6, _b;\n        try {\n            for (var _c = __values(config.preprocessors), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var _e = __read(_d.value, 2), pre = _e[0], priority = _e[1];\n                jax.preFilters.add(pre, priority);\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n        try {\n            for (var _f = __values(config.postprocessors), _g = _f.next(); !_g.done; _g = _f.next()) {\n                var _h = __read(_g.value, 2), post = _h[0], priority = _h[1];\n                jax.postFilters.add(post, priority);\n            }\n        }\n        catch (e_6_1) { e_6 = { error: e_6_1 }; }\n        finally {\n            try {\n                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n            }\n            finally { if (e_6) throw e_6.error; }\n        }\n    };\n    return ParserConfiguration;\n}());\nexports.ParserConfiguration = ParserConfiguration;\n//# sourceMappingURL=Configuration.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SubHandlers = exports.SubHandler = exports.MapHandler = void 0;\nvar PrioritizedList_js_1 = require(\"../../util/PrioritizedList.js\");\nvar FunctionList_js_1 = require(\"../../util/FunctionList.js\");\nvar MapHandler;\n(function (MapHandler) {\n    var maps = new Map();\n    MapHandler.register = function (map) {\n        maps.set(map.name, map);\n    };\n    MapHandler.getMap = function (name) {\n        return maps.get(name);\n    };\n})(MapHandler = exports.MapHandler || (exports.MapHandler = {}));\nvar SubHandler = (function () {\n    function SubHandler() {\n        this._configuration = new PrioritizedList_js_1.PrioritizedList();\n        this._fallback = new FunctionList_js_1.FunctionList();\n    }\n    SubHandler.prototype.add = function (maps, fallback, priority) {\n        var e_1, _a;\n        if (priority === void 0) { priority = PrioritizedList_js_1.PrioritizedList.DEFAULTPRIORITY; }\n        try {\n            for (var _b = __values(maps.slice().reverse()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var name_1 = _c.value;\n                var map = MapHandler.getMap(name_1);\n                if (!map) {\n                    this.warn('Configuration ' + name_1 + ' not found! Omitted.');\n                    return;\n                }\n                this._configuration.add(map, priority);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        if (fallback) {\n            this._fallback.add(fallback, priority);\n        }\n    };\n    SubHandler.prototype.parse = function (input) {\n        var e_2, _a;\n        try {\n            for (var _b = __values(this._configuration), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var map = _c.value.item;\n                var result = map.parse(input);\n                if (result) {\n                    return result;\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        var _d = __read(input, 2), env = _d[0], symbol = _d[1];\n        Array.from(this._fallback)[0].item(env, symbol);\n    };\n    SubHandler.prototype.lookup = function (symbol) {\n        var map = this.applicable(symbol);\n        return map ? map.lookup(symbol) : null;\n    };\n    SubHandler.prototype.contains = function (symbol) {\n        return this.applicable(symbol) ? true : false;\n    };\n    SubHandler.prototype.toString = function () {\n        var e_3, _a;\n        var names = [];\n        try {\n            for (var _b = __values(this._configuration), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var map = _c.value.item;\n                names.push(map.name);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return names.join(', ');\n    };\n    SubHandler.prototype.applicable = function (symbol) {\n        var e_4, _a;\n        try {\n            for (var _b = __values(this._configuration), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var map = _c.value.item;\n                if (map.contains(symbol)) {\n                    return map;\n                }\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        return null;\n    };\n    SubHandler.prototype.retrieve = function (name) {\n        var e_5, _a;\n        try {\n            for (var _b = __values(this._configuration), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var map = _c.value.item;\n                if (map.name === name) {\n                    return map;\n                }\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n        return null;\n    };\n    SubHandler.prototype.warn = function (message) {\n        console.log('TexParser Warning: ' + message);\n    };\n    return SubHandler;\n}());\nexports.SubHandler = SubHandler;\nvar SubHandlers = (function () {\n    function SubHandlers() {\n        this.map = new Map();\n    }\n    SubHandlers.prototype.add = function (handlers, fallbacks, priority) {\n        var e_6, _a;\n        if (priority === void 0) { priority = PrioritizedList_js_1.PrioritizedList.DEFAULTPRIORITY; }\n        try {\n            for (var _b = __values(Object.keys(handlers)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var key = _c.value;\n                var name_2 = key;\n                var subHandler = this.get(name_2);\n                if (!subHandler) {\n                    subHandler = new SubHandler();\n                    this.set(name_2, subHandler);\n                }\n                subHandler.add(handlers[name_2], fallbacks[name_2], priority);\n            }\n        }\n        catch (e_6_1) { e_6 = { error: e_6_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_6) throw e_6.error; }\n        }\n    };\n    SubHandlers.prototype.set = function (name, subHandler) {\n        this.map.set(name, subHandler);\n    };\n    SubHandlers.prototype.get = function (name) {\n        return this.map.get(name);\n    };\n    SubHandlers.prototype.retrieve = function (name) {\n        var e_7, _a;\n        try {\n            for (var _b = __values(this.map.values()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var handler = _c.value;\n                var map = handler.retrieve(name);\n                if (map) {\n                    return map;\n                }\n            }\n        }\n        catch (e_7_1) { e_7 = { error: e_7_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_7) throw e_7.error; }\n        }\n        return null;\n    };\n    SubHandlers.prototype.keys = function () {\n        return this.map.keys();\n    };\n    return SubHandlers;\n}());\nexports.SubHandlers = SubHandlers;\n//# sourceMappingURL=MapHandler.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar MmlNode_js_1 = require(\"../../core/MmlTree/MmlNode.js\");\nvar mo_js_1 = require(\"../../core/MmlTree/MmlNodes/mo.js\");\nvar NodeUtil;\n(function (NodeUtil) {\n    var attrs = new Map([\n        ['autoOP', true],\n        ['fnOP', true],\n        ['movesupsub', true],\n        ['subsupOK', true],\n        ['texprimestyle', true],\n        ['useHeight', true],\n        ['variantForm', true],\n        ['withDelims', true],\n        ['mathaccent', true],\n        ['open', true],\n        ['close', true]\n    ]);\n    function createEntity(code) {\n        return String.fromCodePoint(parseInt(code, 16));\n    }\n    NodeUtil.createEntity = createEntity;\n    function getChildren(node) {\n        return node.childNodes;\n    }\n    NodeUtil.getChildren = getChildren;\n    function getText(node) {\n        return node.getText();\n    }\n    NodeUtil.getText = getText;\n    function appendChildren(node, children) {\n        var e_1, _a;\n        try {\n            for (var children_1 = __values(children), children_1_1 = children_1.next(); !children_1_1.done; children_1_1 = children_1.next()) {\n                var child = children_1_1.value;\n                node.appendChild(child);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (children_1_1 && !children_1_1.done && (_a = children_1.return)) _a.call(children_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    }\n    NodeUtil.appendChildren = appendChildren;\n    function setAttribute(node, attribute, value) {\n        node.attributes.set(attribute, value);\n    }\n    NodeUtil.setAttribute = setAttribute;\n    function setProperty(node, property, value) {\n        node.setProperty(property, value);\n    }\n    NodeUtil.setProperty = setProperty;\n    function setProperties(node, properties) {\n        var e_2, _a;\n        try {\n            for (var _b = __values(Object.keys(properties)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var name_1 = _c.value;\n                var value = properties[name_1];\n                if (name_1 === 'texClass') {\n                    node.texClass = value;\n                    node.setProperty(name_1, value);\n                }\n                else if (name_1 === 'movablelimits') {\n                    node.setProperty('movablelimits', value);\n                    if (node.isKind('mo') || node.isKind('mstyle')) {\n                        node.attributes.set('movablelimits', value);\n                    }\n                }\n                else if (name_1 === 'inferred') {\n                }\n                else if (attrs.has(name_1)) {\n                    node.setProperty(name_1, value);\n                }\n                else {\n                    node.attributes.set(name_1, value);\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    }\n    NodeUtil.setProperties = setProperties;\n    function getProperty(node, property) {\n        return node.getProperty(property);\n    }\n    NodeUtil.getProperty = getProperty;\n    function getAttribute(node, attr) {\n        return node.attributes.get(attr);\n    }\n    NodeUtil.getAttribute = getAttribute;\n    function removeProperties(node) {\n        var properties = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            properties[_i - 1] = arguments[_i];\n        }\n        node.removeProperty.apply(node, __spreadArray([], __read(properties), false));\n    }\n    NodeUtil.removeProperties = removeProperties;\n    function getChildAt(node, position) {\n        return node.childNodes[position];\n    }\n    NodeUtil.getChildAt = getChildAt;\n    function setChild(node, position, child) {\n        var children = node.childNodes;\n        children[position] = child;\n        if (child) {\n            child.parent = node;\n        }\n    }\n    NodeUtil.setChild = setChild;\n    function copyChildren(oldNode, newNode) {\n        var children = oldNode.childNodes;\n        for (var i = 0; i < children.length; i++) {\n            setChild(newNode, i, children[i]);\n        }\n    }\n    NodeUtil.copyChildren = copyChildren;\n    function copyAttributes(oldNode, newNode) {\n        newNode.attributes = oldNode.attributes;\n        setProperties(newNode, oldNode.getAllProperties());\n    }\n    NodeUtil.copyAttributes = copyAttributes;\n    function isType(node, kind) {\n        return node.isKind(kind);\n    }\n    NodeUtil.isType = isType;\n    function isEmbellished(node) {\n        return node.isEmbellished;\n    }\n    NodeUtil.isEmbellished = isEmbellished;\n    function getTexClass(node) {\n        return node.texClass;\n    }\n    NodeUtil.getTexClass = getTexClass;\n    function getCoreMO(node) {\n        return node.coreMO();\n    }\n    NodeUtil.getCoreMO = getCoreMO;\n    function isNode(item) {\n        return item instanceof MmlNode_js_1.AbstractMmlNode || item instanceof MmlNode_js_1.AbstractMmlEmptyNode;\n    }\n    NodeUtil.isNode = isNode;\n    function isInferred(node) {\n        return node.isInferred;\n    }\n    NodeUtil.isInferred = isInferred;\n    function getForm(node) {\n        var e_3, _a;\n        if (!isType(node, 'mo')) {\n            return null;\n        }\n        var mo = node;\n        var forms = mo.getForms();\n        try {\n            for (var forms_1 = __values(forms), forms_1_1 = forms_1.next(); !forms_1_1.done; forms_1_1 = forms_1.next()) {\n                var form = forms_1_1.value;\n                var symbol = mo_js_1.MmlMo.OPTABLE[form][mo.getText()];\n                if (symbol) {\n                    return symbol;\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (forms_1_1 && !forms_1_1.done && (_a = forms_1.return)) _a.call(forms_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return null;\n    }\n    NodeUtil.getForm = getForm;\n})(NodeUtil || (NodeUtil = {}));\nexports.default = NodeUtil;\n//# sourceMappingURL=NodeUtil.js.map", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar MmlNode_js_1 = require(\"../../core/MmlTree/MmlNode.js\");\nvar NodeUtil_js_1 = __importDefault(require(\"./NodeUtil.js\"));\nvar TexParser_js_1 = __importDefault(require(\"./TexParser.js\"));\nvar TexError_js_1 = __importDefault(require(\"./TexError.js\"));\nvar Entities_js_1 = require(\"../../util/Entities.js\");\nvar ParseUtil;\n(function (ParseUtil) {\n    var emPerInch = 7.2;\n    var pxPerInch = 72;\n    var UNIT_CASES = {\n        'em': function (m) { return m; },\n        'ex': function (m) { return m * .43; },\n        'pt': function (m) { return m / 10; },\n        'pc': function (m) { return m * 1.2; },\n        'px': function (m) { return m * emPerInch / pxPerInch; },\n        'in': function (m) { return m * emPerInch; },\n        'cm': function (m) { return m * emPerInch / 2.54; },\n        'mm': function (m) { return m * emPerInch / 25.4; },\n        'mu': function (m) { return m / 18; },\n    };\n    var num = '([-+]?([.,]\\\\d+|\\\\d+([.,]\\\\d*)?))';\n    var unit = '(pt|em|ex|mu|px|mm|cm|in|pc)';\n    var dimenEnd = RegExp('^\\\\s*' + num + '\\\\s*' + unit + '\\\\s*$');\n    var dimenRest = RegExp('^\\\\s*' + num + '\\\\s*' + unit + ' ?');\n    function matchDimen(dim, rest) {\n        if (rest === void 0) { rest = false; }\n        var match = dim.match(rest ? dimenRest : dimenEnd);\n        return match ?\n            muReplace([match[1].replace(/,/, '.'), match[4], match[0].length]) :\n            [null, null, 0];\n    }\n    ParseUtil.matchDimen = matchDimen;\n    function muReplace(_a) {\n        var _b = __read(_a, 3), value = _b[0], unit = _b[1], length = _b[2];\n        if (unit !== 'mu') {\n            return [value, unit, length];\n        }\n        var em = Em(UNIT_CASES[unit](parseFloat(value || '1')));\n        return [em.slice(0, -2), 'em', length];\n    }\n    function dimen2em(dim) {\n        var _a = __read(matchDimen(dim), 2), value = _a[0], unit = _a[1];\n        var m = parseFloat(value || '1');\n        var func = UNIT_CASES[unit];\n        return func ? func(m) : 0;\n    }\n    ParseUtil.dimen2em = dimen2em;\n    function Em(m) {\n        if (Math.abs(m) < .0006) {\n            return '0em';\n        }\n        return m.toFixed(3).replace(/\\.?0+$/, '') + 'em';\n    }\n    ParseUtil.Em = Em;\n    function cols() {\n        var W = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            W[_i] = arguments[_i];\n        }\n        return W.map(function (n) { return Em(n); }).join(' ');\n    }\n    ParseUtil.cols = cols;\n    function fenced(configuration, open, mml, close, big, color) {\n        if (big === void 0) { big = ''; }\n        if (color === void 0) { color = ''; }\n        var nf = configuration.nodeFactory;\n        var mrow = nf.create('node', 'mrow', [], { open: open, close: close, texClass: MmlNode_js_1.TEXCLASS.INNER });\n        var mo;\n        if (big) {\n            mo = new TexParser_js_1.default('\\\\' + big + 'l' + open, configuration.parser.stack.env, configuration).mml();\n        }\n        else {\n            var openNode = nf.create('text', open);\n            mo = nf.create('node', 'mo', [], { fence: true, stretchy: true, symmetric: true, texClass: MmlNode_js_1.TEXCLASS.OPEN }, openNode);\n        }\n        NodeUtil_js_1.default.appendChildren(mrow, [mo, mml]);\n        if (big) {\n            mo = new TexParser_js_1.default('\\\\' + big + 'r' + close, configuration.parser.stack.env, configuration).mml();\n        }\n        else {\n            var closeNode = nf.create('text', close);\n            mo = nf.create('node', 'mo', [], { fence: true, stretchy: true, symmetric: true, texClass: MmlNode_js_1.TEXCLASS.CLOSE }, closeNode);\n        }\n        color && mo.attributes.set('mathcolor', color);\n        NodeUtil_js_1.default.appendChildren(mrow, [mo]);\n        return mrow;\n    }\n    ParseUtil.fenced = fenced;\n    function fixedFence(configuration, open, mml, close) {\n        var mrow = configuration.nodeFactory.create('node', 'mrow', [], { open: open, close: close, texClass: MmlNode_js_1.TEXCLASS.ORD });\n        if (open) {\n            NodeUtil_js_1.default.appendChildren(mrow, [mathPalette(configuration, open, 'l')]);\n        }\n        if (NodeUtil_js_1.default.isType(mml, 'mrow')) {\n            NodeUtil_js_1.default.appendChildren(mrow, NodeUtil_js_1.default.getChildren(mml));\n        }\n        else {\n            NodeUtil_js_1.default.appendChildren(mrow, [mml]);\n        }\n        if (close) {\n            NodeUtil_js_1.default.appendChildren(mrow, [mathPalette(configuration, close, 'r')]);\n        }\n        return mrow;\n    }\n    ParseUtil.fixedFence = fixedFence;\n    function mathPalette(configuration, fence, side) {\n        if (fence === '{' || fence === '}') {\n            fence = '\\\\' + fence;\n        }\n        var D = '{\\\\bigg' + side + ' ' + fence + '}';\n        var T = '{\\\\big' + side + ' ' + fence + '}';\n        return new TexParser_js_1.default('\\\\mathchoice' + D + T + T + T, {}, configuration).mml();\n    }\n    ParseUtil.mathPalette = mathPalette;\n    function fixInitialMO(configuration, nodes) {\n        for (var i = 0, m = nodes.length; i < m; i++) {\n            var child = nodes[i];\n            if (child && (!NodeUtil_js_1.default.isType(child, 'mspace') &&\n                (!NodeUtil_js_1.default.isType(child, 'TeXAtom') ||\n                    (NodeUtil_js_1.default.getChildren(child)[0] &&\n                        NodeUtil_js_1.default.getChildren(NodeUtil_js_1.default.getChildren(child)[0]).length)))) {\n                if (NodeUtil_js_1.default.isEmbellished(child) ||\n                    (NodeUtil_js_1.default.isType(child, 'TeXAtom') && NodeUtil_js_1.default.getTexClass(child) === MmlNode_js_1.TEXCLASS.REL)) {\n                    var mi = configuration.nodeFactory.create('node', 'mi');\n                    nodes.unshift(mi);\n                }\n                break;\n            }\n        }\n    }\n    ParseUtil.fixInitialMO = fixInitialMO;\n    function internalMath(parser, text, level, font) {\n        if (parser.configuration.options.internalMath) {\n            return parser.configuration.options.internalMath(parser, text, level, font);\n        }\n        var mathvariant = font || parser.stack.env.font;\n        var def = (mathvariant ? { mathvariant: mathvariant } : {});\n        var mml = [], i = 0, k = 0, c, node, match = '', braces = 0;\n        if (text.match(/\\\\?[${}\\\\]|\\\\\\(|\\\\(eq)?ref\\s*\\{/)) {\n            while (i < text.length) {\n                c = text.charAt(i++);\n                if (c === '$') {\n                    if (match === '$' && braces === 0) {\n                        node = parser.create('node', 'TeXAtom', [(new TexParser_js_1.default(text.slice(k, i - 1), {}, parser.configuration)).mml()]);\n                        mml.push(node);\n                        match = '';\n                        k = i;\n                    }\n                    else if (match === '') {\n                        if (k < i - 1) {\n                            mml.push(internalText(parser, text.slice(k, i - 1), def));\n                        }\n                        match = '$';\n                        k = i;\n                    }\n                }\n                else if (c === '{' && match !== '') {\n                    braces++;\n                }\n                else if (c === '}') {\n                    if (match === '}' && braces === 0) {\n                        var atom = (new TexParser_js_1.default(text.slice(k, i), {}, parser.configuration)).mml();\n                        node = parser.create('node', 'TeXAtom', [atom], def);\n                        mml.push(node);\n                        match = '';\n                        k = i;\n                    }\n                    else if (match !== '') {\n                        if (braces) {\n                            braces--;\n                        }\n                    }\n                }\n                else if (c === '\\\\') {\n                    if (match === '' && text.substr(i).match(/^(eq)?ref\\s*\\{/)) {\n                        var len = RegExp['$&'].length;\n                        if (k < i - 1) {\n                            mml.push(internalText(parser, text.slice(k, i - 1), def));\n                        }\n                        match = '}';\n                        k = i - 1;\n                        i += len;\n                    }\n                    else {\n                        c = text.charAt(i++);\n                        if (c === '(' && match === '') {\n                            if (k < i - 2) {\n                                mml.push(internalText(parser, text.slice(k, i - 2), def));\n                            }\n                            match = ')';\n                            k = i;\n                        }\n                        else if (c === ')' && match === ')' && braces === 0) {\n                            node = parser.create('node', 'TeXAtom', [(new TexParser_js_1.default(text.slice(k, i - 2), {}, parser.configuration)).mml()]);\n                            mml.push(node);\n                            match = '';\n                            k = i;\n                        }\n                        else if (c.match(/[${}\\\\]/) && match === '') {\n                            i--;\n                            text = text.substr(0, i - 1) + text.substr(i);\n                        }\n                    }\n                }\n            }\n            if (match !== '') {\n                throw new TexError_js_1.default('MathNotTerminated', 'Math not terminated in text box');\n            }\n        }\n        if (k < text.length) {\n            mml.push(internalText(parser, text.slice(k), def));\n        }\n        if (level != null) {\n            mml = [parser.create('node', 'mstyle', mml, { displaystyle: false, scriptlevel: level })];\n        }\n        else if (mml.length > 1) {\n            mml = [parser.create('node', 'mrow', mml)];\n        }\n        return mml;\n    }\n    ParseUtil.internalMath = internalMath;\n    function internalText(parser, text, def) {\n        text = text.replace(/^\\s+/, Entities_js_1.entities.nbsp).replace(/\\s+$/, Entities_js_1.entities.nbsp);\n        var textNode = parser.create('text', text);\n        return parser.create('node', 'mtext', [], def, textNode);\n    }\n    ParseUtil.internalText = internalText;\n    function underOver(parser, base, script, pos, stack) {\n        ParseUtil.checkMovableLimits(base);\n        if (NodeUtil_js_1.default.isType(base, 'munderover') && NodeUtil_js_1.default.isEmbellished(base)) {\n            NodeUtil_js_1.default.setProperties(NodeUtil_js_1.default.getCoreMO(base), { lspace: 0, rspace: 0 });\n            var mo = parser.create('node', 'mo', [], { rspace: 0 });\n            base = parser.create('node', 'mrow', [mo, base]);\n        }\n        var mml = parser.create('node', 'munderover', [base]);\n        NodeUtil_js_1.default.setChild(mml, pos === 'over' ? mml.over : mml.under, script);\n        var node = mml;\n        if (stack) {\n            node = parser.create('node', 'TeXAtom', [mml], { texClass: MmlNode_js_1.TEXCLASS.OP, movesupsub: true });\n        }\n        NodeUtil_js_1.default.setProperty(node, 'subsupOK', true);\n        return node;\n    }\n    ParseUtil.underOver = underOver;\n    function checkMovableLimits(base) {\n        var symbol = (NodeUtil_js_1.default.isType(base, 'mo') ? NodeUtil_js_1.default.getForm(base) : null);\n        if (NodeUtil_js_1.default.getProperty(base, 'movablelimits') || (symbol && symbol[3] && symbol[3].movablelimits)) {\n            NodeUtil_js_1.default.setProperties(base, { movablelimits: false });\n        }\n    }\n    ParseUtil.checkMovableLimits = checkMovableLimits;\n    function trimSpaces(text) {\n        if (typeof (text) !== 'string') {\n            return text;\n        }\n        var TEXT = text.trim();\n        if (TEXT.match(/\\\\$/) && text.match(/ $/)) {\n            TEXT += ' ';\n        }\n        return TEXT;\n    }\n    ParseUtil.trimSpaces = trimSpaces;\n    function setArrayAlign(array, align) {\n        align = ParseUtil.trimSpaces(align || '');\n        if (align === 't') {\n            array.arraydef.align = 'baseline 1';\n        }\n        else if (align === 'b') {\n            array.arraydef.align = 'baseline -1';\n        }\n        else if (align === 'c') {\n            array.arraydef.align = 'axis';\n        }\n        else if (align) {\n            array.arraydef.align = align;\n        }\n        return array;\n    }\n    ParseUtil.setArrayAlign = setArrayAlign;\n    function substituteArgs(parser, args, str) {\n        var text = '';\n        var newstring = '';\n        var i = 0;\n        while (i < str.length) {\n            var c = str.charAt(i++);\n            if (c === '\\\\') {\n                text += c + str.charAt(i++);\n            }\n            else if (c === '#') {\n                c = str.charAt(i++);\n                if (c === '#') {\n                    text += c;\n                }\n                else {\n                    if (!c.match(/[1-9]/) || parseInt(c, 10) > args.length) {\n                        throw new TexError_js_1.default('IllegalMacroParam', 'Illegal macro parameter reference');\n                    }\n                    newstring = addArgs(parser, addArgs(parser, newstring, text), args[parseInt(c, 10) - 1]);\n                    text = '';\n                }\n            }\n            else {\n                text += c;\n            }\n        }\n        return addArgs(parser, newstring, text);\n    }\n    ParseUtil.substituteArgs = substituteArgs;\n    function addArgs(parser, s1, s2) {\n        if (s2.match(/^[a-z]/i) && s1.match(/(^|[^\\\\])(\\\\\\\\)*\\\\[a-z]+$/i)) {\n            s1 += ' ';\n        }\n        if (s1.length + s2.length > parser.configuration.options['maxBuffer']) {\n            throw new TexError_js_1.default('MaxBufferSize', 'MathJax internal buffer size exceeded; is there a' +\n                ' recursive macro call?');\n        }\n        return s1 + s2;\n    }\n    ParseUtil.addArgs = addArgs;\n    function checkMaxMacros(parser, isMacro) {\n        if (isMacro === void 0) { isMacro = true; }\n        if (++parser.macroCount <= parser.configuration.options['maxMacros']) {\n            return;\n        }\n        if (isMacro) {\n            throw new TexError_js_1.default('MaxMacroSub1', 'MathJax maximum macro substitution count exceeded; ' +\n                'is here a recursive macro call?');\n        }\n        else {\n            throw new TexError_js_1.default('MaxMacroSub2', 'MathJax maximum substitution count exceeded; ' +\n                'is there a recursive latex environment?');\n        }\n    }\n    ParseUtil.checkMaxMacros = checkMaxMacros;\n    function checkEqnEnv(parser) {\n        if (parser.stack.global.eqnenv) {\n            throw new TexError_js_1.default('ErroneousNestingEq', 'Erroneous nesting of equation structures');\n        }\n        parser.stack.global.eqnenv = true;\n    }\n    ParseUtil.checkEqnEnv = checkEqnEnv;\n    function copyNode(node, parser) {\n        var tree = node.copy();\n        var options = parser.configuration;\n        tree.walkTree(function (n) {\n            var e_1, _a;\n            options.addNode(n.kind, n);\n            var lists = (n.getProperty('in-lists') || '').split(/,/);\n            try {\n                for (var lists_1 = __values(lists), lists_1_1 = lists_1.next(); !lists_1_1.done; lists_1_1 = lists_1.next()) {\n                    var list = lists_1_1.value;\n                    list && options.addNode(list, n);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (lists_1_1 && !lists_1_1.done && (_a = lists_1.return)) _a.call(lists_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        });\n        return tree;\n    }\n    ParseUtil.copyNode = copyNode;\n    function MmlFilterAttribute(_parser, _name, value) {\n        return value;\n    }\n    ParseUtil.MmlFilterAttribute = MmlFilterAttribute;\n    function getFontDef(parser) {\n        var font = parser.stack.env['font'];\n        return (font ? { mathvariant: font } : {});\n    }\n    ParseUtil.getFontDef = getFontDef;\n    function keyvalOptions(attrib, allowed, error) {\n        var e_2, _a;\n        if (allowed === void 0) { allowed = null; }\n        if (error === void 0) { error = false; }\n        var def = readKeyval(attrib);\n        if (allowed) {\n            try {\n                for (var _b = __values(Object.keys(def)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var key = _c.value;\n                    if (!allowed.hasOwnProperty(key)) {\n                        if (error) {\n                            throw new TexError_js_1.default('InvalidOption', 'Invalid option: %1', key);\n                        }\n                        delete def[key];\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }\n        return def;\n    }\n    ParseUtil.keyvalOptions = keyvalOptions;\n    function readKeyval(text) {\n        var _a, _b;\n        var options = {};\n        var rest = text;\n        var end, key, val;\n        while (rest) {\n            _a = __read(readValue(rest, ['=', ',']), 3), key = _a[0], end = _a[1], rest = _a[2];\n            if (end === '=') {\n                _b = __read(readValue(rest, [',']), 3), val = _b[0], end = _b[1], rest = _b[2];\n                val = (val === 'false' || val === 'true') ?\n                    JSON.parse(val) : val;\n                options[key] = val;\n            }\n            else if (key) {\n                options[key] = true;\n            }\n        }\n        return options;\n    }\n    function removeBraces(text, count) {\n        while (count > 0) {\n            text = text.trim().slice(1, -1);\n            count--;\n        }\n        return text.trim();\n    }\n    function readValue(text, end) {\n        var length = text.length;\n        var braces = 0;\n        var value = '';\n        var index = 0;\n        var start = 0;\n        var startCount = true;\n        var stopCount = false;\n        while (index < length) {\n            var c = text[index++];\n            switch (c) {\n                case ' ':\n                    break;\n                case '{':\n                    if (startCount) {\n                        start++;\n                    }\n                    else {\n                        stopCount = false;\n                        if (start > braces) {\n                            start = braces;\n                        }\n                    }\n                    braces++;\n                    break;\n                case '}':\n                    if (braces) {\n                        braces--;\n                    }\n                    if (startCount || stopCount) {\n                        start--;\n                        stopCount = true;\n                    }\n                    startCount = false;\n                    break;\n                default:\n                    if (!braces && end.indexOf(c) !== -1) {\n                        return [stopCount ? 'true' :\n                                removeBraces(value, start), c, text.slice(index)];\n                    }\n                    startCount = false;\n                    stopCount = false;\n            }\n            value += c;\n        }\n        if (braces) {\n            throw new TexError_js_1.default('ExtraOpenMissingClose', 'Extra open brace or missing close brace');\n        }\n        return [stopCount ? 'true' : removeBraces(value, start), '', text.slice(index)];\n    }\n})(ParseUtil || (ParseUtil = {}));\nexports.default = ParseUtil;\n//# sourceMappingURL=ParseUtil.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar NodeUtil_js_1 = __importDefault(require(\"./NodeUtil.js\"));\nvar Stack = (function () {\n    function Stack(_factory, _env, inner) {\n        this._factory = _factory;\n        this._env = _env;\n        this.global = {};\n        this.stack = [];\n        this.global = { isInner: inner };\n        this.stack = [this._factory.create('start', this.global)];\n        if (_env) {\n            this.stack[0].env = _env;\n        }\n        this.env = this.stack[0].env;\n    }\n    Object.defineProperty(Stack.prototype, \"env\", {\n        get: function () {\n            return this._env;\n        },\n        set: function (env) {\n            this._env = env;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Stack.prototype.Push = function () {\n        var e_1, _a;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        try {\n            for (var args_1 = __values(args), args_1_1 = args_1.next(); !args_1_1.done; args_1_1 = args_1.next()) {\n                var node = args_1_1.value;\n                if (!node) {\n                    continue;\n                }\n                var item = NodeUtil_js_1.default.isNode(node) ?\n                    this._factory.create('mml', node) : node;\n                item.global = this.global;\n                var _b = __read(this.stack.length ? this.Top().checkItem(item) : [null, true], 2), top_1 = _b[0], success = _b[1];\n                if (!success) {\n                    continue;\n                }\n                if (top_1) {\n                    this.Pop();\n                    this.Push.apply(this, __spreadArray([], __read(top_1), false));\n                    continue;\n                }\n                this.stack.push(item);\n                if (item.env) {\n                    if (item.copyEnv) {\n                        Object.assign(item.env, this.env);\n                    }\n                    this.env = item.env;\n                }\n                else {\n                    item.env = this.env;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (args_1_1 && !args_1_1.done && (_a = args_1.return)) _a.call(args_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    Stack.prototype.Pop = function () {\n        var item = this.stack.pop();\n        if (!item.isOpen) {\n            delete item.env;\n        }\n        this.env = (this.stack.length ? this.Top().env : {});\n        return item;\n    };\n    Stack.prototype.Top = function (n) {\n        if (n === void 0) { n = 1; }\n        return this.stack.length < n ? null : this.stack[this.stack.length - n];\n    };\n    Stack.prototype.Prev = function (noPop) {\n        var top = this.Top();\n        return noPop ? top.First : top.Pop();\n    };\n    Stack.prototype.toString = function () {\n        return 'stack[\\n  ' + this.stack.join('\\n  ') + '\\n]';\n    };\n    return Stack;\n}());\nexports.default = Stack;\n//# sourceMappingURL=Stack.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Macro = exports.Symbol = void 0;\nvar Symbol = (function () {\n    function Symbol(_symbol, _char, _attributes) {\n        this._symbol = _symbol;\n        this._char = _char;\n        this._attributes = _attributes;\n    }\n    Object.defineProperty(Symbol.prototype, \"symbol\", {\n        get: function () {\n            return this._symbol;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Symbol.prototype, \"char\", {\n        get: function () {\n            return this._char;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Symbol.prototype, \"attributes\", {\n        get: function () {\n            return this._attributes;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Symbol;\n}());\nexports.Symbol = Symbol;\nvar Macro = (function () {\n    function Macro(_symbol, _func, _args) {\n        if (_args === void 0) { _args = []; }\n        this._symbol = _symbol;\n        this._func = _func;\n        this._args = _args;\n    }\n    Object.defineProperty(Macro.prototype, \"symbol\", {\n        get: function () {\n            return this._symbol;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Macro.prototype, \"func\", {\n        get: function () {\n            return this._func;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Macro.prototype, \"args\", {\n        get: function () {\n            return this._args;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Macro;\n}());\nexports.Macro = Macro;\n//# sourceMappingURL=Symbol.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EnvironmentMap = exports.CommandMap = exports.MacroMap = exports.DelimiterMap = exports.CharacterMap = exports.AbstractParseMap = exports.RegExpMap = exports.AbstractSymbolMap = exports.parseResult = void 0;\nvar Symbol_js_1 = require(\"./Symbol.js\");\nvar MapHandler_js_1 = require(\"./MapHandler.js\");\nfunction parseResult(result) {\n    return result === void 0 ? true : result;\n}\nexports.parseResult = parseResult;\nvar AbstractSymbolMap = (function () {\n    function AbstractSymbolMap(_name, _parser) {\n        this._name = _name;\n        this._parser = _parser;\n        MapHandler_js_1.MapHandler.register(this);\n    }\n    Object.defineProperty(AbstractSymbolMap.prototype, \"name\", {\n        get: function () {\n            return this._name;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractSymbolMap.prototype.parserFor = function (symbol) {\n        return this.contains(symbol) ? this.parser : null;\n    };\n    AbstractSymbolMap.prototype.parse = function (_a) {\n        var _b = __read(_a, 2), env = _b[0], symbol = _b[1];\n        var parser = this.parserFor(symbol);\n        var mapped = this.lookup(symbol);\n        return (parser && mapped) ? parseResult(parser(env, mapped)) : null;\n    };\n    Object.defineProperty(AbstractSymbolMap.prototype, \"parser\", {\n        get: function () {\n            return this._parser;\n        },\n        set: function (parser) {\n            this._parser = parser;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return AbstractSymbolMap;\n}());\nexports.AbstractSymbolMap = AbstractSymbolMap;\nvar RegExpMap = (function (_super) {\n    __extends(RegExpMap, _super);\n    function RegExpMap(name, parser, _regExp) {\n        var _this = _super.call(this, name, parser) || this;\n        _this._regExp = _regExp;\n        return _this;\n    }\n    RegExpMap.prototype.contains = function (symbol) {\n        return this._regExp.test(symbol);\n    };\n    RegExpMap.prototype.lookup = function (symbol) {\n        return this.contains(symbol) ? symbol : null;\n    };\n    return RegExpMap;\n}(AbstractSymbolMap));\nexports.RegExpMap = RegExpMap;\nvar AbstractParseMap = (function (_super) {\n    __extends(AbstractParseMap, _super);\n    function AbstractParseMap() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.map = new Map();\n        return _this;\n    }\n    AbstractParseMap.prototype.lookup = function (symbol) {\n        return this.map.get(symbol);\n    };\n    AbstractParseMap.prototype.contains = function (symbol) {\n        return this.map.has(symbol);\n    };\n    AbstractParseMap.prototype.add = function (symbol, object) {\n        this.map.set(symbol, object);\n    };\n    AbstractParseMap.prototype.remove = function (symbol) {\n        this.map.delete(symbol);\n    };\n    return AbstractParseMap;\n}(AbstractSymbolMap));\nexports.AbstractParseMap = AbstractParseMap;\nvar CharacterMap = (function (_super) {\n    __extends(CharacterMap, _super);\n    function CharacterMap(name, parser, json) {\n        var e_1, _a;\n        var _this = _super.call(this, name, parser) || this;\n        try {\n            for (var _b = __values(Object.keys(json)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var key = _c.value;\n                var value = json[key];\n                var _d = __read((typeof (value) === 'string') ? [value, null] : value, 2), char = _d[0], attrs = _d[1];\n                var character = new Symbol_js_1.Symbol(key, char, attrs);\n                _this.add(key, character);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return _this;\n    }\n    return CharacterMap;\n}(AbstractParseMap));\nexports.CharacterMap = CharacterMap;\nvar DelimiterMap = (function (_super) {\n    __extends(DelimiterMap, _super);\n    function DelimiterMap() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    DelimiterMap.prototype.parse = function (_a) {\n        var _b = __read(_a, 2), env = _b[0], symbol = _b[1];\n        return _super.prototype.parse.call(this, [env, '\\\\' + symbol]);\n    };\n    return DelimiterMap;\n}(CharacterMap));\nexports.DelimiterMap = DelimiterMap;\nvar MacroMap = (function (_super) {\n    __extends(MacroMap, _super);\n    function MacroMap(name, json, functionMap) {\n        var e_2, _a;\n        var _this = _super.call(this, name, null) || this;\n        try {\n            for (var _b = __values(Object.keys(json)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var key = _c.value;\n                var value = json[key];\n                var _d = __read((typeof (value) === 'string') ? [value] : value), func = _d[0], attrs = _d.slice(1);\n                var character = new Symbol_js_1.Macro(key, functionMap[func], attrs);\n                _this.add(key, character);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return _this;\n    }\n    MacroMap.prototype.parserFor = function (symbol) {\n        var macro = this.lookup(symbol);\n        return macro ? macro.func : null;\n    };\n    MacroMap.prototype.parse = function (_a) {\n        var _b = __read(_a, 2), env = _b[0], symbol = _b[1];\n        var macro = this.lookup(symbol);\n        var parser = this.parserFor(symbol);\n        if (!macro || !parser) {\n            return null;\n        }\n        return parseResult(parser.apply(void 0, __spreadArray([env, macro.symbol], __read(macro.args), false)));\n    };\n    return MacroMap;\n}(AbstractParseMap));\nexports.MacroMap = MacroMap;\nvar CommandMap = (function (_super) {\n    __extends(CommandMap, _super);\n    function CommandMap() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CommandMap.prototype.parse = function (_a) {\n        var _b = __read(_a, 2), env = _b[0], symbol = _b[1];\n        var macro = this.lookup(symbol);\n        var parser = this.parserFor(symbol);\n        if (!macro || !parser) {\n            return null;\n        }\n        var saveCommand = env.currentCS;\n        env.currentCS = '\\\\' + symbol;\n        var result = parser.apply(void 0, __spreadArray([env, '\\\\' + macro.symbol], __read(macro.args), false));\n        env.currentCS = saveCommand;\n        return parseResult(result);\n    };\n    return CommandMap;\n}(MacroMap));\nexports.CommandMap = CommandMap;\nvar EnvironmentMap = (function (_super) {\n    __extends(EnvironmentMap, _super);\n    function EnvironmentMap(name, parser, json, functionMap) {\n        var _this = _super.call(this, name, json, functionMap) || this;\n        _this.parser = parser;\n        return _this;\n    }\n    EnvironmentMap.prototype.parse = function (_a) {\n        var _b = __read(_a, 2), env = _b[0], symbol = _b[1];\n        var macro = this.lookup(symbol);\n        var envParser = this.parserFor(symbol);\n        if (!macro || !envParser) {\n            return null;\n        }\n        return parseResult(this.parser(env, macro.symbol, envParser, macro.args));\n    };\n    return EnvironmentMap;\n}(MacroMap));\nexports.EnvironmentMap = EnvironmentMap;\n//# sourceMappingURL=SymbolMap.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TagsFactory = exports.AllTags = exports.NoTags = exports.AbstractTags = exports.TagInfo = exports.Label = void 0;\nvar TexParser_js_1 = __importDefault(require(\"./TexParser.js\"));\nvar Label = (function () {\n    function Label(tag, id) {\n        if (tag === void 0) { tag = '???'; }\n        if (id === void 0) { id = ''; }\n        this.tag = tag;\n        this.id = id;\n    }\n    return Label;\n}());\nexports.Label = Label;\nvar TagInfo = (function () {\n    function TagInfo(env, taggable, defaultTags, tag, tagId, tagFormat, noTag, labelId) {\n        if (env === void 0) { env = ''; }\n        if (taggable === void 0) { taggable = false; }\n        if (defaultTags === void 0) { defaultTags = false; }\n        if (tag === void 0) { tag = null; }\n        if (tagId === void 0) { tagId = ''; }\n        if (tagFormat === void 0) { tagFormat = ''; }\n        if (noTag === void 0) { noTag = false; }\n        if (labelId === void 0) { labelId = ''; }\n        this.env = env;\n        this.taggable = taggable;\n        this.defaultTags = defaultTags;\n        this.tag = tag;\n        this.tagId = tagId;\n        this.tagFormat = tagFormat;\n        this.noTag = noTag;\n        this.labelId = labelId;\n    }\n    return TagInfo;\n}());\nexports.TagInfo = TagInfo;\nvar AbstractTags = (function () {\n    function AbstractTags() {\n        this.counter = 0;\n        this.allCounter = 0;\n        this.configuration = null;\n        this.ids = {};\n        this.allIds = {};\n        this.labels = {};\n        this.allLabels = {};\n        this.redo = false;\n        this.refUpdate = false;\n        this.currentTag = new TagInfo();\n        this.history = [];\n        this.stack = [];\n        this.enTag = function (node, tag) {\n            var nf = this.configuration.nodeFactory;\n            var cell = nf.create('node', 'mtd', [node]);\n            var row = nf.create('node', 'mlabeledtr', [tag, cell]);\n            var table = nf.create('node', 'mtable', [row], {\n                side: this.configuration.options['tagSide'],\n                minlabelspacing: this.configuration.options['tagIndent'],\n                displaystyle: true\n            });\n            return table;\n        };\n    }\n    AbstractTags.prototype.start = function (env, taggable, defaultTags) {\n        if (this.currentTag) {\n            this.stack.push(this.currentTag);\n        }\n        this.currentTag = new TagInfo(env, taggable, defaultTags);\n    };\n    Object.defineProperty(AbstractTags.prototype, \"env\", {\n        get: function () {\n            return this.currentTag.env;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractTags.prototype.end = function () {\n        this.history.push(this.currentTag);\n        this.currentTag = this.stack.pop();\n    };\n    AbstractTags.prototype.tag = function (tag, noFormat) {\n        this.currentTag.tag = tag;\n        this.currentTag.tagFormat = noFormat ? tag : this.formatTag(tag);\n        this.currentTag.noTag = false;\n    };\n    AbstractTags.prototype.notag = function () {\n        this.tag('', true);\n        this.currentTag.noTag = true;\n    };\n    Object.defineProperty(AbstractTags.prototype, \"noTag\", {\n        get: function () {\n            return this.currentTag.noTag;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(AbstractTags.prototype, \"label\", {\n        get: function () {\n            return this.currentTag.labelId;\n        },\n        set: function (label) {\n            this.currentTag.labelId = label;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractTags.prototype.formatUrl = function (id, base) {\n        return base + '#' + encodeURIComponent(id);\n    };\n    AbstractTags.prototype.formatTag = function (tag) {\n        return '(' + tag + ')';\n    };\n    AbstractTags.prototype.formatId = function (id) {\n        return 'mjx-eqn:' + id.replace(/\\s/g, '_');\n    };\n    AbstractTags.prototype.formatNumber = function (n) {\n        return n.toString();\n    };\n    AbstractTags.prototype.autoTag = function () {\n        if (this.currentTag.tag == null) {\n            this.counter++;\n            this.tag(this.formatNumber(this.counter), false);\n        }\n    };\n    AbstractTags.prototype.clearTag = function () {\n        this.label = '';\n        this.tag(null, true);\n        this.currentTag.tagId = '';\n    };\n    AbstractTags.prototype.getTag = function (force) {\n        if (force === void 0) { force = false; }\n        if (force) {\n            this.autoTag();\n            return this.makeTag();\n        }\n        var ct = this.currentTag;\n        if (ct.taggable && !ct.noTag) {\n            if (ct.defaultTags) {\n                this.autoTag();\n            }\n            if (ct.tag) {\n                return this.makeTag();\n            }\n        }\n        return null;\n    };\n    AbstractTags.prototype.resetTag = function () {\n        this.history = [];\n        this.redo = false;\n        this.refUpdate = false;\n        this.clearTag();\n    };\n    AbstractTags.prototype.reset = function (offset) {\n        if (offset === void 0) { offset = 0; }\n        this.resetTag();\n        this.counter = this.allCounter = offset;\n        this.allLabels = {};\n        this.allIds = {};\n    };\n    AbstractTags.prototype.startEquation = function (math) {\n        this.history = [];\n        this.stack = [];\n        this.clearTag();\n        this.currentTag = new TagInfo('', undefined, undefined);\n        this.labels = {};\n        this.ids = {};\n        this.counter = this.allCounter;\n        this.redo = false;\n        var recompile = math.inputData.recompile;\n        if (recompile) {\n            this.refUpdate = true;\n            this.counter = recompile.counter;\n        }\n    };\n    AbstractTags.prototype.finishEquation = function (math) {\n        if (this.redo) {\n            math.inputData.recompile = {\n                state: math.state(),\n                counter: this.allCounter\n            };\n        }\n        if (!this.refUpdate) {\n            this.allCounter = this.counter;\n        }\n        Object.assign(this.allIds, this.ids);\n        Object.assign(this.allLabels, this.labels);\n    };\n    AbstractTags.prototype.finalize = function (node, env) {\n        if (!env.display || this.currentTag.env ||\n            this.currentTag.tag == null) {\n            return node;\n        }\n        var tag = this.makeTag();\n        var table = this.enTag(node, tag);\n        return table;\n    };\n    AbstractTags.prototype.makeId = function () {\n        this.currentTag.tagId = this.formatId(this.configuration.options['useLabelIds'] ?\n            (this.label || this.currentTag.tag) : this.currentTag.tag);\n    };\n    AbstractTags.prototype.makeTag = function () {\n        this.makeId();\n        if (this.label) {\n            this.labels[this.label] = new Label(this.currentTag.tag, this.currentTag.tagId);\n        }\n        var mml = new TexParser_js_1.default('\\\\text{' + this.currentTag.tagFormat + '}', {}, this.configuration).mml();\n        return this.configuration.nodeFactory.create('node', 'mtd', [mml], { id: this.currentTag.tagId });\n    };\n    return AbstractTags;\n}());\nexports.AbstractTags = AbstractTags;\nvar NoTags = (function (_super) {\n    __extends(NoTags, _super);\n    function NoTags() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoTags.prototype.autoTag = function () { };\n    NoTags.prototype.getTag = function () {\n        return !this.currentTag.tag ? null : _super.prototype.getTag.call(this);\n    };\n    return NoTags;\n}(AbstractTags));\nexports.NoTags = NoTags;\nvar AllTags = (function (_super) {\n    __extends(AllTags, _super);\n    function AllTags() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AllTags.prototype.finalize = function (node, env) {\n        if (!env.display || this.history.find(function (x) { return x.taggable; })) {\n            return node;\n        }\n        var tag = this.getTag(true);\n        return this.enTag(node, tag);\n    };\n    return AllTags;\n}(AbstractTags));\nexports.AllTags = AllTags;\nvar TagsFactory;\n(function (TagsFactory) {\n    var tagsMapping = new Map([\n        ['none', NoTags],\n        ['all', AllTags]\n    ]);\n    var defaultTags = 'none';\n    TagsFactory.OPTIONS = {\n        tags: defaultTags,\n        tagSide: 'right',\n        tagIndent: '0.8em',\n        useLabelIds: true,\n        ignoreDuplicateLabels: false\n    };\n    TagsFactory.add = function (name, constr) {\n        tagsMapping.set(name, constr);\n    };\n    TagsFactory.addTags = function (tags) {\n        var e_1, _a;\n        try {\n            for (var _b = __values(Object.keys(tags)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var key = _c.value;\n                TagsFactory.add(key, tags[key]);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    TagsFactory.create = function (name) {\n        var constr = tagsMapping.get(name) || tagsMapping.get(defaultTags);\n        if (!constr) {\n            throw Error('Unknown tags class');\n        }\n        return new constr();\n    };\n    TagsFactory.setDefault = function (name) {\n        defaultTags = name;\n    };\n    TagsFactory.getDefault = function () {\n        return TagsFactory.create(defaultTags);\n    };\n})(TagsFactory = exports.TagsFactory || (exports.TagsFactory = {}));\n//# sourceMappingURL=Tags.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar TexError = (function () {\n    function TexError(id, message) {\n        var rest = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            rest[_i - 2] = arguments[_i];\n        }\n        this.id = id;\n        this.message = TexError.processString(message, rest);\n    }\n    TexError.processString = function (str, args) {\n        var parts = str.split(TexError.pattern);\n        for (var i = 1, m = parts.length; i < m; i += 2) {\n            var c = parts[i].charAt(0);\n            if (c >= '0' && c <= '9') {\n                parts[i] = args[parseInt(parts[i], 10) - 1];\n                if (typeof parts[i] === 'number') {\n                    parts[i] = parts[i].toString();\n                }\n            }\n            else if (c === '{') {\n                c = parts[i].substr(1);\n                if (c >= '0' && c <= '9') {\n                    parts[i] = args[parseInt(parts[i].substr(1, parts[i].length - 2), 10) - 1];\n                    if (typeof parts[i] === 'number') {\n                        parts[i] = parts[i].toString();\n                    }\n                }\n                else {\n                    var match = parts[i].match(/^\\{([a-z]+):%(\\d+)\\|(.*)\\}$/);\n                    if (match) {\n                        parts[i] = '%' + parts[i];\n                    }\n                }\n            }\n            if (parts[i] == null) {\n                parts[i] = '???';\n            }\n        }\n        return parts.join('');\n    };\n    TexError.pattern = /%(\\d+|\\{\\d+\\}|\\{[a-z]+:\\%\\d+(?:\\|(?:%\\{\\d+\\}|%.|[^\\}])*)+\\}|.)/g;\n    return TexError;\n}());\nexports.default = TexError;\n//# sourceMappingURL=TexError.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar ParseUtil_js_1 = __importDefault(require(\"./ParseUtil.js\"));\nvar Stack_js_1 = __importDefault(require(\"./Stack.js\"));\nvar TexError_js_1 = __importDefault(require(\"./TexError.js\"));\nvar MmlNode_js_1 = require(\"../../core/MmlTree/MmlNode.js\");\nvar TexParser = (function () {\n    function TexParser(_string, env, configuration) {\n        var e_1, _a;\n        this._string = _string;\n        this.configuration = configuration;\n        this.macroCount = 0;\n        this.i = 0;\n        this.currentCS = '';\n        var inner = env.hasOwnProperty('isInner');\n        var isInner = env['isInner'];\n        delete env['isInner'];\n        var ENV;\n        if (env) {\n            ENV = {};\n            try {\n                for (var _b = __values(Object.keys(env)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var id = _c.value;\n                    ENV[id] = env[id];\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n        this.configuration.pushParser(this);\n        this.stack = new Stack_js_1.default(this.itemFactory, ENV, inner ? isInner : true);\n        this.Parse();\n        this.Push(this.itemFactory.create('stop'));\n    }\n    Object.defineProperty(TexParser.prototype, \"options\", {\n        get: function () {\n            return this.configuration.options;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(TexParser.prototype, \"itemFactory\", {\n        get: function () {\n            return this.configuration.itemFactory;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(TexParser.prototype, \"tags\", {\n        get: function () {\n            return this.configuration.tags;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(TexParser.prototype, \"string\", {\n        get: function () {\n            return this._string;\n        },\n        set: function (str) {\n            this._string = str;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    TexParser.prototype.parse = function (kind, input) {\n        return this.configuration.handlers.get(kind).parse(input);\n    };\n    TexParser.prototype.lookup = function (kind, symbol) {\n        return this.configuration.handlers.get(kind).lookup(symbol);\n    };\n    TexParser.prototype.contains = function (kind, symbol) {\n        return this.configuration.handlers.get(kind).contains(symbol);\n    };\n    TexParser.prototype.toString = function () {\n        var e_2, _a;\n        var str = '';\n        try {\n            for (var _b = __values(Array.from(this.configuration.handlers.keys())), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var config = _c.value;\n                str += config + ': ' +\n                    this.configuration.handlers.get(config) + '\\n';\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return str;\n    };\n    TexParser.prototype.Parse = function () {\n        var c;\n        while (this.i < this.string.length) {\n            c = this.getCodePoint();\n            this.i += c.length;\n            this.parse('character', [this, c]);\n        }\n    };\n    TexParser.prototype.Push = function (arg) {\n        if (arg instanceof MmlNode_js_1.AbstractMmlNode && arg.isInferred) {\n            this.PushAll(arg.childNodes);\n        }\n        else {\n            this.stack.Push(arg);\n        }\n    };\n    TexParser.prototype.PushAll = function (args) {\n        var e_3, _a;\n        try {\n            for (var args_1 = __values(args), args_1_1 = args_1.next(); !args_1_1.done; args_1_1 = args_1.next()) {\n                var arg = args_1_1.value;\n                this.stack.Push(arg);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (args_1_1 && !args_1_1.done && (_a = args_1.return)) _a.call(args_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    TexParser.prototype.mml = function () {\n        if (!this.stack.Top().isKind('mml')) {\n            return null;\n        }\n        var node = this.stack.Top().First;\n        this.configuration.popParser();\n        return node;\n    };\n    TexParser.prototype.convertDelimiter = function (c) {\n        var symbol = this.lookup('delimiter', c);\n        return symbol ? symbol.char : null;\n    };\n    TexParser.prototype.getCodePoint = function () {\n        var code = this.string.codePointAt(this.i);\n        return code === undefined ? '' : String.fromCodePoint(code);\n    };\n    TexParser.prototype.nextIsSpace = function () {\n        return !!this.string.charAt(this.i).match(/\\s/);\n    };\n    TexParser.prototype.GetNext = function () {\n        while (this.nextIsSpace()) {\n            this.i++;\n        }\n        return this.getCodePoint();\n    };\n    TexParser.prototype.GetCS = function () {\n        var CS = this.string.slice(this.i).match(/^(([a-z]+) ?|[\\uD800-\\uDBFF].|.)/i);\n        if (CS) {\n            this.i += CS[0].length;\n            return CS[2] || CS[1];\n        }\n        else {\n            this.i++;\n            return ' ';\n        }\n    };\n    TexParser.prototype.GetArgument = function (_name, noneOK) {\n        switch (this.GetNext()) {\n            case '':\n                if (!noneOK) {\n                    throw new TexError_js_1.default('MissingArgFor', 'Missing argument for %1', this.currentCS);\n                }\n                return null;\n            case '}':\n                if (!noneOK) {\n                    throw new TexError_js_1.default('ExtraCloseMissingOpen', 'Extra close brace or missing open brace');\n                }\n                return null;\n            case '\\\\':\n                this.i++;\n                return '\\\\' + this.GetCS();\n            case '{':\n                var j = ++this.i, parens = 1;\n                while (this.i < this.string.length) {\n                    switch (this.string.charAt(this.i++)) {\n                        case '\\\\':\n                            this.i++;\n                            break;\n                        case '{':\n                            parens++;\n                            break;\n                        case '}':\n                            if (--parens === 0) {\n                                return this.string.slice(j, this.i - 1);\n                            }\n                            break;\n                    }\n                }\n                throw new TexError_js_1.default('MissingCloseBrace', 'Missing close brace');\n        }\n        var c = this.getCodePoint();\n        this.i += c.length;\n        return c;\n    };\n    TexParser.prototype.GetBrackets = function (_name, def) {\n        if (this.GetNext() !== '[') {\n            return def;\n        }\n        var j = ++this.i, parens = 0;\n        while (this.i < this.string.length) {\n            switch (this.string.charAt(this.i++)) {\n                case '{':\n                    parens++;\n                    break;\n                case '\\\\':\n                    this.i++;\n                    break;\n                case '}':\n                    if (parens-- <= 0) {\n                        throw new TexError_js_1.default('ExtraCloseLooking', 'Extra close brace while looking for %1', '\\']\\'');\n                    }\n                    break;\n                case ']':\n                    if (parens === 0) {\n                        return this.string.slice(j, this.i - 1);\n                    }\n                    break;\n            }\n        }\n        throw new TexError_js_1.default('MissingCloseBracket', 'Could not find closing \\']\\' for argument to %1', this.currentCS);\n    };\n    TexParser.prototype.GetDelimiter = function (name, braceOK) {\n        var c = this.GetNext();\n        this.i += c.length;\n        if (this.i <= this.string.length) {\n            if (c === '\\\\') {\n                c += this.GetCS();\n            }\n            else if (c === '{' && braceOK) {\n                this.i--;\n                c = this.GetArgument(name).trim();\n            }\n            if (this.contains('delimiter', c)) {\n                return this.convertDelimiter(c);\n            }\n        }\n        throw new TexError_js_1.default('MissingOrUnrecognizedDelim', 'Missing or unrecognized delimiter for %1', this.currentCS);\n    };\n    TexParser.prototype.GetDimen = function (name) {\n        if (this.GetNext() === '{') {\n            var dimen = this.GetArgument(name);\n            var _a = __read(ParseUtil_js_1.default.matchDimen(dimen), 2), value = _a[0], unit = _a[1];\n            if (value) {\n                return value + unit;\n            }\n        }\n        else {\n            var dimen = this.string.slice(this.i);\n            var _b = __read(ParseUtil_js_1.default.matchDimen(dimen, true), 3), value = _b[0], unit = _b[1], length_1 = _b[2];\n            if (value) {\n                this.i += length_1;\n                return value + unit;\n            }\n        }\n        throw new TexError_js_1.default('MissingDimOrUnits', 'Missing dimension or its units for %1', this.currentCS);\n    };\n    TexParser.prototype.GetUpTo = function (_name, token) {\n        while (this.nextIsSpace()) {\n            this.i++;\n        }\n        var j = this.i;\n        var parens = 0;\n        while (this.i < this.string.length) {\n            var k = this.i;\n            var c = this.GetNext();\n            this.i += c.length;\n            switch (c) {\n                case '\\\\':\n                    c += this.GetCS();\n                    break;\n                case '{':\n                    parens++;\n                    break;\n                case '}':\n                    if (parens === 0) {\n                        throw new TexError_js_1.default('ExtraCloseLooking', 'Extra close brace while looking for %1', token);\n                    }\n                    parens--;\n                    break;\n            }\n            if (parens === 0 && c === token) {\n                return this.string.slice(j, k);\n            }\n        }\n        throw new TexError_js_1.default('TokenNotFoundForCommand', 'Could not find %1 for %2', token, this.currentCS);\n    };\n    TexParser.prototype.ParseArg = function (name) {\n        return new TexParser(this.GetArgument(name), this.stack.env, this.configuration).mml();\n    };\n    TexParser.prototype.ParseUpTo = function (name, token) {\n        return new TexParser(this.GetUpTo(name, token), this.stack.env, this.configuration).mml();\n    };\n    TexParser.prototype.GetDelimiterArg = function (name) {\n        var c = ParseUtil_js_1.default.trimSpaces(this.GetArgument(name));\n        if (c === '') {\n            return null;\n        }\n        if (this.contains('delimiter', c)) {\n            return c;\n        }\n        throw new TexError_js_1.default('MissingOrUnrecognizedDelim', 'Missing or unrecognized delimiter for %1', this.currentCS);\n    };\n    TexParser.prototype.GetStar = function () {\n        var star = (this.GetNext() === '*');\n        if (star) {\n            this.i++;\n        }\n        return star;\n    };\n    TexParser.prototype.create = function (kind) {\n        var _a;\n        var rest = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            rest[_i - 1] = arguments[_i];\n        }\n        return (_a = this.configuration.nodeFactory).create.apply(_a, __spreadArray([kind], __read(rest), false));\n    };\n    return TexParser;\n}());\nexports.default = TexParser;\n//# sourceMappingURL=TexParser.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mathjax = void 0;\nvar version_js_1 = require(\"./components/version.js\");\nvar HandlerList_js_1 = require(\"./core/HandlerList.js\");\nvar Retries_js_1 = require(\"./util/Retries.js\");\nexports.mathjax = {\n    version: version_js_1.VERSION,\n    handlers: new HandlerList_js_1.HandlerList(),\n    document: function (document, options) {\n        return exports.mathjax.handlers.document(document, options);\n    },\n    handleRetriesFor: Retries_js_1.handleRetriesFor,\n    retryAfter: Retries_js_1.retryAfter,\n    asyncLoad: null,\n};\n//# sourceMappingURL=mathjax.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.asyncLoad = void 0;\nvar mathjax_js_1 = require(\"../mathjax.js\");\nfunction asyncLoad(name) {\n    if (!mathjax_js_1.mathjax.asyncLoad) {\n        return Promise.reject(\"Can't load '\".concat(name, \"': No asyncLoad method specified\"));\n    }\n    return new Promise(function (ok, fail) {\n        var result = mathjax_js_1.mathjax.asyncLoad(name);\n        if (result instanceof Promise) {\n            result.then(function (value) { return ok(value); }).catch(function (err) { return fail(err); });\n        }\n        else {\n            ok(result);\n        }\n    });\n}\nexports.asyncLoad = asyncLoad;\n//# sourceMappingURL=AsyncLoad.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.numeric = exports.translate = exports.remove = exports.add = exports.entities = exports.options = void 0;\nvar Retries_js_1 = require(\"./Retries.js\");\nvar AsyncLoad_js_1 = require(\"./AsyncLoad.js\");\nexports.options = {\n    loadMissingEntities: true\n};\nexports.entities = {\n    ApplyFunction: '\\u2061',\n    Backslash: '\\u2216',\n    Because: '\\u2235',\n    Breve: '\\u02D8',\n    Cap: '\\u22D2',\n    CenterDot: '\\u00B7',\n    CircleDot: '\\u2299',\n    CircleMinus: '\\u2296',\n    CirclePlus: '\\u2295',\n    CircleTimes: '\\u2297',\n    Congruent: '\\u2261',\n    ContourIntegral: '\\u222E',\n    Coproduct: '\\u2210',\n    Cross: '\\u2A2F',\n    Cup: '\\u22D3',\n    CupCap: '\\u224D',\n    Dagger: '\\u2021',\n    Del: '\\u2207',\n    Delta: '\\u0394',\n    Diamond: '\\u22C4',\n    DifferentialD: '\\u2146',\n    DotEqual: '\\u2250',\n    DoubleDot: '\\u00A8',\n    DoubleRightTee: '\\u22A8',\n    DoubleVerticalBar: '\\u2225',\n    DownArrow: '\\u2193',\n    DownLeftVector: '\\u21BD',\n    DownRightVector: '\\u21C1',\n    DownTee: '\\u22A4',\n    Downarrow: '\\u21D3',\n    Element: '\\u2208',\n    EqualTilde: '\\u2242',\n    Equilibrium: '\\u21CC',\n    Exists: '\\u2203',\n    ExponentialE: '\\u2147',\n    FilledVerySmallSquare: '\\u25AA',\n    ForAll: '\\u2200',\n    Gamma: '\\u0393',\n    Gg: '\\u22D9',\n    GreaterEqual: '\\u2265',\n    GreaterEqualLess: '\\u22DB',\n    GreaterFullEqual: '\\u2267',\n    GreaterLess: '\\u2277',\n    GreaterSlantEqual: '\\u2A7E',\n    GreaterTilde: '\\u2273',\n    Hacek: '\\u02C7',\n    Hat: '\\u005E',\n    HumpDownHump: '\\u224E',\n    HumpEqual: '\\u224F',\n    Im: '\\u2111',\n    ImaginaryI: '\\u2148',\n    Integral: '\\u222B',\n    Intersection: '\\u22C2',\n    InvisibleComma: '\\u2063',\n    InvisibleTimes: '\\u2062',\n    Lambda: '\\u039B',\n    Larr: '\\u219E',\n    LeftAngleBracket: '\\u27E8',\n    LeftArrow: '\\u2190',\n    LeftArrowRightArrow: '\\u21C6',\n    LeftCeiling: '\\u2308',\n    LeftDownVector: '\\u21C3',\n    LeftFloor: '\\u230A',\n    LeftRightArrow: '\\u2194',\n    LeftTee: '\\u22A3',\n    LeftTriangle: '\\u22B2',\n    LeftTriangleEqual: '\\u22B4',\n    LeftUpVector: '\\u21BF',\n    LeftVector: '\\u21BC',\n    Leftarrow: '\\u21D0',\n    Leftrightarrow: '\\u21D4',\n    LessEqualGreater: '\\u22DA',\n    LessFullEqual: '\\u2266',\n    LessGreater: '\\u2276',\n    LessSlantEqual: '\\u2A7D',\n    LessTilde: '\\u2272',\n    Ll: '\\u22D8',\n    Lleftarrow: '\\u21DA',\n    LongLeftArrow: '\\u27F5',\n    LongLeftRightArrow: '\\u27F7',\n    LongRightArrow: '\\u27F6',\n    Longleftarrow: '\\u27F8',\n    Longleftrightarrow: '\\u27FA',\n    Longrightarrow: '\\u27F9',\n    Lsh: '\\u21B0',\n    MinusPlus: '\\u2213',\n    NestedGreaterGreater: '\\u226B',\n    NestedLessLess: '\\u226A',\n    NotDoubleVerticalBar: '\\u2226',\n    NotElement: '\\u2209',\n    NotEqual: '\\u2260',\n    NotExists: '\\u2204',\n    NotGreater: '\\u226F',\n    NotGreaterEqual: '\\u2271',\n    NotLeftTriangle: '\\u22EA',\n    NotLeftTriangleEqual: '\\u22EC',\n    NotLess: '\\u226E',\n    NotLessEqual: '\\u2270',\n    NotPrecedes: '\\u2280',\n    NotPrecedesSlantEqual: '\\u22E0',\n    NotRightTriangle: '\\u22EB',\n    NotRightTriangleEqual: '\\u22ED',\n    NotSubsetEqual: '\\u2288',\n    NotSucceeds: '\\u2281',\n    NotSucceedsSlantEqual: '\\u22E1',\n    NotSupersetEqual: '\\u2289',\n    NotTilde: '\\u2241',\n    NotVerticalBar: '\\u2224',\n    Omega: '\\u03A9',\n    OverBar: '\\u203E',\n    OverBrace: '\\u23DE',\n    PartialD: '\\u2202',\n    Phi: '\\u03A6',\n    Pi: '\\u03A0',\n    PlusMinus: '\\u00B1',\n    Precedes: '\\u227A',\n    PrecedesEqual: '\\u2AAF',\n    PrecedesSlantEqual: '\\u227C',\n    PrecedesTilde: '\\u227E',\n    Product: '\\u220F',\n    Proportional: '\\u221D',\n    Psi: '\\u03A8',\n    Rarr: '\\u21A0',\n    Re: '\\u211C',\n    ReverseEquilibrium: '\\u21CB',\n    RightAngleBracket: '\\u27E9',\n    RightArrow: '\\u2192',\n    RightArrowLeftArrow: '\\u21C4',\n    RightCeiling: '\\u2309',\n    RightDownVector: '\\u21C2',\n    RightFloor: '\\u230B',\n    RightTee: '\\u22A2',\n    RightTeeArrow: '\\u21A6',\n    RightTriangle: '\\u22B3',\n    RightTriangleEqual: '\\u22B5',\n    RightUpVector: '\\u21BE',\n    RightVector: '\\u21C0',\n    Rightarrow: '\\u21D2',\n    Rrightarrow: '\\u21DB',\n    Rsh: '\\u21B1',\n    Sigma: '\\u03A3',\n    SmallCircle: '\\u2218',\n    Sqrt: '\\u221A',\n    Square: '\\u25A1',\n    SquareIntersection: '\\u2293',\n    SquareSubset: '\\u228F',\n    SquareSubsetEqual: '\\u2291',\n    SquareSuperset: '\\u2290',\n    SquareSupersetEqual: '\\u2292',\n    SquareUnion: '\\u2294',\n    Star: '\\u22C6',\n    Subset: '\\u22D0',\n    SubsetEqual: '\\u2286',\n    Succeeds: '\\u227B',\n    SucceedsEqual: '\\u2AB0',\n    SucceedsSlantEqual: '\\u227D',\n    SucceedsTilde: '\\u227F',\n    SuchThat: '\\u220B',\n    Sum: '\\u2211',\n    Superset: '\\u2283',\n    SupersetEqual: '\\u2287',\n    Supset: '\\u22D1',\n    Therefore: '\\u2234',\n    Theta: '\\u0398',\n    Tilde: '\\u223C',\n    TildeEqual: '\\u2243',\n    TildeFullEqual: '\\u2245',\n    TildeTilde: '\\u2248',\n    UnderBar: '\\u005F',\n    UnderBrace: '\\u23DF',\n    Union: '\\u22C3',\n    UnionPlus: '\\u228E',\n    UpArrow: '\\u2191',\n    UpDownArrow: '\\u2195',\n    UpTee: '\\u22A5',\n    Uparrow: '\\u21D1',\n    Updownarrow: '\\u21D5',\n    Upsilon: '\\u03A5',\n    Vdash: '\\u22A9',\n    Vee: '\\u22C1',\n    VerticalBar: '\\u2223',\n    VerticalTilde: '\\u2240',\n    Vvdash: '\\u22AA',\n    Wedge: '\\u22C0',\n    Xi: '\\u039E',\n    amp: '\\u0026',\n    acute: '\\u00B4',\n    aleph: '\\u2135',\n    alpha: '\\u03B1',\n    amalg: '\\u2A3F',\n    and: '\\u2227',\n    ang: '\\u2220',\n    angmsd: '\\u2221',\n    angsph: '\\u2222',\n    ape: '\\u224A',\n    backprime: '\\u2035',\n    backsim: '\\u223D',\n    backsimeq: '\\u22CD',\n    beta: '\\u03B2',\n    beth: '\\u2136',\n    between: '\\u226C',\n    bigcirc: '\\u25EF',\n    bigodot: '\\u2A00',\n    bigoplus: '\\u2A01',\n    bigotimes: '\\u2A02',\n    bigsqcup: '\\u2A06',\n    bigstar: '\\u2605',\n    bigtriangledown: '\\u25BD',\n    bigtriangleup: '\\u25B3',\n    biguplus: '\\u2A04',\n    blacklozenge: '\\u29EB',\n    blacktriangle: '\\u25B4',\n    blacktriangledown: '\\u25BE',\n    blacktriangleleft: '\\u25C2',\n    bowtie: '\\u22C8',\n    boxdl: '\\u2510',\n    boxdr: '\\u250C',\n    boxminus: '\\u229F',\n    boxplus: '\\u229E',\n    boxtimes: '\\u22A0',\n    boxul: '\\u2518',\n    boxur: '\\u2514',\n    bsol: '\\u005C',\n    bull: '\\u2022',\n    cap: '\\u2229',\n    check: '\\u2713',\n    chi: '\\u03C7',\n    circ: '\\u02C6',\n    circeq: '\\u2257',\n    circlearrowleft: '\\u21BA',\n    circlearrowright: '\\u21BB',\n    circledR: '\\u00AE',\n    circledS: '\\u24C8',\n    circledast: '\\u229B',\n    circledcirc: '\\u229A',\n    circleddash: '\\u229D',\n    clubs: '\\u2663',\n    colon: '\\u003A',\n    comp: '\\u2201',\n    ctdot: '\\u22EF',\n    cuepr: '\\u22DE',\n    cuesc: '\\u22DF',\n    cularr: '\\u21B6',\n    cup: '\\u222A',\n    curarr: '\\u21B7',\n    curlyvee: '\\u22CE',\n    curlywedge: '\\u22CF',\n    dagger: '\\u2020',\n    daleth: '\\u2138',\n    ddarr: '\\u21CA',\n    deg: '\\u00B0',\n    delta: '\\u03B4',\n    digamma: '\\u03DD',\n    div: '\\u00F7',\n    divideontimes: '\\u22C7',\n    dot: '\\u02D9',\n    doteqdot: '\\u2251',\n    dotplus: '\\u2214',\n    dotsquare: '\\u22A1',\n    dtdot: '\\u22F1',\n    ecir: '\\u2256',\n    efDot: '\\u2252',\n    egs: '\\u2A96',\n    ell: '\\u2113',\n    els: '\\u2A95',\n    empty: '\\u2205',\n    epsi: '\\u03B5',\n    epsiv: '\\u03F5',\n    erDot: '\\u2253',\n    eta: '\\u03B7',\n    eth: '\\u00F0',\n    flat: '\\u266D',\n    fork: '\\u22D4',\n    frown: '\\u2322',\n    gEl: '\\u2A8C',\n    gamma: '\\u03B3',\n    gap: '\\u2A86',\n    gimel: '\\u2137',\n    gnE: '\\u2269',\n    gnap: '\\u2A8A',\n    gne: '\\u2A88',\n    gnsim: '\\u22E7',\n    gt: '\\u003E',\n    gtdot: '\\u22D7',\n    harrw: '\\u21AD',\n    hbar: '\\u210F',\n    hellip: '\\u2026',\n    hookleftarrow: '\\u21A9',\n    hookrightarrow: '\\u21AA',\n    imath: '\\u0131',\n    infin: '\\u221E',\n    intcal: '\\u22BA',\n    iota: '\\u03B9',\n    jmath: '\\u0237',\n    kappa: '\\u03BA',\n    kappav: '\\u03F0',\n    lEg: '\\u2A8B',\n    lambda: '\\u03BB',\n    lap: '\\u2A85',\n    larrlp: '\\u21AB',\n    larrtl: '\\u21A2',\n    lbrace: '\\u007B',\n    lbrack: '\\u005B',\n    le: '\\u2264',\n    leftleftarrows: '\\u21C7',\n    leftthreetimes: '\\u22CB',\n    lessdot: '\\u22D6',\n    lmoust: '\\u23B0',\n    lnE: '\\u2268',\n    lnap: '\\u2A89',\n    lne: '\\u2A87',\n    lnsim: '\\u22E6',\n    longmapsto: '\\u27FC',\n    looparrowright: '\\u21AC',\n    lowast: '\\u2217',\n    loz: '\\u25CA',\n    lt: '\\u003C',\n    ltimes: '\\u22C9',\n    ltri: '\\u25C3',\n    macr: '\\u00AF',\n    malt: '\\u2720',\n    mho: '\\u2127',\n    mu: '\\u03BC',\n    multimap: '\\u22B8',\n    nLeftarrow: '\\u21CD',\n    nLeftrightarrow: '\\u21CE',\n    nRightarrow: '\\u21CF',\n    nVDash: '\\u22AF',\n    nVdash: '\\u22AE',\n    natur: '\\u266E',\n    nearr: '\\u2197',\n    nharr: '\\u21AE',\n    nlarr: '\\u219A',\n    not: '\\u00AC',\n    nrarr: '\\u219B',\n    nu: '\\u03BD',\n    nvDash: '\\u22AD',\n    nvdash: '\\u22AC',\n    nwarr: '\\u2196',\n    omega: '\\u03C9',\n    omicron: '\\u03BF',\n    or: '\\u2228',\n    osol: '\\u2298',\n    period: '\\u002E',\n    phi: '\\u03C6',\n    phiv: '\\u03D5',\n    pi: '\\u03C0',\n    piv: '\\u03D6',\n    prap: '\\u2AB7',\n    precnapprox: '\\u2AB9',\n    precneqq: '\\u2AB5',\n    precnsim: '\\u22E8',\n    prime: '\\u2032',\n    psi: '\\u03C8',\n    quot: '\\u0022',\n    rarrtl: '\\u21A3',\n    rbrace: '\\u007D',\n    rbrack: '\\u005D',\n    rho: '\\u03C1',\n    rhov: '\\u03F1',\n    rightrightarrows: '\\u21C9',\n    rightthreetimes: '\\u22CC',\n    ring: '\\u02DA',\n    rmoust: '\\u23B1',\n    rtimes: '\\u22CA',\n    rtri: '\\u25B9',\n    scap: '\\u2AB8',\n    scnE: '\\u2AB6',\n    scnap: '\\u2ABA',\n    scnsim: '\\u22E9',\n    sdot: '\\u22C5',\n    searr: '\\u2198',\n    sect: '\\u00A7',\n    sharp: '\\u266F',\n    sigma: '\\u03C3',\n    sigmav: '\\u03C2',\n    simne: '\\u2246',\n    smile: '\\u2323',\n    spades: '\\u2660',\n    sub: '\\u2282',\n    subE: '\\u2AC5',\n    subnE: '\\u2ACB',\n    subne: '\\u228A',\n    supE: '\\u2AC6',\n    supnE: '\\u2ACC',\n    supne: '\\u228B',\n    swarr: '\\u2199',\n    tau: '\\u03C4',\n    theta: '\\u03B8',\n    thetav: '\\u03D1',\n    tilde: '\\u02DC',\n    times: '\\u00D7',\n    triangle: '\\u25B5',\n    triangleq: '\\u225C',\n    upsi: '\\u03C5',\n    upuparrows: '\\u21C8',\n    veebar: '\\u22BB',\n    vellip: '\\u22EE',\n    weierp: '\\u2118',\n    xi: '\\u03BE',\n    yen: '\\u00A5',\n    zeta: '\\u03B6',\n    zigrarr: '\\u21DD',\n    nbsp: '\\u00A0',\n    rsquo: '\\u2019',\n    lsquo: '\\u2018'\n};\nvar loaded = {};\nfunction add(additions, file) {\n    Object.assign(exports.entities, additions);\n    loaded[file] = true;\n}\nexports.add = add;\nfunction remove(entity) {\n    delete exports.entities[entity];\n}\nexports.remove = remove;\nfunction translate(text) {\n    return text.replace(/&([a-z][a-z0-9]*|#(?:[0-9]+|x[0-9a-f]+));/ig, replace);\n}\nexports.translate = translate;\nfunction replace(match, entity) {\n    if (entity.charAt(0) === '#') {\n        return numeric(entity.slice(1));\n    }\n    if (exports.entities[entity]) {\n        return exports.entities[entity];\n    }\n    if (exports.options['loadMissingEntities']) {\n        var file = (entity.match(/^[a-zA-Z](fr|scr|opf)$/) ? RegExp.$1 : entity.charAt(0).toLowerCase());\n        if (!loaded[file]) {\n            loaded[file] = true;\n            (0, Retries_js_1.retryAfter)((0, AsyncLoad_js_1.asyncLoad)('./util/entities/' + file + '.js'));\n        }\n    }\n    return match;\n}\nfunction numeric(entity) {\n    var n = (entity.charAt(0) === 'x' ?\n        parseInt(entity.slice(1), 16) :\n        parseInt(entity));\n    return String.fromCodePoint(n);\n}\nexports.numeric = numeric;\n//# sourceMappingURL=Entities.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.FunctionList = void 0;\nvar PrioritizedList_js_1 = require(\"./PrioritizedList.js\");\nvar FunctionList = (function (_super) {\n    __extends(FunctionList, _super);\n    function FunctionList() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    FunctionList.prototype.execute = function () {\n        var e_1, _a;\n        var data = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            data[_i] = arguments[_i];\n        }\n        try {\n            for (var _b = __values(this), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var item = _c.value;\n                var result = item.item.apply(item, __spreadArray([], __read(data), false));\n                if (result === false) {\n                    return false;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return true;\n    };\n    FunctionList.prototype.asyncExecute = function () {\n        var data = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            data[_i] = arguments[_i];\n        }\n        var i = -1;\n        var items = this.items;\n        return new Promise(function (ok, fail) {\n            (function execute() {\n                var _a;\n                while (++i < items.length) {\n                    var result = (_a = items[i]).item.apply(_a, __spreadArray([], __read(data), false));\n                    if (result instanceof Promise) {\n                        result.then(execute).catch(function (err) { return fail(err); });\n                        return;\n                    }\n                    if (result === false) {\n                        ok(false);\n                        return;\n                    }\n                }\n                ok(true);\n            })();\n        });\n    };\n    return FunctionList;\n}(PrioritizedList_js_1.PrioritizedList));\nexports.FunctionList = FunctionList;\n//# sourceMappingURL=FunctionList.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PrioritizedList = void 0;\nvar PrioritizedList = (function () {\n    function PrioritizedList() {\n        this.items = [];\n        this.items = [];\n    }\n    PrioritizedList.prototype[Symbol.iterator] = function () {\n        var i = 0;\n        var items = this.items;\n        return {\n            next: function () {\n                return { value: items[i++], done: (i > items.length) };\n            }\n        };\n    };\n    PrioritizedList.prototype.add = function (item, priority) {\n        if (priority === void 0) { priority = PrioritizedList.DEFAULTPRIORITY; }\n        var i = this.items.length;\n        do {\n            i--;\n        } while (i >= 0 && priority < this.items[i].priority);\n        this.items.splice(i + 1, 0, { item: item, priority: priority });\n        return item;\n    };\n    PrioritizedList.prototype.remove = function (item) {\n        var i = this.items.length;\n        do {\n            i--;\n        } while (i >= 0 && this.items[i].item !== item);\n        if (i >= 0) {\n            this.items.splice(i, 1);\n        }\n    };\n    PrioritizedList.DEFAULTPRIORITY = 5;\n    return PrioritizedList;\n}());\nexports.PrioritizedList = PrioritizedList;\n//# sourceMappingURL=PrioritizedList.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.retryAfter = exports.handleRetriesFor = void 0;\nfunction handleRetriesFor(code) {\n    return new Promise(function run(ok, fail) {\n        try {\n            ok(code());\n        }\n        catch (err) {\n            if (err.retry && err.retry instanceof Promise) {\n                err.retry.then(function () { return run(ok, fail); })\n                    .catch(function (perr) { return fail(perr); });\n            }\n            else if (err.restart && err.restart.isCallback) {\n                MathJax.Callback.After(function () { return run(ok, fail); }, err.restart);\n            }\n            else {\n                fail(err);\n            }\n        }\n    });\n}\nexports.handleRetriesFor = handleRetriesFor;\nfunction retryAfter(promise) {\n    var err = new Error('MathJax retry');\n    err.retry = promise;\n    throw err;\n}\nexports.retryAfter = retryAfter;\n//# sourceMappingURL=Retries.js.map"], "names": [], "sourceRoot": ""}