{"version": 3, "file": "9060.d564b58af7791af334db.js?v=d564b58af7791af334db", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA,YAAY;AACZ;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/http.js"], "sourcesContent": ["function failFirstLine(stream, state) {\n  stream.skipToEnd();\n  state.cur = header;\n  return \"error\";\n}\n\nfunction start(stream, state) {\n  if (stream.match(/^HTTP\\/\\d\\.\\d/)) {\n    state.cur = responseStatusCode;\n    return \"keyword\";\n  } else if (stream.match(/^[A-Z]+/) && /[ \\t]/.test(stream.peek())) {\n    state.cur = requestPath;\n    return \"keyword\";\n  } else {\n    return failFirstLine(stream, state);\n  }\n}\n\nfunction responseStatusCode(stream, state) {\n  var code = stream.match(/^\\d+/);\n  if (!code) return failFirstLine(stream, state);\n\n  state.cur = responseStatusText;\n  var status = Number(code[0]);\n  if (status >= 100 && status < 400) {\n    return \"atom\";\n  } else {\n    return \"error\";\n  }\n}\n\nfunction responseStatusText(stream, state) {\n  stream.skipToEnd();\n  state.cur = header;\n  return null;\n}\n\nfunction requestPath(stream, state) {\n  stream.eatWhile(/\\S/);\n  state.cur = requestProtocol;\n  return \"string.special\";\n}\n\nfunction requestProtocol(stream, state) {\n  if (stream.match(/^HTTP\\/\\d\\.\\d$/)) {\n    state.cur = header;\n    return \"keyword\";\n  } else {\n    return failFirstLine(stream, state);\n  }\n}\n\nfunction header(stream) {\n  if (stream.sol() && !stream.eat(/[ \\t]/)) {\n    if (stream.match(/^.*?:/)) {\n      return \"atom\";\n    } else {\n      stream.skipToEnd();\n      return \"error\";\n    }\n  } else {\n    stream.skipToEnd();\n    return \"string\";\n  }\n}\n\nfunction body(stream) {\n  stream.skipToEnd();\n  return null;\n}\n\nexport const http = {\n  name: \"http\",\n  token: function(stream, state) {\n    var cur = state.cur;\n    if (cur != header && cur != body && stream.eatSpace()) return null;\n    return cur(stream, state);\n  },\n\n  blankLine: function(state) {\n    state.cur = body;\n  },\n\n  startState: function() {\n    return {cur: start};\n  }\n};\n"], "names": [], "sourceRoot": ""}