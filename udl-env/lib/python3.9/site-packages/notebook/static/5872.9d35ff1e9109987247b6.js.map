{"version": 3, "file": "5872.9d35ff1e9109987247b6.js?v=9d35ff1e9109987247b6", "mappings": ";;;;;;;;;;;;;AAE8B;AAGA;AAKA;;AAE9B;AAC2C;AAC3C;AACA,yBAAyB,qEAAM;AAC/B,sBAAsB,mEAAK;AAC3B,IAAI,8DAAG;AACP,GAAG;AACH;;AAEA;AACA,wBAAwB,SAAS,yEAAe;AAChD,iCAAiC,qEAAM;AACvC;AACA;AACA;;AAEA;AACA,2BAA2B,qEAAM;AACjC,EAAE,8DAAG;AACL,cAAc,8EAAgB;AAC9B,EAAE,+EAAgB;AAClB;AACA,2IAA2I,QAAQ;AACnJ,CAAC;AACD,iBAAiB;;AAEjB;AACA;AACA;AACA;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/infoDiagram-PH2N3AL5.mjs"], "sourcesContent": ["import {\n  package_default\n} from \"./chunk-5NNNAHNI.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/info/infoParser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"info\", input);\n    log.debug(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/info/infoDb.ts\nvar DEFAULT_INFO_DB = { version: package_default.version };\nvar getVersion = /* @__PURE__ */ __name(() => DEFAULT_INFO_DB.version, \"getVersion\");\nvar db = {\n  getVersion\n};\n\n// src/diagrams/info/infoRenderer.ts\nvar draw = /* @__PURE__ */ __name((text, id, version) => {\n  log.debug(\"rendering info diagram\\n\" + text);\n  const svg = selectSvgElement(id);\n  configureSvgSize(svg, 100, 400, true);\n  const group = svg.append(\"g\");\n  group.append(\"text\").attr(\"x\", 100).attr(\"y\", 40).attr(\"class\", \"version\").attr(\"font-size\", 32).style(\"text-anchor\", \"middle\").text(`v${version}`);\n}, \"draw\");\nvar renderer = { draw };\n\n// src/diagrams/info/infoDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}