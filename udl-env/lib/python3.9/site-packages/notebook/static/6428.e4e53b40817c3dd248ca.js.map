{"version": 3, "file": "6428.e4e53b40817c3dd248ca.js?v=e4e53b40817c3dd248ca", "mappings": ";;;;;;;;;;;;;;;;;;;;AAG8B;AAKA;AACA;AACA;AACA;AACA;AAGA;AAQA;;AAE9B;AACA;AACA,0BAA0B,qEAAM;AAChC,sBAAsB,gBAAgB,KAAK;AAC3C;AACA,GAAG;AACH;AACA,2BAA2B,qEAAM;AACjC,KAAK;AACL,UAAU;AACV,gBAAgB,kXAAkX;AAClY,kBAAkB,8LAA8L;AAChN;AACA,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,cAAc;AAC1C;AACA;AACA;AACA,4BAA4B,eAAe;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,cAAc;AAC1C;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,cAAc,qCAAqC,IAAI,QAAQ,IAAI,WAAW,IAAI,oCAAoC,IAAI,uGAAuG,oBAAoB,WAAW,oCAAoC,gGAAgG,IAAI,4FAA4F,IAAI,iCAAiC,oBAAoB,oEAAoE,sCAAsC,iBAAiB,yFAAyF,aAAa,KAAK,aAAa,IAAI,iCAAiC,IAAI,gGAAgG,oBAAoB,iBAAiB,sEAAsE,iBAAiB,uDAAuD,SAAS,sBAAsB,aAAa,IAAI,aAAa,oBAAoB,iBAAiB,uDAAuD,SAAS,sBAAsB,aAAa;AACjxC,sBAAsB,sBAAsB;AAC5C,gCAAgC,qEAAM;AACtC;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,KAAK;AACL,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ,iEAAiE;AACjE;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA,OAAO;AACP;AACA,8BAA8B,qEAAM;AACpC;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA,OAAO;AACP;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,gBAAgB;AAChB;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP,4DAA4D;AAC5D,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA,OAAO;AACP;AACA,sCAAsC,qEAAM;AAC5C;AACA,OAAO;AACP,iBAAiB,0BAA0B;AAC3C,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,sBAAsB,uDAAuD,gBAAgB,sOAAsO,EAAE,6DAA6D,EAAE,+JAA+J,EAAE,wEAAwE;AAC7mB,oBAAoB,yBAAyB,iCAAiC,oBAAoB,qCAAqC,iBAAiB,wCAAwC,aAAa,sCAAsC,YAAY,uCAAuC,aAAa,uCAAuC,YAAY,uCAAuC,YAAY,+EAA+E,eAAe;AACvf;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA,iCAAiC,QAAQ;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA,CAAC;AACD,8BAA8B,qEAAM;AACpC;AACA;AACA;AACA,eAAe,yEAAS;AACxB;AACA;AACA;AACA,aAAa,2EAAY;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,2EAAY;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,+BAA+B,UAAU,yEAAS;AAC7D,CAAC;AACD,8BAA8B,qEAAM;AACpC,eAAe,yEAAS;AACxB,yCAAyC,gFAAqB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,2EAAY;AACpB;AACA,WAAW,2EAAY;AACvB,yCAAyC,gFAAqB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,sBAAsB;AACzC,MAAM;AACN;AACA;AACA,gBAAgB,kEAAI,aAAa,QAAQ,qEAAW,EAAE;AACtD;AACA,wCAAwC,UAAU;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,qEAAM;AACpC,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,CAAC;AACD,sCAAsC,qEAAM;AAC5C;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA,iBAAiB,yEAAS;AAC1B;AACA;AACA,gBAAgB,2EAAY;AAC5B;AACA;AACA,sBAAsB,2EAAY;AAClC;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM,OAAO,8DAAG;AAChD,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,2BAA2B,qEAAM;AACjC,EAAE,8DAAG;AACL;AACA;AACA,eAAe,yEAAS;AACxB;AACA,cAAc,8EAAgB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,4EAAa;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,yEAAU,oBAAoB,cAAc;AACvE;AACA;AACA,YAAY,2EAAY;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gFAAiB;AACnB;AACA;AACA,6BAA6B,gFAAqB;AAClD,iCAAiC,gFAAqB;AACtD;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACiD;AACjD,kCAAkC,qEAAM;AACxC;AACA,kBAAkB,+BAA+B;AACjD;AACA,QAAQ,2DAAM;AACd,iCAAiC,2DAAO;AACxC,MAAM;AACN,iCAAiC,4DAAM;AACvC;AACA;AACA,mCAAmC,qEAAM,sCAAsC,4DAAM,iBAAiB,2DAAO;AAC7G,kBAAkB,+BAA+B;AACjD;AACA;AACA,eAAe,OAAO,iBAAiB,OAAO,iBAAiB,OAAO,mBAAmB,OAAO,oBAAoB,OAAO;AAC3H,cAAc;AACd,gBAAgB;;AAEhB;AACA,eAAe,OAAO;AACtB,aAAa;AACb;AACA,iBAAiB;AACjB;AACA,eAAe;AACf;AACA,oBAAoB;AACpB,gBAAgB;AAChB;AACA,kBAAkB;AAClB,sBAAsB;AACtB;AACA,eAAe,OAAO;AACtB,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ,cAAc;AACd;AACA;;AAEA;AACA,YAAY;AACZ,cAAc;AACd;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,IAAI;AACJ;AACA,YAAY;AACZ;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/kanban-definition-NDS4AKOZ.mjs"], "sourcesContent": ["import {\n  JSON_SCHEMA,\n  load\n} from \"./chunk-6JRP7KZX.mjs\";\nimport {\n  insertCluster,\n  insertNode,\n  positionNode\n} from \"./chunk-HRU6DDCH.mjs\";\nimport \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  defaultConfig_default,\n  getConfig2 as getConfig,\n  log,\n  sanitizeText,\n  setupGraphViewbox\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/kanban/parser/kanban.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 4], $V1 = [1, 13], $V2 = [1, 12], $V3 = [1, 15], $V4 = [1, 16], $V5 = [1, 20], $V6 = [1, 19], $V7 = [6, 7, 8], $V8 = [1, 26], $V9 = [1, 24], $Va = [1, 25], $Vb = [6, 7, 11], $Vc = [1, 31], $Vd = [6, 7, 11, 24], $Ve = [1, 6, 13, 16, 17, 20, 23], $Vf = [1, 35], $Vg = [1, 36], $Vh = [1, 6, 7, 11, 13, 16, 17, 20, 23], $Vi = [1, 38];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"mindMap\": 4, \"spaceLines\": 5, \"SPACELINE\": 6, \"NL\": 7, \"KANBAN\": 8, \"document\": 9, \"stop\": 10, \"EOF\": 11, \"statement\": 12, \"SPACELIST\": 13, \"node\": 14, \"shapeData\": 15, \"ICON\": 16, \"CLASS\": 17, \"nodeWithId\": 18, \"nodeWithoutId\": 19, \"NODE_DSTART\": 20, \"NODE_DESCR\": 21, \"NODE_DEND\": 22, \"NODE_ID\": 23, \"SHAPE_DATA\": 24, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 6: \"SPACELINE\", 7: \"NL\", 8: \"KANBAN\", 11: \"EOF\", 13: \"SPACELIST\", 16: \"ICON\", 17: \"CLASS\", 20: \"NODE_DSTART\", 21: \"NODE_DESCR\", 22: \"NODE_DEND\", 23: \"NODE_ID\", 24: \"SHAPE_DATA\" },\n    productions_: [0, [3, 1], [3, 2], [5, 1], [5, 2], [5, 2], [4, 2], [4, 3], [10, 1], [10, 1], [10, 1], [10, 2], [10, 2], [9, 3], [9, 2], [12, 3], [12, 2], [12, 2], [12, 2], [12, 1], [12, 2], [12, 1], [12, 1], [12, 1], [12, 1], [14, 1], [14, 1], [19, 3], [18, 1], [18, 4], [15, 2], [15, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 6:\n        case 7:\n          return yy;\n          break;\n        case 8:\n          yy.getLogger().trace(\"Stop NL \");\n          break;\n        case 9:\n          yy.getLogger().trace(\"Stop EOF \");\n          break;\n        case 11:\n          yy.getLogger().trace(\"Stop NL2 \");\n          break;\n        case 12:\n          yy.getLogger().trace(\"Stop EOF2 \");\n          break;\n        case 15:\n          yy.getLogger().info(\"Node: \", $$[$0 - 1].id);\n          yy.addNode($$[$0 - 2].length, $$[$0 - 1].id, $$[$0 - 1].descr, $$[$0 - 1].type, $$[$0]);\n          break;\n        case 16:\n          yy.getLogger().info(\"Node: \", $$[$0].id);\n          yy.addNode($$[$0 - 1].length, $$[$0].id, $$[$0].descr, $$[$0].type);\n          break;\n        case 17:\n          yy.getLogger().trace(\"Icon: \", $$[$0]);\n          yy.decorateNode({ icon: $$[$0] });\n          break;\n        case 18:\n        case 23:\n          yy.decorateNode({ class: $$[$0] });\n          break;\n        case 19:\n          yy.getLogger().trace(\"SPACELIST\");\n          break;\n        case 20:\n          yy.getLogger().trace(\"Node: \", $$[$0 - 1].id);\n          yy.addNode(0, $$[$0 - 1].id, $$[$0 - 1].descr, $$[$0 - 1].type, $$[$0]);\n          break;\n        case 21:\n          yy.getLogger().trace(\"Node: \", $$[$0].id);\n          yy.addNode(0, $$[$0].id, $$[$0].descr, $$[$0].type);\n          break;\n        case 22:\n          yy.decorateNode({ icon: $$[$0] });\n          break;\n        case 27:\n          yy.getLogger().trace(\"node found ..\", $$[$0 - 2]);\n          this.$ = { id: $$[$0 - 1], descr: $$[$0 - 1], type: yy.getType($$[$0 - 2], $$[$0]) };\n          break;\n        case 28:\n          this.$ = { id: $$[$0], descr: $$[$0], type: 0 };\n          break;\n        case 29:\n          yy.getLogger().trace(\"node found ..\", $$[$0 - 3]);\n          this.$ = { id: $$[$0 - 3], descr: $$[$0 - 1], type: yy.getType($$[$0 - 2], $$[$0]) };\n          break;\n        case 30:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n        case 31:\n          this.$ = $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 5: 3, 6: [1, 5], 8: $V0 }, { 1: [3] }, { 1: [2, 1] }, { 4: 6, 6: [1, 7], 7: [1, 8], 8: $V0 }, { 6: $V1, 7: [1, 10], 9: 9, 12: 11, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, o($V7, [2, 3]), { 1: [2, 2] }, o($V7, [2, 4]), o($V7, [2, 5]), { 1: [2, 6], 6: $V1, 12: 21, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, { 6: $V1, 9: 22, 12: 11, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, { 6: $V8, 7: $V9, 10: 23, 11: $Va }, o($Vb, [2, 24], { 18: 17, 19: 18, 14: 27, 16: [1, 28], 17: [1, 29], 20: $V5, 23: $V6 }), o($Vb, [2, 19]), o($Vb, [2, 21], { 15: 30, 24: $Vc }), o($Vb, [2, 22]), o($Vb, [2, 23]), o($Vd, [2, 25]), o($Vd, [2, 26]), o($Vd, [2, 28], { 20: [1, 32] }), { 21: [1, 33] }, { 6: $V8, 7: $V9, 10: 34, 11: $Va }, { 1: [2, 7], 6: $V1, 12: 21, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, o($Ve, [2, 14], { 7: $Vf, 11: $Vg }), o($Vh, [2, 8]), o($Vh, [2, 9]), o($Vh, [2, 10]), o($Vb, [2, 16], { 15: 37, 24: $Vc }), o($Vb, [2, 17]), o($Vb, [2, 18]), o($Vb, [2, 20], { 24: $Vi }), o($Vd, [2, 31]), { 21: [1, 39] }, { 22: [1, 40] }, o($Ve, [2, 13], { 7: $Vf, 11: $Vg }), o($Vh, [2, 11]), o($Vh, [2, 12]), o($Vb, [2, 15], { 24: $Vi }), o($Vd, [2, 30]), { 22: [1, 41] }, o($Vd, [2, 27]), o($Vd, [2, 29])],\n    defaultActions: { 2: [2, 1], 6: [2, 2] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.pushState(\"shapeData\");\n            yy_.yytext = \"\";\n            return 24;\n            break;\n          case 1:\n            this.pushState(\"shapeDataStr\");\n            return 24;\n            break;\n          case 2:\n            this.popState();\n            return 24;\n            break;\n          case 3:\n            const re = /\\n\\s*/g;\n            yy_.yytext = yy_.yytext.replace(re, \"<br/>\");\n            return 24;\n            break;\n          case 4:\n            return 24;\n            break;\n          case 5:\n            this.popState();\n            break;\n          case 6:\n            yy.getLogger().trace(\"Found comment\", yy_.yytext);\n            return 6;\n            break;\n          case 7:\n            return 8;\n            break;\n          case 8:\n            this.begin(\"CLASS\");\n            break;\n          case 9:\n            this.popState();\n            return 17;\n            break;\n          case 10:\n            this.popState();\n            break;\n          case 11:\n            yy.getLogger().trace(\"Begin icon\");\n            this.begin(\"ICON\");\n            break;\n          case 12:\n            yy.getLogger().trace(\"SPACELINE\");\n            return 6;\n            break;\n          case 13:\n            return 7;\n            break;\n          case 14:\n            return 16;\n            break;\n          case 15:\n            yy.getLogger().trace(\"end icon\");\n            this.popState();\n            break;\n          case 16:\n            yy.getLogger().trace(\"Exploding node\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 17:\n            yy.getLogger().trace(\"Cloud\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 18:\n            yy.getLogger().trace(\"Explosion Bang\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 19:\n            yy.getLogger().trace(\"Cloud Bang\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 20:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 21:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 22:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 23:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 24:\n            return 13;\n            break;\n          case 25:\n            return 23;\n            break;\n          case 26:\n            return 11;\n            break;\n          case 27:\n            this.begin(\"NSTR2\");\n            break;\n          case 28:\n            return \"NODE_DESCR\";\n            break;\n          case 29:\n            this.popState();\n            break;\n          case 30:\n            yy.getLogger().trace(\"Starting NSTR\");\n            this.begin(\"NSTR\");\n            break;\n          case 31:\n            yy.getLogger().trace(\"description:\", yy_.yytext);\n            return \"NODE_DESCR\";\n            break;\n          case 32:\n            this.popState();\n            break;\n          case 33:\n            this.popState();\n            yy.getLogger().trace(\"node end ))\");\n            return \"NODE_DEND\";\n            break;\n          case 34:\n            this.popState();\n            yy.getLogger().trace(\"node end )\");\n            return \"NODE_DEND\";\n            break;\n          case 35:\n            this.popState();\n            yy.getLogger().trace(\"node end ...\", yy_.yytext);\n            return \"NODE_DEND\";\n            break;\n          case 36:\n            this.popState();\n            yy.getLogger().trace(\"node end ((\");\n            return \"NODE_DEND\";\n            break;\n          case 37:\n            this.popState();\n            yy.getLogger().trace(\"node end (-\");\n            return \"NODE_DEND\";\n            break;\n          case 38:\n            this.popState();\n            yy.getLogger().trace(\"node end (-\");\n            return \"NODE_DEND\";\n            break;\n          case 39:\n            this.popState();\n            yy.getLogger().trace(\"node end ((\");\n            return \"NODE_DEND\";\n            break;\n          case 40:\n            this.popState();\n            yy.getLogger().trace(\"node end ((\");\n            return \"NODE_DEND\";\n            break;\n          case 41:\n            yy.getLogger().trace(\"Long description:\", yy_.yytext);\n            return 21;\n            break;\n          case 42:\n            yy.getLogger().trace(\"Long description:\", yy_.yytext);\n            return 21;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:@\\{)/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\\\"]+)/i, /^(?:[^}^\"]+)/i, /^(?:\\})/i, /^(?:\\s*%%.*)/i, /^(?:kanban\\b)/i, /^(?::::)/i, /^(?:.+)/i, /^(?:\\n)/i, /^(?:::icon\\()/i, /^(?:[\\s]+[\\n])/i, /^(?:[\\n]+)/i, /^(?:[^\\)]+)/i, /^(?:\\))/i, /^(?:-\\))/i, /^(?:\\(-)/i, /^(?:\\)\\))/i, /^(?:\\))/i, /^(?:\\(\\()/i, /^(?:\\{\\{)/i, /^(?:\\()/i, /^(?:\\[)/i, /^(?:[\\s]+)/i, /^(?:[^\\(\\[\\n\\)\\{\\}@]+)/i, /^(?:$)/i, /^(?:[\"][`])/i, /^(?:[^`\"]+)/i, /^(?:[`][\"])/i, /^(?:[\"])/i, /^(?:[^\"]+)/i, /^(?:[\"])/i, /^(?:[\\)]\\))/i, /^(?:[\\)])/i, /^(?:[\\]])/i, /^(?:\\}\\})/i, /^(?:\\(-)/i, /^(?:-\\))/i, /^(?:\\(\\()/i, /^(?:\\()/i, /^(?:[^\\)\\]\\(\\}]+)/i, /^(?:.+(?!\\(\\())/i],\n      conditions: { \"shapeDataEndBracket\": { \"rules\": [], \"inclusive\": false }, \"shapeDataStr\": { \"rules\": [2, 3], \"inclusive\": false }, \"shapeData\": { \"rules\": [1, 4, 5], \"inclusive\": false }, \"CLASS\": { \"rules\": [9, 10], \"inclusive\": false }, \"ICON\": { \"rules\": [14, 15], \"inclusive\": false }, \"NSTR2\": { \"rules\": [28, 29], \"inclusive\": false }, \"NSTR\": { \"rules\": [31, 32], \"inclusive\": false }, \"NODE\": { \"rules\": [27, 30, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 6, 7, 8, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar kanban_default = parser;\n\n// src/diagrams/kanban/kanbanDb.ts\nvar nodes = [];\nvar sections = [];\nvar cnt = 0;\nvar elements = {};\nvar clear = /* @__PURE__ */ __name(() => {\n  nodes = [];\n  sections = [];\n  cnt = 0;\n  elements = {};\n}, \"clear\");\nvar getSection = /* @__PURE__ */ __name((level) => {\n  if (nodes.length === 0) {\n    return null;\n  }\n  const sectionLevel = nodes[0].level;\n  let lastSection = null;\n  for (let i = nodes.length - 1; i >= 0; i--) {\n    if (nodes[i].level === sectionLevel && !lastSection) {\n      lastSection = nodes[i];\n    }\n    if (nodes[i].level < sectionLevel) {\n      throw new Error('Items without section detected, found section (\"' + nodes[i].label + '\")');\n    }\n  }\n  if (level === lastSection?.level) {\n    return null;\n  }\n  return lastSection;\n}, \"getSection\");\nvar getSections = /* @__PURE__ */ __name(function() {\n  return sections;\n}, \"getSections\");\nvar getData = /* @__PURE__ */ __name(function() {\n  const edges = [];\n  const _nodes = [];\n  const sections2 = getSections();\n  const conf = getConfig();\n  for (const section of sections2) {\n    const node = {\n      id: section.id,\n      label: sanitizeText(section.label ?? \"\", conf),\n      isGroup: true,\n      ticket: section.ticket,\n      shape: \"kanbanSection\",\n      level: section.level,\n      look: conf.look\n    };\n    _nodes.push(node);\n    const children = nodes.filter((n) => n.parentId === section.id);\n    for (const item of children) {\n      const childNode = {\n        id: item.id,\n        parentId: section.id,\n        label: sanitizeText(item.label ?? \"\", conf),\n        isGroup: false,\n        ticket: item?.ticket,\n        priority: item?.priority,\n        assigned: item?.assigned,\n        icon: item?.icon,\n        shape: \"kanbanItem\",\n        level: item.level,\n        rx: 5,\n        ry: 5,\n        cssStyles: [\"text-align: left\"]\n      };\n      _nodes.push(childNode);\n    }\n  }\n  return { nodes: _nodes, edges, other: {}, config: getConfig() };\n}, \"getData\");\nvar addNode = /* @__PURE__ */ __name((level, id, descr, type, shapeData) => {\n  const conf = getConfig();\n  let padding = conf.mindmap?.padding ?? defaultConfig_default.mindmap.padding;\n  switch (type) {\n    case nodeType.ROUNDED_RECT:\n    case nodeType.RECT:\n    case nodeType.HEXAGON:\n      padding *= 2;\n  }\n  const node = {\n    id: sanitizeText(id, conf) || \"kbn\" + cnt++,\n    level,\n    label: sanitizeText(descr, conf),\n    width: conf.mindmap?.maxNodeWidth ?? defaultConfig_default.mindmap.maxNodeWidth,\n    padding,\n    isGroup: false\n  };\n  if (shapeData !== void 0) {\n    let yamlData;\n    if (!shapeData.includes(\"\\n\")) {\n      yamlData = \"{\\n\" + shapeData + \"\\n}\";\n    } else {\n      yamlData = shapeData + \"\\n\";\n    }\n    const doc = load(yamlData, { schema: JSON_SCHEMA });\n    if (doc.shape && (doc.shape !== doc.shape.toLowerCase() || doc.shape.includes(\"_\"))) {\n      throw new Error(`No such shape: ${doc.shape}. Shape names should be lowercase.`);\n    }\n    if (doc?.shape && doc.shape === \"kanbanItem\") {\n      node.shape = doc?.shape;\n    }\n    if (doc?.label) {\n      node.label = doc?.label;\n    }\n    if (doc?.icon) {\n      node.icon = doc?.icon.toString();\n    }\n    if (doc?.assigned) {\n      node.assigned = doc?.assigned.toString();\n    }\n    if (doc?.ticket) {\n      node.ticket = doc?.ticket.toString();\n    }\n    if (doc?.priority) {\n      node.priority = doc?.priority;\n    }\n  }\n  const section = getSection(level);\n  if (section) {\n    node.parentId = section.id || \"kbn\" + cnt++;\n  } else {\n    sections.push(node);\n  }\n  nodes.push(node);\n}, \"addNode\");\nvar nodeType = {\n  DEFAULT: 0,\n  NO_BORDER: 0,\n  ROUNDED_RECT: 1,\n  RECT: 2,\n  CIRCLE: 3,\n  CLOUD: 4,\n  BANG: 5,\n  HEXAGON: 6\n};\nvar getType = /* @__PURE__ */ __name((startStr, endStr) => {\n  log.debug(\"In get type\", startStr, endStr);\n  switch (startStr) {\n    case \"[\":\n      return nodeType.RECT;\n    case \"(\":\n      return endStr === \")\" ? nodeType.ROUNDED_RECT : nodeType.CLOUD;\n    case \"((\":\n      return nodeType.CIRCLE;\n    case \")\":\n      return nodeType.CLOUD;\n    case \"))\":\n      return nodeType.BANG;\n    case \"{{\":\n      return nodeType.HEXAGON;\n    default:\n      return nodeType.DEFAULT;\n  }\n}, \"getType\");\nvar setElementForId = /* @__PURE__ */ __name((id, element) => {\n  elements[id] = element;\n}, \"setElementForId\");\nvar decorateNode = /* @__PURE__ */ __name((decoration) => {\n  if (!decoration) {\n    return;\n  }\n  const config = getConfig();\n  const node = nodes[nodes.length - 1];\n  if (decoration.icon) {\n    node.icon = sanitizeText(decoration.icon, config);\n  }\n  if (decoration.class) {\n    node.cssClasses = sanitizeText(decoration.class, config);\n  }\n}, \"decorateNode\");\nvar type2Str = /* @__PURE__ */ __name((type) => {\n  switch (type) {\n    case nodeType.DEFAULT:\n      return \"no-border\";\n    case nodeType.RECT:\n      return \"rect\";\n    case nodeType.ROUNDED_RECT:\n      return \"rounded-rect\";\n    case nodeType.CIRCLE:\n      return \"circle\";\n    case nodeType.CLOUD:\n      return \"cloud\";\n    case nodeType.BANG:\n      return \"bang\";\n    case nodeType.HEXAGON:\n      return \"hexgon\";\n    // cspell: disable-line\n    default:\n      return \"no-border\";\n  }\n}, \"type2Str\");\nvar getLogger = /* @__PURE__ */ __name(() => log, \"getLogger\");\nvar getElementById = /* @__PURE__ */ __name((id) => elements[id], \"getElementById\");\nvar db = {\n  clear,\n  addNode,\n  getSections,\n  getData,\n  nodeType,\n  getType,\n  setElementForId,\n  decorateNode,\n  type2Str,\n  getLogger,\n  getElementById\n};\nvar kanbanDb_default = db;\n\n// src/diagrams/kanban/kanbanRenderer.ts\nvar draw = /* @__PURE__ */ __name(async (text, id, _version, diagObj) => {\n  log.debug(\"Rendering kanban diagram\\n\" + text);\n  const db2 = diagObj.db;\n  const data4Layout = db2.getData();\n  const conf = getConfig();\n  conf.htmlLabels = false;\n  const svg = selectSvgElement(id);\n  const sectionsElem = svg.append(\"g\");\n  sectionsElem.attr(\"class\", \"sections\");\n  const nodesElem = svg.append(\"g\");\n  nodesElem.attr(\"class\", \"items\");\n  const sections2 = data4Layout.nodes.filter(\n    // TODO: TypeScript 5.5 will infer this predicate automatically\n    (node) => node.isGroup\n  );\n  let cnt2 = 0;\n  const padding = 10;\n  const sectionObjects = [];\n  let maxLabelHeight = 25;\n  for (const section of sections2) {\n    const WIDTH = conf?.kanban?.sectionWidth || 200;\n    cnt2 = cnt2 + 1;\n    section.x = WIDTH * cnt2 + (cnt2 - 1) * padding / 2;\n    section.width = WIDTH;\n    section.y = 0;\n    section.height = WIDTH * 3;\n    section.rx = 5;\n    section.ry = 5;\n    section.cssClasses = section.cssClasses + \" section-\" + cnt2;\n    const sectionObj = await insertCluster(sectionsElem, section);\n    maxLabelHeight = Math.max(maxLabelHeight, sectionObj?.labelBBox?.height);\n    sectionObjects.push(sectionObj);\n  }\n  let i = 0;\n  for (const section of sections2) {\n    const sectionObj = sectionObjects[i];\n    i = i + 1;\n    const WIDTH = conf?.kanban?.sectionWidth || 200;\n    const top = -WIDTH * 3 / 2 + maxLabelHeight;\n    let y = top;\n    const sectionItems = data4Layout.nodes.filter((node) => node.parentId === section.id);\n    for (const item of sectionItems) {\n      if (item.isGroup) {\n        throw new Error(\"Groups within groups are not allowed in Kanban diagrams\");\n      }\n      item.x = section.x;\n      item.width = WIDTH - 1.5 * padding;\n      const nodeEl = await insertNode(nodesElem, item, { config: conf });\n      const bbox = nodeEl.node().getBBox();\n      item.y = y + bbox.height / 2;\n      await positionNode(item);\n      y = item.y + bbox.height / 2 + padding / 2;\n    }\n    const rect = sectionObj.cluster.select(\"rect\");\n    const height = Math.max(y - top + 3 * padding, 50) + (maxLabelHeight - 25);\n    rect.attr(\"height\", height);\n  }\n  setupGraphViewbox(\n    void 0,\n    svg,\n    conf.mindmap?.padding ?? defaultConfig_default.kanban.padding,\n    conf.mindmap?.useMaxWidth ?? defaultConfig_default.kanban.useMaxWidth\n  );\n}, \"draw\");\nvar kanbanRenderer_default = {\n  draw\n};\n\n// src/diagrams/kanban/styles.ts\nimport { darken, lighten, isDark } from \"khroma\";\nvar genSections = /* @__PURE__ */ __name((options) => {\n  let sections2 = \"\";\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options[\"lineColor\" + i] = options[\"lineColor\" + i] || options[\"cScaleInv\" + i];\n    if (isDark(options[\"lineColor\" + i])) {\n      options[\"lineColor\" + i] = lighten(options[\"lineColor\" + i], 20);\n    } else {\n      options[\"lineColor\" + i] = darken(options[\"lineColor\" + i], 20);\n    }\n  }\n  const adjuster = /* @__PURE__ */ __name((color, level) => options.darkMode ? darken(color, level) : lighten(color, level), \"adjuster\");\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = \"\" + (17 - 3 * i);\n    sections2 += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${i - 1} polygon, .section-${i - 1} path  {\n      fill: ${adjuster(options[\"cScale\" + i], 10)};\n      stroke: ${adjuster(options[\"cScale\" + i], 10)};\n\n    }\n    .section-${i - 1} text {\n     fill: ${options[\"cScaleLabel\" + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options[\"cScaleLabel\" + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options[\"cScale\" + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options[\"cScaleInv\" + i]} ;\n      stroke-width: 3;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.background};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .kanban-ticket-link {\n    fill: ${options.background};\n    stroke: ${options.nodeBorder};\n    text-decoration: underline;\n  }\n    `;\n  }\n  return sections2;\n}, \"genSections\");\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .cluster-label, .label {\n    color: ${options.textColor};\n    fill: ${options.textColor};\n    }\n  .kanban-label {\n    dy: 1em;\n    alignment-baseline: middle;\n    text-anchor: middle;\n    dominant-baseline: middle;\n    text-align: center;\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/kanban/kanban-definition.ts\nvar diagram = {\n  db: kanbanDb_default,\n  renderer: kanbanRenderer_default,\n  parser: kanban_default,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}