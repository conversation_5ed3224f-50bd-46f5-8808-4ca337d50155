{"version": 3, "file": "7264.56c0f8b7752822724b0f.js?v=56c0f8b7752822724b0f", "mappings": ";;;;;;;;;;AAAA;AACA;AACA,oCAAoC,OAAO;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,YAAY;AACZ,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,oBAAoB;AACpB;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/eiffel.js"], "sourcesContent": ["function wordObj(words) {\n  var o = {};\n  for (var i = 0, e = words.length; i < e; ++i) o[words[i]] = true;\n  return o;\n}\nvar keywords = wordObj([\n  'note',\n  'across',\n  'when',\n  'variant',\n  'until',\n  'unique',\n  'undefine',\n  'then',\n  'strip',\n  'select',\n  'retry',\n  'rescue',\n  'require',\n  'rename',\n  'reference',\n  'redefine',\n  'prefix',\n  'once',\n  'old',\n  'obsolete',\n  'loop',\n  'local',\n  'like',\n  'is',\n  'inspect',\n  'infix',\n  'include',\n  'if',\n  'frozen',\n  'from',\n  'external',\n  'export',\n  'ensure',\n  'end',\n  'elseif',\n  'else',\n  'do',\n  'creation',\n  'create',\n  'check',\n  'alias',\n  'agent',\n  'separate',\n  'invariant',\n  'inherit',\n  'indexing',\n  'feature',\n  'expanded',\n  'deferred',\n  'class',\n  'Void',\n  'True',\n  'Result',\n  'Precursor',\n  'False',\n  'Current',\n  'create',\n  'attached',\n  'detachable',\n  'as',\n  'and',\n  'implies',\n  'not',\n  'or'\n]);\nvar operators = wordObj([\":=\", \"and then\",\"and\", \"or\",\"<<\",\">>\"]);\n\nfunction chain(newtok, stream, state) {\n  state.tokenize.push(newtok);\n  return newtok(stream, state);\n}\n\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) return null;\n  var ch = stream.next();\n  if (ch == '\"'||ch == \"'\") {\n    return chain(readQuoted(ch, \"string\"), stream, state);\n  } else if (ch == \"-\"&&stream.eat(\"-\")) {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == \":\"&&stream.eat(\"=\")) {\n    return \"operator\";\n  } else if (/[0-9]/.test(ch)) {\n    stream.eatWhile(/[xXbBCc0-9\\.]/);\n    stream.eat(/[\\?\\!]/);\n    return \"variable\";\n  } else if (/[a-zA-Z_0-9]/.test(ch)) {\n    stream.eatWhile(/[a-zA-Z_0-9]/);\n    stream.eat(/[\\?\\!]/);\n    return \"variable\";\n  } else if (/[=+\\-\\/*^%<>~]/.test(ch)) {\n    stream.eatWhile(/[=+\\-\\/*^%<>~]/);\n    return \"operator\";\n  } else {\n    return null;\n  }\n}\n\nfunction readQuoted(quote, style,  unescaped) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && (unescaped || !escaped)) {\n        state.tokenize.pop();\n        break;\n      }\n      escaped = !escaped && ch == \"%\";\n    }\n    return style;\n  };\n}\n\nexport const eiffel = {\n  name: \"eiffel\",\n  startState: function() {\n    return {tokenize: [tokenBase]};\n  },\n\n  token: function(stream, state) {\n    var style = state.tokenize[state.tokenize.length-1](stream, state);\n    if (style == \"variable\") {\n      var word = stream.current();\n      style = keywords.propertyIsEnumerable(stream.current()) ? \"keyword\"\n        : operators.propertyIsEnumerable(stream.current()) ? \"operator\"\n        : /^[A-Z][A-Z_0-9]*$/g.test(word) ? \"tag\"\n        : /^0[bB][0-1]+$/g.test(word) ? \"number\"\n        : /^0[cC][0-7]+$/g.test(word) ? \"number\"\n        : /^0[xX][a-fA-F0-9]+$/g.test(word) ? \"number\"\n        : /^([0-9]+\\.[0-9]*)|([0-9]*\\.[0-9]+)$/g.test(word) ? \"number\"\n        : /^[0-9]+$/g.test(word) ? \"number\"\n        : \"variable\";\n    }\n    return style;\n  },\n  languageData: {\n    commentTokens: {line: \"--\"}\n  }\n};\n\n"], "names": [], "sourceRoot": ""}