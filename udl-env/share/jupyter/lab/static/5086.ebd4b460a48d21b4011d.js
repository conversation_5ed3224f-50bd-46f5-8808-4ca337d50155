"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[5086],{65086:(t,e,a)=>{a.d(e,{diagram:()=>O});var i=a(82107);var r=a(52480);var n=a(35645);var s=a(16864);var d=a(58564);var o=a(43901);var c=a(26424);var g=a(1510);var p=a(80693);var h=a(56273);var l=a(52724);var f=a(6047);var x=a(1218);var u=a(82211);var D=a(84416);var y=(0,f.K2)((t=>t.append("circle").attr("class","start-state").attr("r",(0,f.D7)().state.sizeUnit).attr("cx",(0,f.D7)().state.padding+(0,f.D7)().state.sizeUnit).attr("cy",(0,f.D7)().state.padding+(0,f.D7)().state.sizeUnit)),"drawStartState");var v=(0,f.K2)((t=>t.append("line").style("stroke","grey").style("stroke-dasharray","3").attr("x1",(0,f.D7)().state.textHeight).attr("class","divider").attr("x2",(0,f.D7)().state.textHeight*2).attr("y1",0).attr("y2",0)),"drawDivider");var w=(0,f.K2)(((t,e)=>{const a=t.append("text").attr("x",2*(0,f.D7)().state.padding).attr("y",(0,f.D7)().state.textHeight+2*(0,f.D7)().state.padding).attr("font-size",(0,f.D7)().state.fontSize).attr("class","state-title").text(e.id);const i=a.node().getBBox();t.insert("rect",":first-child").attr("x",(0,f.D7)().state.padding).attr("y",(0,f.D7)().state.padding).attr("width",i.width+2*(0,f.D7)().state.padding).attr("height",i.height+2*(0,f.D7)().state.padding).attr("rx",(0,f.D7)().state.radius);return a}),"drawSimpleState");var m=(0,f.K2)(((t,e)=>{const a=(0,f.K2)((function(t,e,a){const i=t.append("tspan").attr("x",2*(0,f.D7)().state.padding).text(e);if(!a){i.attr("dy",(0,f.D7)().state.textHeight)}}),"addTspan");const i=t.append("text").attr("x",2*(0,f.D7)().state.padding).attr("y",(0,f.D7)().state.textHeight+1.3*(0,f.D7)().state.padding).attr("font-size",(0,f.D7)().state.fontSize).attr("class","state-title").text(e.descriptions[0]);const r=i.node().getBBox();const n=r.height;const s=t.append("text").attr("x",(0,f.D7)().state.padding).attr("y",n+(0,f.D7)().state.padding*.4+(0,f.D7)().state.dividerMargin+(0,f.D7)().state.textHeight).attr("class","state-description");let d=true;let o=true;e.descriptions.forEach((function(t){if(!d){a(s,t,o);o=false}d=false}));const c=t.append("line").attr("x1",(0,f.D7)().state.padding).attr("y1",(0,f.D7)().state.padding+n+(0,f.D7)().state.dividerMargin/2).attr("y2",(0,f.D7)().state.padding+n+(0,f.D7)().state.dividerMargin/2).attr("class","descr-divider");const g=s.node().getBBox();const p=Math.max(g.width,r.width);c.attr("x2",p+3*(0,f.D7)().state.padding);t.insert("rect",":first-child").attr("x",(0,f.D7)().state.padding).attr("y",(0,f.D7)().state.padding).attr("width",p+2*(0,f.D7)().state.padding).attr("height",g.height+n+2*(0,f.D7)().state.padding).attr("rx",(0,f.D7)().state.radius);return t}),"drawDescrState");var b=(0,f.K2)(((t,e,a)=>{const i=(0,f.D7)().state.padding;const r=2*(0,f.D7)().state.padding;const n=t.node().getBBox();const s=n.width;const d=n.x;const o=t.append("text").attr("x",0).attr("y",(0,f.D7)().state.titleShift).attr("font-size",(0,f.D7)().state.fontSize).attr("class","state-title").text(e.id);const c=o.node().getBBox();const g=c.width+r;let p=Math.max(g,s);if(p===s){p=p+r}let h;const l=t.node().getBBox();if(e.doc){}h=d-i;if(g>s){h=(s-p)/2+i}if(Math.abs(d-l.x)<i&&g>s){h=d-(g-s)/2}const x=1-(0,f.D7)().state.textHeight;t.insert("rect",":first-child").attr("x",h).attr("y",x).attr("class",a?"alt-composit":"composit").attr("width",p).attr("height",l.height+(0,f.D7)().state.textHeight+(0,f.D7)().state.titleShift+1).attr("rx","0");o.attr("x",h+i);if(g<=s){o.attr("x",d+(p-r)/2-g/2+i)}t.insert("rect",":first-child").attr("x",h).attr("y",(0,f.D7)().state.titleShift-(0,f.D7)().state.textHeight-(0,f.D7)().state.padding).attr("width",p).attr("height",(0,f.D7)().state.textHeight*3).attr("rx",(0,f.D7)().state.radius);t.insert("rect",":first-child").attr("x",h).attr("y",(0,f.D7)().state.titleShift-(0,f.D7)().state.textHeight-(0,f.D7)().state.padding).attr("width",p).attr("height",l.height+3+2*(0,f.D7)().state.textHeight).attr("rx",(0,f.D7)().state.radius);return t}),"addTitleAndBox");var B=(0,f.K2)((t=>{t.append("circle").attr("class","end-state-outer").attr("r",(0,f.D7)().state.sizeUnit+(0,f.D7)().state.miniPadding).attr("cx",(0,f.D7)().state.padding+(0,f.D7)().state.sizeUnit+(0,f.D7)().state.miniPadding).attr("cy",(0,f.D7)().state.padding+(0,f.D7)().state.sizeUnit+(0,f.D7)().state.miniPadding);return t.append("circle").attr("class","end-state-inner").attr("r",(0,f.D7)().state.sizeUnit).attr("cx",(0,f.D7)().state.padding+(0,f.D7)().state.sizeUnit+2).attr("cy",(0,f.D7)().state.padding+(0,f.D7)().state.sizeUnit+2)}),"drawEndState");var k=(0,f.K2)(((t,e)=>{let a=(0,f.D7)().state.forkWidth;let i=(0,f.D7)().state.forkHeight;if(e.parentId){let t=a;a=i;i=t}return t.append("rect").style("stroke","black").style("fill","black").attr("width",a).attr("height",i).attr("x",(0,f.D7)().state.padding).attr("y",(0,f.D7)().state.padding)}),"drawForkJoinState");var S=(0,f.K2)(((t,e,a,i)=>{let r=0;const n=i.append("text");n.style("text-anchor","start");n.attr("class","noteText");let s=t.replace(/\r\n/g,"<br/>");s=s.replace(/\n/g,"<br/>");const d=s.split(f.Y2.lineBreakRegex);let o=1.25*(0,f.D7)().state.noteMargin;for(const c of d){const t=c.trim();if(t.length>0){const i=n.append("tspan");i.text(t);if(o===0){const t=i.node().getBBox();o+=t.height}r+=o;i.attr("x",e+(0,f.D7)().state.noteMargin);i.attr("y",a+r+1.25*(0,f.D7)().state.noteMargin)}}return{textWidth:n.node().getBBox().width,textHeight:r}}),"_drawLongText");var N=(0,f.K2)(((t,e)=>{e.attr("class","state-note");const a=e.append("rect").attr("x",0).attr("y",(0,f.D7)().state.padding);const i=e.append("g");const{textWidth:r,textHeight:n}=S(t,0,0,i);a.attr("height",n+2*(0,f.D7)().state.noteMargin);a.attr("width",r+(0,f.D7)().state.noteMargin*2);return a}),"drawNote");var E=(0,f.K2)((function(t,e){const a=e.id;const i={id:a,label:e.id,width:0,height:0};const r=t.append("g").attr("id",a).attr("class","stateGroup");if(e.type==="start"){y(r)}if(e.type==="end"){B(r)}if(e.type==="fork"||e.type==="join"){k(r,e)}if(e.type==="note"){N(e.note.text,r)}if(e.type==="divider"){v(r)}if(e.type==="default"&&e.descriptions.length===0){w(r,e)}if(e.type==="default"&&e.descriptions.length>0){m(r,e)}const n=r.node().getBBox();i.width=n.width+2*(0,f.D7)().state.padding;i.height=n.height+2*(0,f.D7)().state.padding;return i}),"drawState");var M=0;var K=(0,f.K2)((function(t,e,a){const r=(0,f.K2)((function(t){switch(t){case i.u4.relationType.AGGREGATION:return"aggregation";case i.u4.relationType.EXTENSION:return"extension";case i.u4.relationType.COMPOSITION:return"composition";case i.u4.relationType.DEPENDENCY:return"dependency"}}),"getRelationType");e.points=e.points.filter((t=>!Number.isNaN(t.y)));const n=e.points;const s=(0,x.n8j)().x((function(t){return t.x})).y((function(t){return t.y})).curve(x.qrM);const d=t.append("path").attr("d",s(n)).attr("id","edge"+M).attr("class","transition");let o="";if((0,f.D7)().state.arrowMarkerAbsolute){o=(0,f.ID)(true)}d.attr("marker-end","url("+o+"#"+r(i.u4.relationType.DEPENDENCY)+"End)");if(a.title!==void 0){const i=t.append("g").attr("class","stateLabel");const{x:r,y:n}=l._K.calcLabelPosition(e.points);const s=f.Y2.getRows(a.title);let d=0;const o=[];let c=0;let g=0;for(let t=0;t<=s.length;t++){const e=i.append("text").attr("text-anchor","middle").text(s[t]).attr("x",r).attr("y",n+d);const a=e.node().getBBox();c=Math.max(c,a.width);g=Math.min(g,a.x);f.Rm.info(a.x,r,n+d);if(d===0){const t=e.node().getBBox();d=t.height;f.Rm.info("Title height",d,n)}o.push(e)}let p=d*s.length;if(s.length>1){const t=(s.length-1)*d*.5;o.forEach(((e,a)=>e.attr("y",n+a*d-t)));p=d*s.length}const h=i.node().getBBox();i.insert("rect",":first-child").attr("class","box").attr("x",r-c/2-(0,f.D7)().state.padding/2).attr("y",n-p/2-(0,f.D7)().state.padding/2-3.5).attr("width",c+(0,f.D7)().state.padding).attr("height",p+(0,f.D7)().state.padding);f.Rm.info(h)}M++}),"drawEdge");var R;var z={};var H=(0,f.K2)((function(){}),"setConf");var T=(0,f.K2)((function(t){t.append("defs").append("marker").attr("id","dependencyEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")}),"insertMarkers");var L=(0,f.K2)((function(t,e,a,i){R=(0,f.D7)().state;const r=(0,f.D7)().securityLevel;let n;if(r==="sandbox"){n=(0,x.Ltv)("#i"+e)}const s=r==="sandbox"?(0,x.Ltv)(n.nodes()[0].contentDocument.body):(0,x.Ltv)("body");const d=r==="sandbox"?n.nodes()[0].contentDocument:document;f.Rm.debug("Rendering diagram "+t);const o=s.select(`[id='${e}']`);T(o);const c=i.db.getRootDoc();G(c,o,void 0,false,s,d,i);const g=R.padding;const p=o.node().getBBox();const h=p.width+g*2;const l=p.height+g*2;const u=h*1.75;(0,f.a$)(o,l,u,R.useMaxWidth);o.attr("viewBox",`${p.x-R.padding}  ${p.y-R.padding} `+h+" "+l)}),"draw");var A=(0,f.K2)((t=>t?t.length*R.fontSizeFactor:1),"getLabelWidth");var G=(0,f.K2)(((t,e,a,i,r,n,s)=>{const d=new D.T({compound:true,multigraph:true});let o;let c=true;for(o=0;o<t.length;o++){if(t[o].stmt==="relation"){c=false;break}}if(a){d.setGraph({rankdir:"LR",multigraph:true,compound:true,ranker:"tight-tree",ranksep:c?1:R.edgeLengthFactor,nodeSep:c?1:50,isMultiGraph:true})}else{d.setGraph({rankdir:"TB",multigraph:true,compound:true,ranksep:c?1:R.edgeLengthFactor,nodeSep:c?1:50,ranker:"tight-tree",isMultiGraph:true})}d.setDefaultEdgeLabel((function(){return{}}));const g=s.db.getStates();const p=s.db.getRelations();const h=Object.keys(g);let l=true;for(const f of h){const t=g[f];if(a){t.parentId=a}let o;if(t.doc){let a=e.append("g").attr("id",t.id).attr("class","stateGroup");o=G(t.doc,a,t.id,!i,r,n,s);if(l){a=b(a,t,i);let e=a.node().getBBox();o.width=e.width;o.height=e.height+R.padding/2;z[t.id]={y:R.compositTitleSize}}else{let t=a.node().getBBox();o.width=t.width;o.height=t.height}}else{o=E(e,t,d)}if(t.note){const a={descriptions:[],id:t.id+"-note",note:t.note,type:"note"};const i=E(e,a,d);if(t.note.position==="left of"){d.setNode(o.id+"-note",i);d.setNode(o.id,o)}else{d.setNode(o.id,o);d.setNode(o.id+"-note",i)}d.setParent(o.id,o.id+"-group");d.setParent(o.id+"-note",o.id+"-group")}else{d.setNode(o.id,o)}}f.Rm.debug("Count=",d.nodeCount(),d);let x=0;p.forEach((function(t){x++;f.Rm.debug("Setting edge",t);d.setEdge(t.id1,t.id2,{relation:t,width:A(t.title),height:R.labelHeight*f.Y2.getRows(t.title).length,labelpos:"c"},"id"+x)}));(0,u.Zp)(d);f.Rm.debug("Graph after layout",d.nodes());const y=e.node();d.nodes().forEach((function(t){if(t!==void 0&&d.node(t)!==void 0){f.Rm.warn("Node "+t+": "+JSON.stringify(d.node(t)));r.select("#"+y.id+" #"+t).attr("transform","translate("+(d.node(t).x-d.node(t).width/2)+","+(d.node(t).y+(z[t]?z[t].y:0)-d.node(t).height/2)+" )");r.select("#"+y.id+" #"+t).attr("data-x-shift",d.node(t).x-d.node(t).width/2);const e=n.querySelectorAll("#"+y.id+" #"+t+" .divider");e.forEach((t=>{const e=t.parentElement;let a=0;let i=0;if(e){if(e.parentElement){a=e.parentElement.getBBox().width}i=parseInt(e.getAttribute("data-x-shift"),10);if(Number.isNaN(i)){i=0}}t.setAttribute("x1",0-i+8);t.setAttribute("x2",a-i-8)}))}else{f.Rm.debug("No Node "+t+": "+JSON.stringify(d.node(t)))}}));let v=y.getBBox();d.edges().forEach((function(t){if(t!==void 0&&d.edge(t)!==void 0){f.Rm.debug("Edge "+t.v+" -> "+t.w+": "+JSON.stringify(d.edge(t)));K(e,d.edge(t),d.edge(t).relation)}}));v=y.getBBox();const w={id:a?a:"root",label:a?a:"root",width:0,height:0};w.width=v.width+2*R.padding;w.height=v.height+2*R.padding;f.Rm.debug("Doc rendered",w,d);return w}),"renderDoc");var C={setConf:H,draw:L};var O={parser:i.Zk,get db(){return new i.u4(1)},renderer:C,styles:i.tM,init:(0,f.K2)((t=>{if(!t.state){t.state={}}t.state.arrowMarkerAbsolute=t.arrowMarkerAbsolute}),"init")}}}]);