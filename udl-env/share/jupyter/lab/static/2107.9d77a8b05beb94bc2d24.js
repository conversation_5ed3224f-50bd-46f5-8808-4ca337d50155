"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[2107],{82107:(t,e,s)=>{s.d(e,{Zk:()=>c,q7:()=>et,tM:()=>St,u4:()=>gt});var i=s(52480);var r=s(35645);var n=s(16864);var a=s(52724);var o=s(6047);var l=function(){var t=(0,o.K2)((function(t,e,s,i){for(s=s||{},i=t.length;i--;s[t[i]]=e);return s}),"o"),e=[1,2],s=[1,3],i=[1,4],r=[2,4],n=[1,9],a=[1,11],l=[1,16],c=[1,17],h=[1,18],d=[1,19],u=[1,33],p=[1,20],f=[1,21],y=[1,22],g=[1,23],m=[1,24],S=[1,26],b=[1,27],k=[1,28],_=[1,29],T=[1,30],v=[1,31],E=[1,32],D=[1,35],x=[1,36],C=[1,37],$=[1,38],L=[1,34],A=[1,4,5,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57],I=[1,4,5,14,15,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,39,40,41,45,48,51,52,53,54,57],R=[4,5,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57];var w={trace:(0,o.K2)((function t(){}),"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NL:5,SD:6,document:7,line:8,statement:9,classDefStatement:10,styleStatement:11,cssClassStatement:12,idStatement:13,DESCR:14,"--\x3e":15,HIDE_EMPTY:16,scale:17,WIDTH:18,COMPOSIT_STATE:19,STRUCT_START:20,STRUCT_STOP:21,STATE_DESCR:22,AS:23,ID:24,FORK:25,JOIN:26,CHOICE:27,CONCURRENT:28,note:29,notePosition:30,NOTE_TEXT:31,direction:32,acc_title:33,acc_title_value:34,acc_descr:35,acc_descr_value:36,acc_descr_multiline_value:37,CLICK:38,STRING:39,HREF:40,classDef:41,CLASSDEF_ID:42,CLASSDEF_STYLEOPTS:43,DEFAULT:44,style:45,STYLE_IDS:46,STYLEDEF_STYLEOPTS:47,class:48,CLASSENTITY_IDS:49,STYLECLASS:50,direction_tb:51,direction_bt:52,direction_rl:53,direction_lr:54,eol:55,";":56,EDGE_STATE:57,STYLE_SEPARATOR:58,left_of:59,right_of:60,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NL",6:"SD",14:"DESCR",15:"--\x3e",16:"HIDE_EMPTY",17:"scale",18:"WIDTH",19:"COMPOSIT_STATE",20:"STRUCT_START",21:"STRUCT_STOP",22:"STATE_DESCR",23:"AS",24:"ID",25:"FORK",26:"JOIN",27:"CHOICE",28:"CONCURRENT",29:"note",31:"NOTE_TEXT",33:"acc_title",34:"acc_title_value",35:"acc_descr",36:"acc_descr_value",37:"acc_descr_multiline_value",38:"CLICK",39:"STRING",40:"HREF",41:"classDef",42:"CLASSDEF_ID",43:"CLASSDEF_STYLEOPTS",44:"DEFAULT",45:"style",46:"STYLE_IDS",47:"STYLEDEF_STYLEOPTS",48:"class",49:"CLASSENTITY_IDS",50:"STYLECLASS",51:"direction_tb",52:"direction_bt",53:"direction_rl",54:"direction_lr",56:";",57:"EDGE_STATE",58:"STYLE_SEPARATOR",59:"left_of",60:"right_of"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,3],[9,4],[9,1],[9,2],[9,1],[9,4],[9,3],[9,6],[9,1],[9,1],[9,1],[9,1],[9,4],[9,4],[9,1],[9,2],[9,2],[9,1],[9,5],[9,5],[10,3],[10,3],[11,3],[12,3],[32,1],[32,1],[32,1],[32,1],[55,1],[55,1],[13,1],[13,1],[13,3],[13,3],[30,1],[30,1]],performAction:(0,o.K2)((function t(e,s,i,r,n,a,o){var l=a.length-1;switch(n){case 3:r.setRootDoc(a[l]);return a[l];break;case 4:this.$=[];break;case 5:if(a[l]!="nl"){a[l-1].push(a[l]);this.$=a[l-1]}break;case 6:case 7:this.$=a[l];break;case 8:this.$="nl";break;case 12:this.$=a[l];break;case 13:const t=a[l-1];t.description=r.trimColon(a[l]);this.$=t;break;case 14:this.$={stmt:"relation",state1:a[l-2],state2:a[l]};break;case 15:const e=r.trimColon(a[l]);this.$={stmt:"relation",state1:a[l-3],state2:a[l-1],description:e};break;case 19:this.$={stmt:"state",id:a[l-3],type:"default",description:"",doc:a[l-1]};break;case 20:var c=a[l];var h=a[l-2].trim();if(a[l].match(":")){var d=a[l].split(":");c=d[0];h=[h,d[1]]}this.$={stmt:"state",id:c,type:"default",description:h};break;case 21:this.$={stmt:"state",id:a[l-3],type:"default",description:a[l-5],doc:a[l-1]};break;case 22:this.$={stmt:"state",id:a[l],type:"fork"};break;case 23:this.$={stmt:"state",id:a[l],type:"join"};break;case 24:this.$={stmt:"state",id:a[l],type:"choice"};break;case 25:this.$={stmt:"state",id:r.getDividerId(),type:"divider"};break;case 26:this.$={stmt:"state",id:a[l-1].trim(),note:{position:a[l-2].trim(),text:a[l].trim()}};break;case 29:this.$=a[l].trim();r.setAccTitle(this.$);break;case 30:case 31:this.$=a[l].trim();r.setAccDescription(this.$);break;case 32:this.$={stmt:"click",id:a[l-3],url:a[l-2],tooltip:a[l-1]};break;case 33:this.$={stmt:"click",id:a[l-3],url:a[l-1],tooltip:""};break;case 34:case 35:this.$={stmt:"classDef",id:a[l-1].trim(),classes:a[l].trim()};break;case 36:this.$={stmt:"style",id:a[l-1].trim(),styleClass:a[l].trim()};break;case 37:this.$={stmt:"applyClass",id:a[l-1].trim(),styleClass:a[l].trim()};break;case 38:r.setDirection("TB");this.$={stmt:"dir",value:"TB"};break;case 39:r.setDirection("BT");this.$={stmt:"dir",value:"BT"};break;case 40:r.setDirection("RL");this.$={stmt:"dir",value:"RL"};break;case 41:r.setDirection("LR");this.$={stmt:"dir",value:"LR"};break;case 44:case 45:this.$={stmt:"state",id:a[l].trim(),type:"default",description:""};break;case 46:this.$={stmt:"state",id:a[l-2].trim(),classes:[a[l].trim()],type:"default",description:""};break;case 47:this.$={stmt:"state",id:a[l-2].trim(),classes:[a[l].trim()],type:"default",description:""};break}}),"anonymous"),table:[{3:1,4:e,5:s,6:i},{1:[3]},{3:5,4:e,5:s,6:i},{3:6,4:e,5:s,6:i},t([1,4,5,16,17,19,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57],r,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:n,5:a,8:8,9:10,10:12,11:13,12:14,13:15,16:l,17:c,19:h,22:d,24:u,25:p,26:f,27:y,28:g,29:m,32:25,33:S,35:b,37:k,38:_,41:T,45:v,48:E,51:D,52:x,53:C,54:$,57:L},t(A,[2,5]),{9:39,10:12,11:13,12:14,13:15,16:l,17:c,19:h,22:d,24:u,25:p,26:f,27:y,28:g,29:m,32:25,33:S,35:b,37:k,38:_,41:T,45:v,48:E,51:D,52:x,53:C,54:$,57:L},t(A,[2,7]),t(A,[2,8]),t(A,[2,9]),t(A,[2,10]),t(A,[2,11]),t(A,[2,12],{14:[1,40],15:[1,41]}),t(A,[2,16]),{18:[1,42]},t(A,[2,18],{20:[1,43]}),{23:[1,44]},t(A,[2,22]),t(A,[2,23]),t(A,[2,24]),t(A,[2,25]),{30:45,31:[1,46],59:[1,47],60:[1,48]},t(A,[2,28]),{34:[1,49]},{36:[1,50]},t(A,[2,31]),{13:51,24:u,57:L},{42:[1,52],44:[1,53]},{46:[1,54]},{49:[1,55]},t(I,[2,44],{58:[1,56]}),t(I,[2,45],{58:[1,57]}),t(A,[2,38]),t(A,[2,39]),t(A,[2,40]),t(A,[2,41]),t(A,[2,6]),t(A,[2,13]),{13:58,24:u,57:L},t(A,[2,17]),t(R,r,{7:59}),{24:[1,60]},{24:[1,61]},{23:[1,62]},{24:[2,48]},{24:[2,49]},t(A,[2,29]),t(A,[2,30]),{39:[1,63],40:[1,64]},{43:[1,65]},{43:[1,66]},{47:[1,67]},{50:[1,68]},{24:[1,69]},{24:[1,70]},t(A,[2,14],{14:[1,71]}),{4:n,5:a,8:8,9:10,10:12,11:13,12:14,13:15,16:l,17:c,19:h,21:[1,72],22:d,24:u,25:p,26:f,27:y,28:g,29:m,32:25,33:S,35:b,37:k,38:_,41:T,45:v,48:E,51:D,52:x,53:C,54:$,57:L},t(A,[2,20],{20:[1,73]}),{31:[1,74]},{24:[1,75]},{39:[1,76]},{39:[1,77]},t(A,[2,34]),t(A,[2,35]),t(A,[2,36]),t(A,[2,37]),t(I,[2,46]),t(I,[2,47]),t(A,[2,15]),t(A,[2,19]),t(R,r,{7:78}),t(A,[2,26]),t(A,[2,27]),{5:[1,79]},{5:[1,80]},{4:n,5:a,8:8,9:10,10:12,11:13,12:14,13:15,16:l,17:c,19:h,21:[1,81],22:d,24:u,25:p,26:f,27:y,28:g,29:m,32:25,33:S,35:b,37:k,38:_,41:T,45:v,48:E,51:D,52:x,53:C,54:$,57:L},t(A,[2,32]),t(A,[2,33]),t(A,[2,21])],defaultActions:{5:[2,1],6:[2,2],47:[2,48],48:[2,49]},parseError:(0,o.K2)((function t(e,s){if(s.recoverable){this.trace(e)}else{var i=new Error(e);i.hash=s;throw i}}),"parseError"),parse:(0,o.K2)((function t(e){var s=this,i=[0],r=[],n=[null],a=[],l=this.table,c="",h=0,d=0,u=0,p=2,f=1;var y=a.slice.call(arguments,1);var g=Object.create(this.lexer);var m={yy:{}};for(var S in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,S)){m.yy[S]=this.yy[S]}}g.setInput(e,m.yy);m.yy.lexer=g;m.yy.parser=this;if(typeof g.yylloc=="undefined"){g.yylloc={}}var b=g.yylloc;a.push(b);var k=g.options&&g.options.ranges;if(typeof m.yy.parseError==="function"){this.parseError=m.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function _(t){i.length=i.length-2*t;n.length=n.length-t;a.length=a.length-t}(0,o.K2)(_,"popStack");function T(){var t;t=r.pop()||g.lex()||f;if(typeof t!=="number"){if(t instanceof Array){r=t;t=r.pop()}t=s.symbols_[t]||t}return t}(0,o.K2)(T,"lex");var v,E,D,x,C,$,L={},A,I,R,w;while(true){D=i[i.length-1];if(this.defaultActions[D]){x=this.defaultActions[D]}else{if(v===null||typeof v=="undefined"){v=T()}x=l[D]&&l[D][v]}if(typeof x==="undefined"||!x.length||!x[0]){var O="";w=[];for(A in l[D]){if(this.terminals_[A]&&A>p){w.push("'"+this.terminals_[A]+"'")}}if(g.showPosition){O="Parse error on line "+(h+1)+":\n"+g.showPosition()+"\nExpecting "+w.join(", ")+", got '"+(this.terminals_[v]||v)+"'"}else{O="Parse error on line "+(h+1)+": Unexpected "+(v==f?"end of input":"'"+(this.terminals_[v]||v)+"'")}this.parseError(O,{text:g.match,token:this.terminals_[v]||v,line:g.yylineno,loc:b,expected:w})}if(x[0]instanceof Array&&x.length>1){throw new Error("Parse Error: multiple actions possible at state: "+D+", token: "+v)}switch(x[0]){case 1:i.push(v);n.push(g.yytext);a.push(g.yylloc);i.push(x[1]);v=null;if(!E){d=g.yyleng;c=g.yytext;h=g.yylineno;b=g.yylloc;if(u>0){u--}}else{v=E;E=null}break;case 2:I=this.productions_[x[1]][1];L.$=n[n.length-I];L._$={first_line:a[a.length-(I||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(I||1)].first_column,last_column:a[a.length-1].last_column};if(k){L._$.range=[a[a.length-(I||1)].range[0],a[a.length-1].range[1]]}$=this.performAction.apply(L,[c,d,h,m.yy,x[1],n,a].concat(y));if(typeof $!=="undefined"){return $}if(I){i=i.slice(0,-1*I*2);n=n.slice(0,-1*I);a=a.slice(0,-1*I)}i.push(this.productions_[x[1]][0]);n.push(L.$);a.push(L._$);R=l[i[i.length-2]][i[i.length-1]];i.push(R);break;case 3:return true}}return true}),"parse")};var O=function(){var t={EOF:1,parseError:(0,o.K2)((function t(e,s){if(this.yy.parser){this.yy.parser.parseError(e,s)}else{throw new Error(e)}}),"parseError"),setInput:(0,o.K2)((function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,o.K2)((function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t}),"input"),unput:(0,o.K2)((function(t){var e=t.length;var s=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(s.length-1){this.yylineno-=s.length-1}var r=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===i.length?this.yylloc.first_column:0)+i[i.length-s.length].length-s[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[r[0],r[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,o.K2)((function(){this._more=true;return this}),"more"),reject:(0,o.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,o.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,o.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,o.K2)((function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,o.K2)((function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,o.K2)((function(t,e){var s,i,r;if(this.options.backtrack_lexer){r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){r.yylloc.range=this.yylloc.range.slice(0)}}i=t[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];s=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(s){return s}else if(this._backtrack){for(var n in r){this[n]=r[n]}return false}return false}),"test_match"),next:(0,o.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,s,i;if(!this._more){this.yytext="";this.match=""}var r=this._currentRules();for(var n=0;n<r.length;n++){s=this._input.match(this.rules[r[n]]);if(s&&(!e||s[0].length>e[0].length)){e=s;i=n;if(this.options.backtrack_lexer){t=this.test_match(s,r[n]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,r[i]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,o.K2)((function t(){var e=this.next();if(e){return e}else{return this.lex()}}),"lex"),begin:(0,o.K2)((function t(e){this.conditionStack.push(e)}),"begin"),popState:(0,o.K2)((function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,o.K2)((function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,o.K2)((function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}}),"topState"),pushState:(0,o.K2)((function t(e){this.begin(e)}),"pushState"),stateStackSize:(0,o.K2)((function t(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":true},performAction:(0,o.K2)((function t(e,s,i,r){var n=r;switch(i){case 0:return 38;break;case 1:return 40;break;case 2:return 39;break;case 3:return 44;break;case 4:return 51;break;case 5:return 52;break;case 6:return 53;break;case 7:return 54;break;case 8:break;case 9:{}break;case 10:return 5;break;case 11:break;case 12:break;case 13:break;case 14:break;case 15:this.pushState("SCALE");return 17;break;case 16:return 18;break;case 17:this.popState();break;case 18:this.begin("acc_title");return 33;break;case 19:this.popState();return"acc_title_value";break;case 20:this.begin("acc_descr");return 35;break;case 21:this.popState();return"acc_descr_value";break;case 22:this.begin("acc_descr_multiline");break;case 23:this.popState();break;case 24:return"acc_descr_multiline_value";break;case 25:this.pushState("CLASSDEF");return 41;break;case 26:this.popState();this.pushState("CLASSDEFID");return"DEFAULT_CLASSDEF_ID";break;case 27:this.popState();this.pushState("CLASSDEFID");return 42;break;case 28:this.popState();return 43;break;case 29:this.pushState("CLASS");return 48;break;case 30:this.popState();this.pushState("CLASS_STYLE");return 49;break;case 31:this.popState();return 50;break;case 32:this.pushState("STYLE");return 45;break;case 33:this.popState();this.pushState("STYLEDEF_STYLES");return 46;break;case 34:this.popState();return 47;break;case 35:this.pushState("SCALE");return 17;break;case 36:return 18;break;case 37:this.popState();break;case 38:this.pushState("STATE");break;case 39:this.popState();s.yytext=s.yytext.slice(0,-8).trim();return 25;break;case 40:this.popState();s.yytext=s.yytext.slice(0,-8).trim();return 26;break;case 41:this.popState();s.yytext=s.yytext.slice(0,-10).trim();return 27;break;case 42:this.popState();s.yytext=s.yytext.slice(0,-8).trim();return 25;break;case 43:this.popState();s.yytext=s.yytext.slice(0,-8).trim();return 26;break;case 44:this.popState();s.yytext=s.yytext.slice(0,-10).trim();return 27;break;case 45:return 51;break;case 46:return 52;break;case 47:return 53;break;case 48:return 54;break;case 49:this.pushState("STATE_STRING");break;case 50:this.pushState("STATE_ID");return"AS";break;case 51:this.popState();return"ID";break;case 52:this.popState();break;case 53:return"STATE_DESCR";break;case 54:return 19;break;case 55:this.popState();break;case 56:this.popState();this.pushState("struct");return 20;break;case 57:break;case 58:this.popState();return 21;break;case 59:break;case 60:this.begin("NOTE");return 29;break;case 61:this.popState();this.pushState("NOTE_ID");return 59;break;case 62:this.popState();this.pushState("NOTE_ID");return 60;break;case 63:this.popState();this.pushState("FLOATING_NOTE");break;case 64:this.popState();this.pushState("FLOATING_NOTE_ID");return"AS";break;case 65:break;case 66:return"NOTE_TEXT";break;case 67:this.popState();return"ID";break;case 68:this.popState();this.pushState("NOTE_TEXT");return 24;break;case 69:this.popState();s.yytext=s.yytext.substr(2).trim();return 31;break;case 70:this.popState();s.yytext=s.yytext.slice(0,-8).trim();return 31;break;case 71:return 6;break;case 72:return 6;break;case 73:return 16;break;case 74:return 57;break;case 75:return 24;break;case 76:s.yytext=s.yytext.trim();return 14;break;case 77:return 15;break;case 78:return 28;break;case 79:return 58;break;case 80:return 5;break;case 81:return"INVALID";break}}),"anonymous"),rules:[/^(?:click\b)/i,/^(?:href\b)/i,/^(?:"[^"]*")/i,/^(?:default\b)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n]+)/i,/^(?:[\s]+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%[^\n]*)/i,/^(?:scale\s+)/i,/^(?:\d+)/i,/^(?:\s+width\b)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:classDef\s+)/i,/^(?:DEFAULT\s+)/i,/^(?:\w+\s+)/i,/^(?:[^\n]*)/i,/^(?:class\s+)/i,/^(?:(\w+)+((,\s*\w+)*))/i,/^(?:[^\n]*)/i,/^(?:style\s+)/i,/^(?:[\w,]+\s+)/i,/^(?:[^\n]*)/i,/^(?:scale\s+)/i,/^(?:\d+)/i,/^(?:\s+width\b)/i,/^(?:state\s+)/i,/^(?:.*<<fork>>)/i,/^(?:.*<<join>>)/i,/^(?:.*<<choice>>)/i,/^(?:.*\[\[fork\]\])/i,/^(?:.*\[\[join\]\])/i,/^(?:.*\[\[choice\]\])/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:["])/i,/^(?:\s*as\s+)/i,/^(?:[^\n\{]*)/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[^\n\s\{]+)/i,/^(?:\n)/i,/^(?:\{)/i,/^(?:%%(?!\{)[^\n]*)/i,/^(?:\})/i,/^(?:[\n])/i,/^(?:note\s+)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:")/i,/^(?:\s*as\s*)/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[^\n]*)/i,/^(?:\s*[^:\n\s\-]+)/i,/^(?:\s*:[^:\n;]+)/i,/^(?:[\s\S]*?end note\b)/i,/^(?:stateDiagram\s+)/i,/^(?:stateDiagram-v2\s+)/i,/^(?:hide empty description\b)/i,/^(?:\[\*\])/i,/^(?:[^:\n\s\-\{]+)/i,/^(?:\s*:[^:\n;]+)/i,/^(?:-->)/i,/^(?:--)/i,/^(?::::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{LINE:{rules:[12,13],inclusive:false},struct:{rules:[12,13,25,29,32,38,45,46,47,48,57,58,59,60,74,75,76,77,78],inclusive:false},FLOATING_NOTE_ID:{rules:[67],inclusive:false},FLOATING_NOTE:{rules:[64,65,66],inclusive:false},NOTE_TEXT:{rules:[69,70],inclusive:false},NOTE_ID:{rules:[68],inclusive:false},NOTE:{rules:[61,62,63],inclusive:false},STYLEDEF_STYLEOPTS:{rules:[],inclusive:false},STYLEDEF_STYLES:{rules:[34],inclusive:false},STYLE_IDS:{rules:[],inclusive:false},STYLE:{rules:[33],inclusive:false},CLASS_STYLE:{rules:[31],inclusive:false},CLASS:{rules:[30],inclusive:false},CLASSDEFID:{rules:[28],inclusive:false},CLASSDEF:{rules:[26,27],inclusive:false},acc_descr_multiline:{rules:[23,24],inclusive:false},acc_descr:{rules:[21],inclusive:false},acc_title:{rules:[19],inclusive:false},SCALE:{rules:[16,17,36,37],inclusive:false},ALIAS:{rules:[],inclusive:false},STATE_ID:{rules:[51],inclusive:false},STATE_STRING:{rules:[52,53],inclusive:false},FORK_STATE:{rules:[],inclusive:false},STATE:{rules:[12,13,39,40,41,42,43,44,49,50,54,55,56],inclusive:false},ID:{rules:[12,13],inclusive:false},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,10,11,13,14,15,18,20,22,25,29,32,35,38,56,60,71,72,73,74,75,76,77,79,80,81],inclusive:true}}};return t}();w.lexer=O;function N(){this.yy={}}(0,o.K2)(N,"Parser");N.prototype=w;w.Parser=N;return new N}();l.parser=l;var c=l;var h="TB";var d="TB";var u="dir";var p="state";var f="root";var y="relation";var g="classDef";var m="style";var S="applyClass";var b="default";var k="divider";var _="fill:none";var T="fill: #333";var v="c";var E="text";var D="normal";var x="rect";var C="rectWithTitle";var $="stateStart";var L="stateEnd";var A="divider";var I="roundedWithTitle";var R="note";var w="noteGroup";var O="statediagram";var N="state";var K=`${O}-${N}`;var B="transition";var Y="note";var F="note-edge";var P=`${B} ${F}`;var G=`${O}-${Y}`;var j="cluster";var W=`${O}-${j}`;var z="cluster-alt";var M=`${O}-${z}`;var U="parent";var V="note";var X="state";var H="----";var J=`${H}${V}`;var q=`${H}${U}`;var Z=(0,o.K2)(((t,e=d)=>{if(!t.doc){return e}let s=e;for(const i of t.doc){if(i.stmt==="dir"){s=i.value}}return s}),"getDir");var Q=(0,o.K2)((function(t,e){return e.db.getClasses()}),"getClasses");var tt=(0,o.K2)((async function(t,e,s,l){o.Rm.info("REF0:");o.Rm.info("Drawing state diagram (v2)",e);const{securityLevel:c,state:h,layout:d}=(0,o.D7)();l.db.extract(l.db.getRootDocV2());const u=l.db.getData();const p=(0,i.A)(e,c);u.type=l.type;u.layoutAlgorithm=d;u.nodeSpacing=h?.nodeSpacing||50;u.rankSpacing=h?.rankSpacing||50;u.markers=["barb"];u.diagramId=e;await(0,n.XX)(u,p);const f=8;try{const t=typeof l.db.getLinks==="function"?l.db.getLinks():new Map;t.forEach(((t,e)=>{const s=typeof e==="string"?e:typeof e?.id==="string"?e.id:"";if(!s){o.Rm.warn("⚠️ Invalid or missing stateId from key:",JSON.stringify(e));return}const i=p.node()?.querySelectorAll("g");let r;i?.forEach((t=>{const e=t.textContent?.trim();if(e===s){r=t}}));if(!r){o.Rm.warn("⚠️ Could not find node matching text:",s);return}const n=r.parentNode;if(!n){o.Rm.warn("⚠️ Node has no parent, cannot wrap:",s);return}const a=document.createElementNS("http://www.w3.org/2000/svg","a");const l=t.url.replace(/^"+|"+$/g,"");a.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);a.setAttribute("target","_blank");if(t.tooltip){const e=t.tooltip.replace(/^"+|"+$/g,"");a.setAttribute("title",e)}n.replaceChild(a,r);a.appendChild(r);o.Rm.info("🔗 Wrapped node in <a> tag for:",s,t.url)}))}catch(y){o.Rm.error("❌ Error injecting clickable links:",y)}a._K.insertTitle(p,"statediagramTitleText",h?.titleTopMargin??25,l.db.getDiagramTitle());(0,r.P)(p,f,O,h?.useMaxWidth??true)}),"draw");var et={getClasses:Q,draw:tt,getDir:Z};var st=new Map;var it=0;function rt(t="",e=0,s="",i=H){const r=s!==null&&s.length>0?`${i}${s}`:"";return`${X}-${t}${r}-${e}`}(0,o.K2)(rt,"stateDomId");var nt=(0,o.K2)(((t,e,s,i,r,n,a,l)=>{o.Rm.trace("items",e);e.forEach((e=>{switch(e.stmt){case p:ht(t,e,s,i,r,n,a,l);break;case b:ht(t,e,s,i,r,n,a,l);break;case y:{ht(t,e.state1,s,i,r,n,a,l);ht(t,e.state2,s,i,r,n,a,l);const c={id:"edge"+it,start:e.state1.id,end:e.state2.id,arrowhead:"normal",arrowTypeEnd:"arrow_barb",style:_,labelStyle:"",label:o.Y2.sanitizeText(e.description??"",(0,o.D7)()),arrowheadStyle:T,labelpos:v,labelType:E,thickness:D,classes:B,look:a};r.push(c);it++}break}}))}),"setupDoc");var at=(0,o.K2)(((t,e=d)=>{let s=e;if(t.doc){for(const e of t.doc){if(e.stmt==="dir"){s=e.value}}}return s}),"getDir");function ot(t,e,s){if(!e.id||e.id==="</join></fork>"||e.id==="</choice>"){return}if(e.cssClasses){if(!Array.isArray(e.cssCompiledStyles)){e.cssCompiledStyles=[]}e.cssClasses.split(" ").forEach((t=>{const i=s.get(t);if(i){e.cssCompiledStyles=[...e.cssCompiledStyles??[],...i.styles]}}))}const i=t.find((t=>t.id===e.id));if(i){Object.assign(i,e)}else{t.push(e)}}(0,o.K2)(ot,"insertOrUpdateNode");function lt(t){return t?.classes?.join(" ")??""}(0,o.K2)(lt,"getClassesFromDbInfo");function ct(t){return t?.styles??[]}(0,o.K2)(ct,"getStylesFromDbInfo");var ht=(0,o.K2)(((t,e,s,i,r,n,a,l)=>{const c=e.id;const h=s.get(c);const d=lt(h);const u=ct(h);const p=(0,o.D7)();o.Rm.info("dataFetcher parsedItem",e,h,u);if(c!=="root"){let s=x;if(e.start===true){s=$}else if(e.start===false){s=L}if(e.type!==b){s=e.type}if(!st.get(c)){st.set(c,{id:c,shape:s,description:o.Y2.sanitizeText(c,p),cssClasses:`${d} ${K}`,cssStyles:u})}const h=st.get(c);if(e.description){if(Array.isArray(h.description)){h.shape=C;h.description.push(e.description)}else{if(h.description?.length&&h.description.length>0){h.shape=C;if(h.description===c){h.description=[e.description]}else{h.description=[h.description,e.description]}}else{h.shape=x;h.description=e.description}}h.description=o.Y2.sanitizeTextOrArray(h.description,p)}if(h.description?.length===1&&h.shape===C){if(h.type==="group"){h.shape=I}else{h.shape=x}}if(!h.type&&e.doc){o.Rm.info("Setting cluster for XCX",c,at(e));h.type="group";h.isGroup=true;h.dir=at(e);h.shape=e.type===k?A:I;h.cssClasses=`${h.cssClasses} ${W} ${n?M:""}`}const f={labelStyle:"",shape:h.shape,label:h.description,cssClasses:h.cssClasses,cssCompiledStyles:[],cssStyles:h.cssStyles,id:c,dir:h.dir,domId:rt(c,it),type:h.type,isGroup:h.type==="group",padding:8,rx:10,ry:10,look:a};if(f.shape===A){f.label=""}if(t&&t.id!=="root"){o.Rm.trace("Setting node ",c," to be child of its parent ",t.id);f.parentId=t.id}f.centerLabel=true;if(e.note){const t={labelStyle:"",shape:R,label:e.note.text,cssClasses:G,cssStyles:[],cssCompiledStyles:[],id:c+J+"-"+it,domId:rt(c,it,V),type:h.type,isGroup:h.type==="group",padding:p.flowchart?.padding,look:a,position:e.note.position};const s=c+q;const n={labelStyle:"",shape:w,label:e.note.text,cssClasses:h.cssClasses,cssStyles:[],id:c+q,domId:rt(c,it,U),type:"group",isGroup:true,padding:16,look:a,position:e.note.position};it++;n.id=s;t.parentId=s;ot(i,n,l);ot(i,t,l);ot(i,f,l);let o=c;let d=t.id;if(e.note.position==="left of"){o=t.id;d=c}r.push({id:o+"-"+d,start:o,end:d,arrowhead:"none",arrowTypeEnd:"",style:_,labelStyle:"",classes:P,arrowheadStyle:T,labelpos:v,labelType:E,thickness:D,look:a})}else{ot(i,f,l)}}if(e.doc){o.Rm.trace("Adding nodes children ");nt(e,e.doc,s,i,r,!n,a,l)}}),"dataFetcher");var dt=(0,o.K2)((()=>{st.clear();it=0}),"reset");var ut={START_NODE:"[*]",START_TYPE:"start",END_NODE:"[*]",END_TYPE:"end",COLOR_KEYWORD:"color",FILL_KEYWORD:"fill",BG_FILL:"bgFill",STYLECLASS_SEP:","};var pt=(0,o.K2)((()=>new Map),"newClassesList");var ft=(0,o.K2)((()=>({relations:[],states:new Map,documents:{}})),"newDoc");var yt=(0,o.K2)((t=>JSON.parse(JSON.stringify(t))),"clone");var gt=class{constructor(t){this.version=t;this.nodes=[];this.edges=[];this.rootDoc=[];this.classes=pt();this.documents={root:ft()};this.currentDocument=this.documents.root;this.startEndCount=0;this.dividerCnt=0;this.links=new Map;this.getAccTitle=o.iN;this.setAccTitle=o.SV;this.getAccDescription=o.m7;this.setAccDescription=o.EI;this.setDiagramTitle=o.ke;this.getDiagramTitle=o.ab;this.clear();this.setRootDoc=this.setRootDoc.bind(this);this.getDividerId=this.getDividerId.bind(this);this.setDirection=this.setDirection.bind(this);this.trimColon=this.trimColon.bind(this)}static{(0,o.K2)(this,"StateDB")}static{this.relationType={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3}}extract(t){this.clear(true);for(const i of Array.isArray(t)?t:t.doc){switch(i.stmt){case p:this.addState(i.id.trim(),i.type,i.doc,i.description,i.note);break;case y:this.addRelation(i.state1,i.state2,i.description);break;case g:this.addStyleClass(i.id.trim(),i.classes);break;case m:this.handleStyleDef(i);break;case S:this.setCssClass(i.id.trim(),i.styleClass);break;case"click":this.addLink(i.id,i.url,i.tooltip);break}}const e=this.getStates();const s=(0,o.D7)();dt();ht(void 0,this.getRootDocV2(),e,this.nodes,this.edges,true,s.look,this.classes);for(const i of this.nodes){if(!Array.isArray(i.label)){continue}i.description=i.label.slice(1);if(i.isGroup&&i.description.length>0){throw new Error(`Group nodes can only have label. Remove the additional description for node [${i.id}]`)}i.label=i.label[0]}}handleStyleDef(t){const e=t.id.trim().split(",");const s=t.styleClass.split(",");for(const i of e){let t=this.getState(i);if(!t){const e=i.trim();this.addState(e);t=this.getState(e)}if(t){t.styles=s.map((t=>t.replace(/;/g,"")?.trim()))}}}setRootDoc(t){o.Rm.info("Setting root doc",t);this.rootDoc=t;if(this.version===1){this.extract(t)}else{this.extract(this.getRootDocV2())}}docTranslator(t,e,s){if(e.stmt===y){this.docTranslator(t,e.state1,true);this.docTranslator(t,e.state2,false);return}if(e.stmt===p){if(e.id===ut.START_NODE){e.id=t.id+(s?"_start":"_end");e.start=s}else{e.id=e.id.trim()}}if(e.stmt!==f&&e.stmt!==p||!e.doc){return}const i=[];let r=[];for(const n of e.doc){if(n.type===k){const t=yt(n);t.doc=yt(r);i.push(t);r=[]}else{r.push(n)}}if(i.length>0&&r.length>0){const t={stmt:p,id:(0,a.$C)(),type:"divider",doc:yt(r)};i.push(yt(t));e.doc=i}e.doc.forEach((t=>this.docTranslator(e,t,true)))}getRootDocV2(){this.docTranslator({id:f,stmt:f},{id:f,stmt:f,doc:this.rootDoc},true);return{id:f,doc:this.rootDoc}}addState(t,e=b,s=void 0,i=void 0,r=void 0,n=void 0,a=void 0,l=void 0){const c=t?.trim();if(!this.currentDocument.states.has(c)){o.Rm.info("Adding state ",c,i);this.currentDocument.states.set(c,{stmt:p,id:c,descriptions:[],type:e,doc:s,note:r,classes:[],styles:[],textStyles:[]})}else{const t=this.currentDocument.states.get(c);if(!t){throw new Error(`State not found: ${c}`)}if(!t.doc){t.doc=s}if(!t.type){t.type=e}}if(i){o.Rm.info("Setting state description",c,i);const t=Array.isArray(i)?i:[i];t.forEach((t=>this.addDescription(c,t.trim())))}if(r){const t=this.currentDocument.states.get(c);if(!t){throw new Error(`State not found: ${c}`)}t.note=r;t.note.text=o.Y2.sanitizeText(t.note.text,(0,o.D7)())}if(n){o.Rm.info("Setting state classes",c,n);const t=Array.isArray(n)?n:[n];t.forEach((t=>this.setCssClass(c,t.trim())))}if(a){o.Rm.info("Setting state styles",c,a);const t=Array.isArray(a)?a:[a];t.forEach((t=>this.setStyle(c,t.trim())))}if(l){o.Rm.info("Setting state styles",c,a);const t=Array.isArray(l)?l:[l];t.forEach((t=>this.setTextStyle(c,t.trim())))}}clear(t){this.nodes=[];this.edges=[];this.documents={root:ft()};this.currentDocument=this.documents.root;this.startEndCount=0;this.classes=pt();if(!t){this.links=new Map;(0,o.IU)()}}getState(t){return this.currentDocument.states.get(t)}getStates(){return this.currentDocument.states}logDocuments(){o.Rm.info("Documents = ",this.documents)}getRelations(){return this.currentDocument.relations}addLink(t,e,s){this.links.set(t,{url:e,tooltip:s});o.Rm.warn("Adding link",t,e,s)}getLinks(){return this.links}startIdIfNeeded(t=""){if(t===ut.START_NODE){this.startEndCount++;return`${ut.START_TYPE}${this.startEndCount}`}return t}startTypeIfNeeded(t="",e=b){return t===ut.START_NODE?ut.START_TYPE:e}endIdIfNeeded(t=""){if(t===ut.END_NODE){this.startEndCount++;return`${ut.END_TYPE}${this.startEndCount}`}return t}endTypeIfNeeded(t="",e=b){return t===ut.END_NODE?ut.END_TYPE:e}addRelationObjs(t,e,s=""){const i=this.startIdIfNeeded(t.id.trim());const r=this.startTypeIfNeeded(t.id.trim(),t.type);const n=this.startIdIfNeeded(e.id.trim());const a=this.startTypeIfNeeded(e.id.trim(),e.type);this.addState(i,r,t.doc,t.description,t.note,t.classes,t.styles,t.textStyles);this.addState(n,a,e.doc,e.description,e.note,e.classes,e.styles,e.textStyles);this.currentDocument.relations.push({id1:i,id2:n,relationTitle:o.Y2.sanitizeText(s,(0,o.D7)())})}addRelation(t,e,s){if(typeof t==="object"&&typeof e==="object"){this.addRelationObjs(t,e,s)}else if(typeof t==="string"&&typeof e==="string"){const i=this.startIdIfNeeded(t.trim());const r=this.startTypeIfNeeded(t);const n=this.endIdIfNeeded(e.trim());const a=this.endTypeIfNeeded(e);this.addState(i,r);this.addState(n,a);this.currentDocument.relations.push({id1:i,id2:n,relationTitle:s?o.Y2.sanitizeText(s,(0,o.D7)()):void 0})}}addDescription(t,e){const s=this.currentDocument.states.get(t);const i=e.startsWith(":")?e.replace(":","").trim():e;s?.descriptions?.push(o.Y2.sanitizeText(i,(0,o.D7)()))}cleanupLabel(t){return t.startsWith(":")?t.slice(2).trim():t.trim()}getDividerId(){this.dividerCnt++;return`divider-id-${this.dividerCnt}`}addStyleClass(t,e=""){if(!this.classes.has(t)){this.classes.set(t,{id:t,styles:[],textStyles:[]})}const s=this.classes.get(t);if(e&&s){e.split(ut.STYLECLASS_SEP).forEach((t=>{const e=t.replace(/([^;]*);/,"$1").trim();if(RegExp(ut.COLOR_KEYWORD).exec(t)){const t=e.replace(ut.FILL_KEYWORD,ut.BG_FILL);const i=t.replace(ut.COLOR_KEYWORD,ut.FILL_KEYWORD);s.textStyles.push(i)}s.styles.push(e)}))}}getClasses(){return this.classes}setCssClass(t,e){t.split(",").forEach((t=>{let s=this.getState(t);if(!s){const e=t.trim();this.addState(e);s=this.getState(e)}s?.classes?.push(e)}))}setStyle(t,e){this.getState(t)?.styles?.push(e)}setTextStyle(t,e){this.getState(t)?.textStyles?.push(e)}getDirectionStatement(){return this.rootDoc.find((t=>t.stmt===u))}getDirection(){return this.getDirectionStatement()?.value??h}setDirection(t){const e=this.getDirectionStatement();if(e){e.value=t}else{this.rootDoc.unshift({stmt:u,value:t})}}trimColon(t){return t.startsWith(":")?t.slice(1).trim():t.trim()}getData(){const t=(0,o.D7)();return{nodes:this.nodes,edges:this.edges,other:{},config:t,direction:Z(this.getRootDocV2())}}getConfig(){return(0,o.D7)().state}};var mt=(0,o.K2)((t=>`\ndefs #statediagram-barbEnd {\n    fill: ${t.transitionColor};\n    stroke: ${t.transitionColor};\n  }\ng.stateGroup text {\n  fill: ${t.nodeBorder};\n  stroke: none;\n  font-size: 10px;\n}\ng.stateGroup text {\n  fill: ${t.textColor};\n  stroke: none;\n  font-size: 10px;\n\n}\ng.stateGroup .state-title {\n  font-weight: bolder;\n  fill: ${t.stateLabelColor};\n}\n\ng.stateGroup rect {\n  fill: ${t.mainBkg};\n  stroke: ${t.nodeBorder};\n}\n\ng.stateGroup line {\n  stroke: ${t.lineColor};\n  stroke-width: 1;\n}\n\n.transition {\n  stroke: ${t.transitionColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.stateGroup .composit {\n  fill: ${t.background};\n  border-bottom: 1px\n}\n\n.stateGroup .alt-composit {\n  fill: #e0e0e0;\n  border-bottom: 1px\n}\n\n.state-note {\n  stroke: ${t.noteBorderColor};\n  fill: ${t.noteBkgColor};\n\n  text {\n    fill: ${t.noteTextColor};\n    stroke: none;\n    font-size: 10px;\n  }\n}\n\n.stateLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${t.mainBkg};\n  opacity: 0.5;\n}\n\n.edgeLabel .label rect {\n  fill: ${t.labelBackgroundColor};\n  opacity: 0.5;\n}\n.edgeLabel {\n  background-color: ${t.edgeLabelBackground};\n  p {\n    background-color: ${t.edgeLabelBackground};\n  }\n  rect {\n    opacity: 0.5;\n    background-color: ${t.edgeLabelBackground};\n    fill: ${t.edgeLabelBackground};\n  }\n  text-align: center;\n}\n.edgeLabel .label text {\n  fill: ${t.transitionLabelColor||t.tertiaryTextColor};\n}\n.label div .edgeLabel {\n  color: ${t.transitionLabelColor||t.tertiaryTextColor};\n}\n\n.stateLabel text {\n  fill: ${t.stateLabelColor};\n  font-size: 10px;\n  font-weight: bold;\n}\n\n.node circle.state-start {\n  fill: ${t.specialStateColor};\n  stroke: ${t.specialStateColor};\n}\n\n.node .fork-join {\n  fill: ${t.specialStateColor};\n  stroke: ${t.specialStateColor};\n}\n\n.node circle.state-end {\n  fill: ${t.innerEndBackground};\n  stroke: ${t.background};\n  stroke-width: 1.5\n}\n.end-state-inner {\n  fill: ${t.compositeBackground||t.background};\n  // stroke: ${t.background};\n  stroke-width: 1.5\n}\n\n.node rect {\n  fill: ${t.stateBkg||t.mainBkg};\n  stroke: ${t.stateBorder||t.nodeBorder};\n  stroke-width: 1px;\n}\n.node polygon {\n  fill: ${t.mainBkg};\n  stroke: ${t.stateBorder||t.nodeBorder};;\n  stroke-width: 1px;\n}\n#statediagram-barbEnd {\n  fill: ${t.lineColor};\n}\n\n.statediagram-cluster rect {\n  fill: ${t.compositeTitleBackground};\n  stroke: ${t.stateBorder||t.nodeBorder};\n  stroke-width: 1px;\n}\n\n.cluster-label, .nodeLabel {\n  color: ${t.stateLabelColor};\n  // line-height: 1;\n}\n\n.statediagram-cluster rect.outer {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state .divider {\n  stroke: ${t.stateBorder||t.nodeBorder};\n}\n\n.statediagram-state .title-state {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-cluster.statediagram-cluster .inner {\n  fill: ${t.compositeBackground||t.background};\n}\n.statediagram-cluster.statediagram-cluster-alt .inner {\n  fill: ${t.altBackground?t.altBackground:"#efefef"};\n}\n\n.statediagram-cluster .inner {\n  rx:0;\n  ry:0;\n}\n\n.statediagram-state rect.basic {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state rect.divider {\n  stroke-dasharray: 10,10;\n  fill: ${t.altBackground?t.altBackground:"#efefef"};\n}\n\n.note-edge {\n  stroke-dasharray: 5;\n}\n\n.statediagram-note rect {\n  fill: ${t.noteBkgColor};\n  stroke: ${t.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n.statediagram-note rect {\n  fill: ${t.noteBkgColor};\n  stroke: ${t.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n\n.statediagram-note text {\n  fill: ${t.noteTextColor};\n}\n\n.statediagram-note .nodeLabel {\n  color: ${t.noteTextColor};\n}\n.statediagram .edgeLabel {\n  color: red; // ${t.noteTextColor};\n}\n\n#dependencyStart, #dependencyEnd {\n  fill: ${t.lineColor};\n  stroke: ${t.lineColor};\n  stroke-width: 1;\n}\n\n.statediagramTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${t.textColor};\n}\n`),"getStyles");var St=mt},35645:(t,e,s)=>{s.d(e,{P:()=>r});var i=s(6047);var r=(0,i.K2)(((t,e,s,r)=>{t.attr("class",s);const{width:o,height:l,x:c,y:h}=n(t,e);(0,i.a$)(t,l,o,r);const d=a(c,h,o,l,e);t.attr("viewBox",d);i.Rm.debug(`viewBox configured: ${d} with padding: ${e}`)}),"setupViewPortForSVG");var n=(0,i.K2)(((t,e)=>{const s=t.node()?.getBBox()||{width:0,height:0,x:0,y:0};return{width:s.width+e*2,height:s.height+e*2,x:s.x,y:s.y}}),"calculateDimensionsWithPadding");var a=(0,i.K2)(((t,e,s,i,r)=>`${t-r} ${e-r} ${s} ${i}`),"createViewBox")},52480:(t,e,s)=>{s.d(e,{A:()=>n});var i=s(6047);var r=s(1218);var n=(0,i.K2)(((t,e)=>{let s;if(e==="sandbox"){s=(0,r.Ltv)("#i"+t)}const i=e==="sandbox"?(0,r.Ltv)(s.nodes()[0].contentDocument.body):(0,r.Ltv)("body");const n=i.select(`[id="${t}"]`);return n}),"getDiagramElement")}}]);