{"name": "@jupyter-widgets/jupyterlab-manager", "version": "5.0.15", "description": "The JupyterLab extension providing Jupyter widgets.", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab notebook", "jupyterlab-extension"], "homepage": "https://github.com/jupyter-widgets/ipywidgets", "bugs": {"url": "https://github.com/jupyter-widgets/ipywidgets/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyter-widgets/ipywidgets"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/*.css"], "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "dist/*.js", "schema/*.json"], "scripts": {"build": "jlpm build:lib && jlpm build:labextension:dev", "build:labextension": "jupyter labextension build .", "build:labextension:dev": "jupyter labextension build --development True .", "build:lib": "tsc -b", "build:prod": "jlpm build:lib && jlpm build:labextension", "clean": "jlpm clean:lib", "clean:all": "jlpm clean:lib && jlpm clean:labextension", "clean:labextension": "<PERSON><PERSON><PERSON> labextension", "clean:lib": "rimraf lib tsconfig.tsbuildinfo", "eslint": "eslint . --ext .ts,.tsx --fix", "eslint:check": "eslint . --ext .ts,.tsx", "install:extension": "jlpm build", "prepare": "jlpm clean && jlpm build:prod", "watch": "jupyter labextension watch ."}, "dependencies": {"@jupyter-widgets/base": "^6.0.11", "@jupyter-widgets/base-manager": "^1.0.12", "@jupyter-widgets/controls": "^5.0.12", "@jupyter-widgets/output": "^6.0.11", "@jupyterlab/application": "^3.0.0 || ^4.0.0", "@jupyterlab/apputils": "^3.0.0 || ^4.0.0", "@jupyterlab/console": "^3.0.0 || ^4.0.0", "@jupyterlab/docregistry": "^3.0.0 || ^4.0.0", "@jupyterlab/logconsole": "^3.0.0 || ^4.0.0", "@jupyterlab/mainmenu": "^3.0.0 || ^4.0.0", "@jupyterlab/nbformat": "^3.0.0 || ^4.0.0", "@jupyterlab/notebook": "^3.0.0 || ^4.0.0", "@jupyterlab/outputarea": "^3.0.0 || ^4.0.0", "@jupyterlab/rendermime": "^3.0.0 || ^4.0.0", "@jupyterlab/rendermime-interfaces": "^3.0.0 || ^4.0.0", "@jupyterlab/services": "^6.0.0 || ^7.0.0", "@jupyterlab/settingregistry": "^3.0.0 || ^4.0.0", "@jupyterlab/translation": "^3.0.0 || ^4.0.0", "@lumino/algorithm": "^1 || ^2", "@lumino/coreutils": "^1 || ^2", "@lumino/disposable": "^1 || ^2", "@lumino/signaling": "^1 || ^2", "@lumino/widgets": "^1 || ^2", "@types/backbone": "1.4.14", "jquery": "^3.1.1", "semver": "^7.3.5"}, "devDependencies": {"@jupyterlab/builder": "^3.0.0 || ^4.0.0", "@jupyterlab/cells": "^3.0.0 || ^4.0.0", "@types/jquery": "^3.5.16", "@types/semver": "^7.3.6", "@typescript-eslint/eslint-plugin": "^5.8.0", "@typescript-eslint/parser": "^5.8.0", "eslint": "^8.5.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "rimraf": "^3.0.2", "source-map-loader": "^4.0.1", "typescript": "~4.9.4"}, "jupyterlab": {"extension": true, "outputDir": "labextension", "schemaDir": "./schema", "_build": {"load": "static/remoteEntry.35b6c65bd99dab37b910.js", "extension": "./extension"}}, "gitHead": "efcf380707fd57050fc781e2ce991b557ec5ac0d"}